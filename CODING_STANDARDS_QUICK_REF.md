# Coding Standards Quick Reference

## 🎯 Key Principles
- **Type Safety First** - Use strict TypeScript, avoid `any` when possible
- **Consistency Over Preferences** - Follow established patterns
- **Modularity** - Single responsibility per module

## 📁 File Naming
| Type | Pattern | Example |
|------|---------|---------|
| Module | `kebab-case.module.ts` | `app-settings.module.ts` |
| Service | `kebab-case.service.ts` | `user-login.service.ts` |
| Controller | `kebab-case.controller.ts` | `auth.controller.ts` |
| DTO | `kebab-case.dto.ts` | `create-setting.dto.ts` |
| Entity | `kebab-case.entity.ts` | `user-login.entity.ts` |
| Interface | `kebab-case.interface.ts` | `encryption.interface.ts` |
| Enum | `kebab-case.enum.ts` | `user-role.enum.ts` |
| Config | `kebab-case.config.ts` | `auth.config.ts` |
| Error Codes | `kebab-case.error-codes.ts` | `auth.error-codes.ts` |

## 🏷️ Naming Conventions
- **Classes**: `PascalCase` with descriptive suffixes
  - `AppSettingsService`, `AuthController`, `LoginDto`, `UserEntity`
- **Variables/Methods**: `camelCase`
  - `getUserAgent()`, `ipAddress`, `findByKey()`
- **Constants**: `SCREAMING_SNAKE_CASE`
  - `AUTH_NAMESPACE`, `AUTH_ERROR_CODES`, `JWT_SECRET`
- **Interfaces**: 
  - Services: `IEncryptionService` (with `I` prefix)
  - Data: `CreateSetting`, `SettingFilter` (no prefix)

## 📂 Directory Structure
```
src/modules/{module-name}/
├── controllers/
├── services/
├── dto/
├── entities/
├── interfaces/
├── enums/
├── {module}.module.ts
├── {module}.config.ts
├── {module}.error-codes.ts
└── index.ts
```

## 📦 Import Order
```typescript
// 1. Node modules
import { Injectable, Logger } from '@nestjs/common';

// 2. Internal app imports (with aliases)
import { HttpErrorException } from '@common/exception/http-error.exception';

// 3. Local relative imports
import { AppSettingEntity } from '../entities/app-setting.entity';
```

## 🎨 Path Aliases
```typescript
// ✅ Use these
@app/*      // src/*
@common/*   // src/common/*
@config/*   // src/config/*
@modules/*  // src/modules/*
@utils/*    // src/utils/*

// ❌ Avoid deep relative paths
../../../common/exception/
```

## 🗃️ Entity Conventions
```typescript
@Entity('table_name')  // snake_case for table names
@Index(['foreign_key_id'])  // Manual index for foreign keys
@Index(['status', 'created_at'])  // Composite indexes for queries
export class EntityNameEntity extends CustomBaseEntity {
  @Column({ name: 'column_name' })  // snake_case for columns
  propertyName: string;  // camelCase for properties

  // Foreign Key Pattern (NO constraints, manual indexes)
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => UserEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
```

### Foreign Key Rules:
- ❌ **Never** use database foreign key constraints
- ✅ **Always** set `createForeignKeyConstraints: false`
- ✅ **Always** create manual `@Index(['foreign_key_id'])`
- ✅ Use composite indexes for query optimization

## 🔧 Service Pattern
```typescript
@Injectable()
export class ServiceName {
  private readonly logger = new Logger(ServiceName.name);

  constructor(
    @InjectRepository(Entity)
    private readonly repository: Repository<Entity>,
  ) {}

  async methodName(): Promise<ReturnType> {
    try {
      // Implementation
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }
      this.logger.error('Error message', error);
      throw new HttpErrorException(ERROR_CODES.DATABASE_ERROR);
    }
  }
}
```

## 🎮 Controller Pattern
```typescript
@ApiTags('Resource')
@Controller({ path: 'resource', version: '1' })
export class ResourceController {
  // Decorator Order (Top to Bottom):
  // 1. @ApiOperation
  // 2. @ApiParam
  // 3. @ApiQuery
  // 4. @ApiBody
  // 5. @ApiResponse
  // 6. Custom Decorators
  // 7. @UseGuards
  // 8. @HttpCode
  // 9. HTTP Method
  
  @ApiOperation({ summary: 'Description' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiQuery({ name: 'filter', required: false })
  @ApiBody({ type: RequestDto })
  @ApiResponse({ status: HttpStatus.OK, type: ResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Validation failed' })
  @Public()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @Post(':id/endpoint')
  async method(
    @Param('id') id: string,
    @Query('filter') filter: string,
    @Body() dto: RequestDto
  ): Promise<ResponseDto> {
    return this.service.method(dto);
  }
}
```

## 📋 DTO Pattern
```typescript
// DTO Decorator Order (Top to Bottom):
// 1. @ApiProperty
// 2. Validation (Input DTOs only)
// 3. Transform (if applicable)
// 4. @Expose

// Input DTO Example
export class CreateResourceDto {
  @ApiProperty({ 
    description: 'Field description with purpose',
    example: 'example_value',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @Transform(({ value }) => value?.trim())
  @Expose()
  field: string;

  @ApiProperty({ 
    description: 'Optional field description',
    example: 'optional_example',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  optionalField?: string;
}

// Response DTO Example
export class ResourceResponseDto {
  @ApiProperty({ 
    description: 'Resource identifier',
    example: 'uuid-here'
  })
  @Expose()
  id: string;

  @ApiProperty({ 
    description: 'Creation timestamp',
    example: '2023-12-01T10:30:00Z'
  })
  @Type(() => Date)
  @Expose()
  createdAt: Date;
}
```

## ❌ Error Handling
```typescript
const MODULE_NAMESPACE = 'MODULE';

type ModuleErrorCodes = 'RESOURCE_NOT_FOUND' | 'VALIDATION_FAILED';

export const MODULE_ERROR_CODES: Record<ModuleErrorCodes, ErrorCode> = {
  RESOURCE_NOT_FOUND: {
    code: `${MODULE_NAMESPACE}:10000`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Resource not found'
  },
  VALIDATION_FAILED: {
    code: `${MODULE_NAMESPACE}:10001`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Validation failed'
  }
};

// Usage
throw new HttpErrorException(
  MODULE_ERROR_CODES.RESOURCE_NOT_FOUND,
  { description: 'Additional context' }
);
```

## 📚 Documentation
```typescript
/**
 * Method description
 * @param param - Parameter description
 * @returns Promise<Type> - Return description
 * @throws HttpErrorException - When error occurs
 */
@ApiOperation({ 
  summary: 'API endpoint summary',
  description: 'Detailed endpoint description'
})
@ApiResponse({ status: 201, description: 'Success', type: ResponseDto })
@ApiResponse({ status: 400, description: 'Validation failed' })
@ApiResponse({ status: 409, description: 'Resource conflict' })
@ApiBody({ type: CreateDto, description: 'Request body description' })
async method(param: Type): Promise<ReturnType> {}
```

### DTO Documentation Requirements
```typescript
export class CreateResourceDto {
  @ApiProperty({ 
    description: 'Field description with purpose',
    example: 'example_value',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  field: string;

  @ApiProperty({ 
    description: 'Optional field description',
    example: 'optional_example',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  optionalField?: string;
}
```

## 🧪 Test Naming
- Unit tests: `{name}.spec.ts`
- E2E tests: `{name}.e2e-spec.ts`

## 📐 Formatting Rules
- Single quotes for strings
- Trailing commas everywhere
- 2 spaces indentation
- Line length: reasonable (no strict limit)

## ✅ Quick Checklist
- [ ] File named correctly with kebab-case
- [ ] Class uses PascalCase with appropriate suffix
- [ ] Methods use camelCase
- [ ] Imports organized (node modules → app imports → local imports)
- [ ] Path aliases used instead of relative paths
- [ ] Error handling with proper error codes
- [ ] JSDoc comments for public methods
- [ ] **Controller decorators in correct order**: @ApiOperation → @ApiParam → @ApiQuery → @ApiBody → @ApiResponse → Custom → @UseGuards → @HttpCode → HTTP Method
- [ ] **DTO decorators in correct order**: @ApiProperty → Validation → Transform → @Expose
- [ ] Swagger documentation for all DTOs (@ApiProperty with examples)
- [ ] All HTTP status codes documented (success + error cases)
- [ ] Request/Response body types specified
- [ ] Validation decorators on DTOs
- [ ] Logger initialized with class name
- [ ] Database columns use snake_case
- [ ] **Entity relations use createForeignKeyConstraints: false**
- [ ] **Manual @Index created for all foreign key columns**
- [ ] **Composite indexes for query optimization**
- [ ] Proper HTTP status codes
- [ ] @ApiTags for controller grouping
- [ ] @ApiBearerAuth for protected endpoints