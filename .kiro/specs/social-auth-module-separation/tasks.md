# Implementation Plan

- [x] 1. Create social auth module directory structure and interfaces

  - Create `src/modules/social-auth/` directory with subdirectories (controllers, services, strategies, entities, interfaces, enums, guards)
  - Define `SocialAuthModuleOptions` interface with provider configurations
  - Create `SocialAuthConfigType` interface for configuration typing
  - Define provider-specific config interfaces (GoogleProviderConfig, FacebookProviderConfig, MicrosoftProviderConfig)
  - _Requirements: 1.1, 2.1, 5.1_

- [x] 2. Create social auth configuration system

  - Create `src/modules/social-auth/social-auth.config.ts` with configuration registration
  - Implement environment variable validation for social providers
  - Add configuration validation logic for enabled providers
  - Create configuration factory functions for dynamic module setup
  - _Requirements: 2.2, 5.2, 5.3_

- [x] 3. Move and refactor social entity to social auth module

  - Move `SocialEntity` from `src/modules/auth/entities/social.entity.ts` to `src/modules/social-auth/entities/social.entity.ts`
  - Update entity imports and ensure database compatibility
  - Move `SocialProviderEnum` to `src/modules/social-auth/enums/social-provider.enum.ts`
  - Update all references to the moved entity and enum
  - _Requirements: 3.2, 4.1_

- [x] 4. Create dynamic social auth module

  - Create `src/modules/social-auth/social-auth.module.ts` as a dynamic module
  - Implement `forRoot()` method for synchronous configuration
  - Implement `forRootAsync()` method for asynchronous configuration
  - Add conditional provider registration based on configuration
  - Set up TypeORM feature registration for SocialEntity
  - _Requirements: 1.2, 2.1, 2.2_

- [x] 5. Move and refactor social strategies

  - Move `FacebookStrategy` from auth module to `src/modules/social-auth/strategies/facebook.strategy.ts`
  - Move `GoogleStrategy` from auth module to `src/modules/social-auth/strategies/google.strategy.ts`
  - Move `MicrosoftStrategy` from auth module to `src/modules/social-auth/strategies/microsoft.strategy.ts`
  - Update strategy imports and configuration references
  - _Requirements: 1.2, 2.2, 4.1_

- [x] 6. Move and refactor social auth guards

  - Move `FacebookAuthGuard` to `src/modules/social-auth/guards/facebook-auth.guard.ts`
  - Move `GoogleAuthGuard` to `src/modules/social-auth/guards/google-auth.guard.ts`
  - Move `MicrosoftAuthGuard` to `src/modules/social-auth/guards/microsoft-auth.guard.ts`
  - Update guard imports and references
  - _Requirements: 1.2, 2.2_

- [x] 7. Create social auth service

  - Create `src/modules/social-auth/services/social-auth.service.ts`
  - Move social user creation logic from existing SocialService
  - Implement integration with core AuthService for token generation
  - Add methods for social user linking and OAuth callback handling
  - Ensure compatibility with existing user and session management
  - _Requirements: 3.2, 3.3, 3.4_

- [x] 8. Create social auth controller

  - Create `src/modules/social-auth/controllers/social-auth.controller.ts`
  - Move social login endpoints from existing SocialController
  - Update route paths to use `/social-auth` prefix
  - Implement provider-specific login and redirect endpoints
  - Add OAuth callback handling endpoint
  - _Requirements: 1.1, 3.2, 3.3_

- [x] 9. Update core auth module to remove social dependencies

  - Remove social-related imports from `src/modules/auth/auth.module.ts`
  - Remove SocialController, SocialService, and social strategies from providers
  - Remove SocialEntity from TypeORM feature registration
  - Update exports to exclude social-related services
  - _Requirements: 3.1, 4.1_

- [x] 10. Extract social configuration from core auth config

  - Remove social provider configurations from `src/modules/auth/auth.config.ts`
  - Remove social-related environment variable validators
  - Update AuthConfigType to exclude social provider types
  - Ensure core auth functionality remains intact
  - _Requirements: 3.1, 4.1_

- [x] 11. Implement conditional provider registration in dynamic module

  - Add logic to register strategies only for enabled providers
  - Implement provider validation during module initialization
  - Add error handling for missing provider configurations
  - Create provider factory functions for conditional instantiation
  - _Requirements: 2.2, 2.3, 5.3_

- [x] 12. Add comprehensive error handling and validation

  - Implement configuration validation with descriptive error messages
  - Add runtime error handling for OAuth provider failures
  - Create custom exception classes for social auth specific errors
  - Add dependency validation and clear error messages for missing dependencies
  - _Requirements: 4.2, 5.3_

- [x] 13. Create example usage and integration documentation
  - Create example of how to import and configure the social auth module
  - Document provider-specific configuration requirements
  - Provide examples of conditional provider enabling
  - Create migration guide for existing applications using social auth
  - _Requirements: 1.1, 2.1, 5.1_
