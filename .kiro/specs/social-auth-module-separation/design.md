# Design Document

## Overview

This design outlines the separation of social authentication functionality from the main auth module into a standalone, optional, and dynamic module. The new `SocialAuthModule` will be completely decoupled from the core authentication system while maintaining seamless integration when enabled.

The design follows NestJS dynamic module patterns, allowing runtime configuration of social providers and optional loading of social authentication features. This approach reduces bundle size when social auth is not needed and improves modularity.

## Architecture

### Current State Analysis

The current auth module (`src/modules/auth/auth.module.ts`) contains:
- Core authentication (JWT, local strategy, user login)
- Social authentication (Facebook, Google, Microsoft strategies)
- Social controller and service
- Social entity and related database models
- Mixed configuration in `auth.config.ts`

### Target Architecture

```
src/modules/
├── auth/                          # Core authentication module
│   ├── controllers/
│   │   └── auth.controller.ts     # Core auth endpoints only
│   ├── services/
│   │   ├── auth.service.ts        # Core auth logic only
│   │   └── user-login.service.ts  # Unchanged
│   ├── strategies/
│   │   ├── jwt.strategy.ts        # Unchanged
│   │   └── local.strategy.ts      # Unchanged
│   ├── entities/
│   │   ├── session.entity.ts      # Unchanged
│   │   ├── otp.entity.ts          # Unchanged
│   │   └── user-login.entity.ts   # Unchanged
│   ├── auth.config.ts             # Core auth config only
│   └── auth.module.ts             # Core auth module only
│
└── social-auth/                   # New social authentication module
    ├── controllers/
    │   └── social-auth.controller.ts
    ├── services/
    │   └── social-auth.service.ts
    ├── strategies/
    │   ├── facebook.strategy.ts
    │   ├── google.strategy.ts
    │   └── microsoft.strategy.ts
    ├── entities/
    │   └── social.entity.ts
    ├── interfaces/
    │   └── social-auth-config.interface.ts
    ├── enums/
    │   └── social-provider.enum.ts
    ├── guards/
    │   ├── facebook-auth.guard.ts
    │   ├── google-auth.guard.ts
    │   └── microsoft-auth.guard.ts
    ├── social-auth.config.ts
    └── social-auth.module.ts      # Dynamic module
```

## Components and Interfaces

### 1. SocialAuthModule (Dynamic Module)

```typescript
interface SocialAuthModuleOptions {
  providers: {
    google?: GoogleProviderConfig;
    facebook?: FacebookProviderConfig;
    microsoft?: MicrosoftProviderConfig;
  };
  enabledProviders?: SocialProviderEnum[];
}

interface GoogleProviderConfig {
  clientId: string;
  clientSecret: string;
  callbackURL: string;
}

interface FacebookProviderConfig {
  clientId: string;
  clientSecret: string;
  redirectUrl: string;
}

interface MicrosoftProviderConfig {
  clientId: string;
  clientSecret: string;
  callbackURL: string;
  tenant: string;
}
```

The module will implement both `forRoot()` and `forRootAsync()` methods for synchronous and asynchronous configuration.

### 2. SocialAuthController

Extracted from the current `SocialController`, this will handle:
- `/social-auth/google/login` and `/social-auth/google/redirect`
- `/social-auth/facebook/login` and `/social-auth/facebook/redirect`
- `/social-auth/microsoft/login` and `/social-auth/microsoft/redirect`
- `/social-auth/login/code` for OAuth callback handling

### 3. SocialAuthService

Extracted from the current `SocialService`, responsible for:
- Processing social provider payloads
- Creating or linking social accounts to existing users
- Integrating with core auth service for token generation
- Managing social entity persistence

### 4. Strategy Classes

Each social provider strategy will be conditionally registered based on configuration:
- `FacebookStrategy` - only if Facebook config is provided
- `GoogleStrategy` - only if Google config is provided  
- `MicrosoftStrategy` - only if Microsoft config is provided

### 5. Configuration Integration

The social auth module will have its own configuration system that integrates with the existing auth configuration when needed.

```typescript
// social-auth.config.ts
export interface SocialAuthConfigType {
  google?: GoogleProviderConfig;
  facebook?: FacebookProviderConfig;
  microsoft?: MicrosoftProviderConfig;
}
```

## Data Models

### Social Entity Migration

The `SocialEntity` will be moved to the social auth module but remain compatible with existing database schemas. No database migration is required.

### Integration Points

The social auth module will depend on:
- Core `AuthService` for token generation and user session management
- `UserService` for user creation and retrieval
- `UserEntity` for user relationships

## Error Handling

### Configuration Validation

- Validate provider configurations at module initialization
- Throw descriptive errors for missing required configuration
- Provide clear error messages for invalid provider setups

### Runtime Error Handling

- Handle OAuth provider errors gracefully
- Maintain existing error codes and messages for backward compatibility
- Add new error codes specific to social authentication failures

### Dependency Errors

- Clear error messages when social auth module is used without required dependencies
- Graceful degradation when optional peer dependencies are missing

## Testing Strategy

### Unit Tests

1. **SocialAuthModule Tests**
   - Test dynamic module configuration
   - Test conditional provider registration
   - Test error handling for invalid configurations

2. **SocialAuthService Tests**
   - Test social user creation and linking
   - Test integration with core auth service
   - Test error scenarios for each provider

3. **Strategy Tests**
   - Test each social provider strategy independently
   - Test OAuth flow validation
   - Test profile data extraction and transformation

4. **Controller Tests**
   - Test social auth endpoints
   - Test redirect handling
   - Test error responses

### Integration Tests

1. **Module Integration**
   - Test social auth module integration with core auth module
   - Test database entity relationships
   - Test JWT token generation flow

2. **Provider Integration**
   - Test OAuth flows with mock providers
   - Test callback handling
   - Test user session creation

### Migration Tests

1. **Backward Compatibility**
   - Test existing social auth functionality continues to work
   - Test database compatibility
   - Test API endpoint compatibility

## Implementation Phases

### Phase 1: Module Structure Setup
- Create new social-auth module directory structure
- Set up basic module configuration interfaces
- Create placeholder services and controllers

### Phase 2: Entity and Configuration Migration
- Move SocialEntity to social-auth module
- Extract social configuration from auth.config.ts
- Set up dynamic module configuration system

### Phase 3: Service and Controller Migration
- Move and refactor SocialService to SocialAuthService
- Move and refactor SocialController to SocialAuthController
- Update import paths and dependencies

### Phase 4: Strategy Migration
- Move social strategies to social-auth module
- Update strategy registration to be conditional
- Update guard classes

### Phase 5: Core Auth Module Cleanup
- Remove social dependencies from core auth module
- Update auth.module.ts to exclude social components
- Clean up auth.config.ts

### Phase 6: Integration and Testing
- Ensure seamless integration between modules
- Add comprehensive test coverage
- Validate backward compatibility

### Phase 7: Documentation and Examples
- Create usage examples for the new module
- Update existing documentation
- Provide migration guide for existing applications