# Design Document

## Overview

The simplified tracking module will focus on core user behavior tracking functionality, removing complex analytics, reporting, and queue processing features. The design emphasizes simplicity, performance, and ease of maintenance while providing essential tracking capabilities for mobile and web applications.

## Architecture

### Core Components

1. **TrackingModule** - Main module with minimal dependencies
2. **TrackingController** - Simple REST API for event collection
3. **TrackingService** - Core business logic for event processing
4. **SessionService** - Basic session management
5. **TrackingEventEntity** - Simplified event storage
6. **UserSessionEntity** - Basic session tracking

### Removed Components

- All analytics controllers (marketplace, seller, dashboard, etc.)
- Complex queue processors (BullMQ integration)
- Advanced analytics services
- Real-time analytics gateway
- Data retention and archiving services
- Complex aggregation processors

## Components and Interfaces

### TrackingController

Simplified REST API with essential endpoints:

```typescript
@Controller('api/tracking')
export class TrackingController {
  // POST /api/tracking/events - Track single event
  trackEvent(eventData: TrackingEventDto): Promise<{success: boolean, eventId: string}>
  
  // POST /api/tracking/batch - Track multiple events
  trackBatchEvents(batchData: BatchTrackingDto): Promise<{success: boolean, count: number}>
  
  // POST /api/tracking/pageview - Track page view
  trackPageView(pageData: PageViewDto): Promise<{success: boolean, eventId: string}>
  
  // POST /api/tracking/session - Create session
  createSession(sessionData: SessionDto): Promise<SessionResponseDto>
  
  // POST /api/tracking/session/:id/end - End session
  endSession(sessionId: string): Promise<{success: boolean}>
}
```

### TrackingService

Core service with simplified functionality:

```typescript
@Injectable()
export class TrackingService {
  // Process and store single event
  trackEvent(eventData: TrackingEventDto): Promise<TrackingEventEntity>
  
  // Process batch of events
  trackBatchEvents(events: TrackingEventDto[]): Promise<TrackingEventEntity[]>
  
  // Validate and enrich event data
  private validateAndEnrichEvent(eventData: TrackingEventDto): TrackingEventDto
  
  // Generate anonymous ID if needed
  private generateAnonymousId(): string
}
```

### SessionService

Basic session management:

```typescript
@Injectable()
export class SessionService {
  // Create new session
  createSession(sessionData: SessionDto): Promise<UserSessionEntity>
  
  // End existing session
  endSession(sessionId: string): Promise<UserSessionEntity>
  
  // Get active session
  getSession(sessionId: string): Promise<UserSessionEntity>
  
  // Update session activity
  updateSessionActivity(sessionId: string): Promise<void>
}
```

## Data Models

### TrackingEventEntity (Simplified)

```typescript
@Entity('tracking_events')
export class TrackingEventEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  eventType: string; // PAGE_VIEW, CLICK, CUSTOM
  
  @Column({ type: 'uuid', nullable: true })
  userId?: string;
  
  @Column({ type: 'varchar', nullable: true })
  anonymousId?: string;
  
  @Column({ type: 'varchar', nullable: true })
  sessionId?: string;
  
  @Column({ type: 'varchar', nullable: true })
  pageUrl?: string;
  
  @Column({ type: 'varchar', nullable: true })
  referrer?: string;
  
  @Column({ type: 'varchar', nullable: true })
  userAgent?: string;
  
  @Column({ type: 'varchar', nullable: true })
  deviceType?: string; // mobile, desktop, tablet
  
  @Column({ type: 'jsonb', nullable: true })
  properties?: Record<string, any>;
  
  @Column({ type: 'timestamp' })
  timestamp: Date;
  
  @CreateDateColumn()
  createdAt: Date;
}
```

### UserSessionEntity (Simplified)

```typescript
@Entity('user_sessions')
export class UserSessionEntity extends BaseEntity {
  @Column({ type: 'varchar', unique: true })
  sessionId: string;
  
  @Column({ type: 'uuid', nullable: true })
  userId?: string;
  
  @Column({ type: 'varchar', nullable: true })
  anonymousId?: string;
  
  @Column({ type: 'varchar', nullable: true })
  userAgent?: string;
  
  @Column({ type: 'varchar', nullable: true })
  deviceType?: string;
  
  @Column({ type: 'timestamp' })
  startTime: Date;
  
  @Column({ type: 'timestamp', nullable: true })
  endTime?: Date;
  
  @Column({ type: 'timestamp' })
  lastActivity: Date;
  
  @Column({ type: 'integer', default: 0 })
  pageViews: number;
  
  @Column({ type: 'jsonb', nullable: true })
  properties?: Record<string, any>;
  
  @CreateDateColumn()
  createdAt: Date;
  
  @UpdateDateColumn()
  updatedAt: Date;
}
```

### DTOs

```typescript
export class TrackingEventDto {
  eventType: string;
  userId?: string;
  anonymousId?: string;
  sessionId?: string;
  pageUrl?: string;
  referrer?: string;
  userAgent?: string;
  deviceType?: string;
  properties?: Record<string, any>;
  timestamp?: Date;
}

export class BatchTrackingDto {
  events: TrackingEventDto[];
}

export class PageViewDto extends TrackingEventDto {
  eventType: 'PAGE_VIEW';
  pageUrl: string;
}

export class SessionDto {
  userId?: string;
  anonymousId?: string;
  userAgent?: string;
  deviceType?: string;
  properties?: Record<string, any>;
}
```

## Error Handling

### Validation Errors
- Invalid event type
- Missing required fields
- Invalid data formats
- Oversized payloads

### Processing Errors
- Database connection issues
- Constraint violations
- Service unavailability

### Error Response Format
```typescript
{
  success: false,
  error: {
    code: 'VALIDATION_ERROR',
    message: 'Invalid event data',
    details: ['eventType is required']
  }
}
```

## Testing Strategy

### Unit Tests
- TrackingService event processing logic
- SessionService session management
- DTO validation
- Error handling scenarios

### Integration Tests
- Controller endpoint functionality
- Database operations
- Event enrichment process
- Session lifecycle

### Performance Tests
- High-volume event processing
- Batch processing efficiency
- Database query performance
- Memory usage under load

## Database Optimizations

### Indexes
```sql
-- Tracking events indexes
CREATE INDEX idx_tracking_events_event_type ON tracking_events(event_type);
CREATE INDEX idx_tracking_events_user_id ON tracking_events(user_id);
CREATE INDEX idx_tracking_events_session_id ON tracking_events(session_id);
CREATE INDEX idx_tracking_events_timestamp ON tracking_events(timestamp);
CREATE INDEX idx_tracking_events_anonymous_id ON tracking_events(anonymous_id);

-- User sessions indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_start_time ON user_sessions(start_time);
```

### Partitioning Strategy
- Consider partitioning tracking_events by timestamp for large datasets
- Archive old data based on retention policies

## Configuration

### Environment Variables
```typescript
export interface TrackingConfig {
  // Rate limiting
  TRACKING_RATE_LIMIT: number; // requests per minute
  TRACKING_BATCH_LIMIT: number; // max events per batch
  
  // Session management
  SESSION_TIMEOUT: number; // minutes
  SESSION_CLEANUP_INTERVAL: number; // minutes
  
  // Data retention
  EVENT_RETENTION_DAYS: number;
  SESSION_RETENTION_DAYS: number;
}
```

### Default Configuration
```typescript
export const defaultTrackingConfig: TrackingConfig = {
  TRACKING_RATE_LIMIT: 100,
  TRACKING_BATCH_LIMIT: 50,
  SESSION_TIMEOUT: 30,
  SESSION_CLEANUP_INTERVAL: 60,
  EVENT_RETENTION_DAYS: 90,
  SESSION_RETENTION_DAYS: 30,
};
```

## Migration Strategy

### Phase 1: Remove Complex Components
1. Remove analytics controllers and services
2. Remove queue processors and BullMQ dependencies
3. Remove real-time analytics gateway
4. Clean up unused entities and DTOs

### Phase 2: Simplify Core Components
1. Simplify TrackingService by removing complex features
2. Streamline TrackingController endpoints
3. Update entity relationships
4. Remove unused dependencies from module

### Phase 3: Optimize and Test
1. Add database indexes
2. Implement basic rate limiting
3. Add comprehensive tests
4. Performance optimization

## Security Considerations

### Data Privacy
- Hash IP addresses before storage
- Implement data anonymization for GDPR compliance
- Provide data deletion capabilities

### Rate Limiting
- Implement per-IP rate limiting
- Different limits for authenticated vs anonymous users
- Batch request size limits

### Input Validation
- Sanitize all input data
- Validate event types against whitelist
- Limit property object size and depth