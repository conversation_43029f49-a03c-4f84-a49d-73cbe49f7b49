# Design Document

## Overview

This design converts the individual alert provider modules (DiscordAlertModule, EmailAlertModule, SlackAlertModule, TelegramAlertModule) from static modules to dynamic modules. Each provider module will support `forRoot()` methods that accept specific configuration options, allowing the AlertModule to pass targeted configuration to each provider instead of relying on global ConfigService.

## Architecture

The solution transforms each static provider module into a dynamic module that can accept configuration through dependency injection. The key architectural changes include:

1. **Dynamic Module Pattern**: Each provider module implements `forRoot(config)` static method
2. **Configuration Injection**: Provider-specific configuration is injected via custom provider tokens
3. **Fallback Mechanism**: Maintains backward compatibility by falling back to ConfigService when no config is injected
4. **Type Safety**: Each provider has its own configuration interface extracted from AlertOptions

## Components and Interfaces

### Configuration Interfaces

Each provider will have its own configuration interface extracted from the existing AlertOptions:

```typescript
// Discord Provider Configuration
export interface DiscordAlertConfig {
  enabled: boolean;
  webhookUrl: string;
}

// Email Provider Configuration  
export interface EmailAlertConfig {
  enabled: boolean;
  templateDir?: string;
  templateName?: string;
  adminEmail?: string;
}

// Slack Provider Configuration
export interface SlackAlertConfig {
  enabled: boolean;
  channel: string;
  token: string;
}

// Telegram Provider Configuration
export interface TelegramAlertConfig {
  enabled: boolean;
  token: string;
  chatId: string;
}
```

### Provider Tokens

Each provider module will use a unique injection token for its configuration:

```typescript
export const DISCORD_ALERT_CONFIG = 'DISCORD_ALERT_CONFIG';
export const EMAIL_ALERT_CONFIG = 'EMAIL_ALERT_CONFIG';
export const SLACK_ALERT_CONFIG = 'SLACK_ALERT_CONFIG';
export const TELEGRAM_ALERT_CONFIG = 'TELEGRAM_ALERT_CONFIG';
```

### Dynamic Module Implementation

Each provider module will implement the following pattern:

```typescript
@Module({})
export class DiscordAlertModule {
  static forRoot(config: DiscordAlertConfig): DynamicModule {
    return {
      module: DiscordAlertModule,
      providers: [
        {
          provide: DISCORD_ALERT_CONFIG,
          useValue: config,
        },
        DiscordAlertService,
      ],
      exports: [DiscordAlertService],
    };
  }
}
```

### Service Configuration Injection

Each service will be updated to accept configuration via dependency injection with fallback to ConfigService:

```typescript
@Injectable()
export class DiscordAlertService {
  private readonly config: DiscordAlertConfig;

  constructor(
    @Optional() @Inject(DISCORD_ALERT_CONFIG) injectedConfig: DiscordAlertConfig,
    private configService: ConfigService<{ alert: AlertConfigType }>,
  ) {
    // Use injected config if available, otherwise fall back to ConfigService
    this.config = injectedConfig || this.getConfigFromService();
  }

  private getConfigFromService(): DiscordAlertConfig {
    const discordConfig = this.configService.get('alert.discord', { infer: true });
    return {
      enabled: discordConfig?.enabled || false,
      webhookUrl: discordConfig?.webhookUrl || '',
    };
  }
}
```

## Data Models

### Updated AlertModule Integration

The AlertModule's `getImports` method will be updated to pass specific configuration to each provider:

```typescript
private static getImports(options: AlertOptions): Array<any> {
  const imports: Array<any> = [];

  if (options.discord?.enabled) {
    imports.push(DiscordAlertModule.forRoot(options.discord));
  }

  if (options.email?.enabled) {
    imports.push(EmailAlertModule.forRoot(options.email));
  }

  if (options.slack?.enabled) {
    imports.push(SlackAlertModule.forRoot(options.slack));
  }

  if (options.telegram?.enabled) {
    imports.push(TelegramAlertModule.forRoot(options.telegram));
  }

  return imports;
}
```

### Configuration Mapping

The AlertModule will need to map its configuration options to provider-specific configurations, handling any differences between the AlertOptions interface and provider-specific interfaces.

## Error Handling

### Configuration Validation

Each provider module will validate its configuration and provide clear error messages for invalid options:

```typescript
static forRoot(config: DiscordAlertConfig): DynamicModule {
  if (config.enabled && !config.webhookUrl) {
    throw new Error('Discord webhook URL is required when Discord alerts are enabled');
  }
  // ... rest of implementation
}
```

### Fallback Behavior

Services will gracefully handle missing configuration by falling back to ConfigService behavior, ensuring backward compatibility.

## Testing Strategy

### Unit Tests

- Test that each provider module's `forRoot()` method returns correct DynamicModule configuration
- Test that services receive injected configuration correctly
- Test fallback behavior when no configuration is injected
- Test configuration validation and error handling
- Test that AlertModule passes correct configuration to each provider

### Integration Tests

- Test that AlertModule with dynamic provider configuration works end-to-end
- Test that services use injected configuration instead of ConfigService when available
- Test backward compatibility with existing static module imports
- Test that disabled providers are not imported

### Migration Tests

- Test that existing implementations continue to work without changes
- Test that mixed usage (some providers with injected config, others with ConfigService) works correctly

## Implementation Approach

The implementation will follow this sequence:

1. **Create Configuration Interfaces**: Define provider-specific configuration interfaces
2. **Create Provider Tokens**: Define injection tokens for each provider
3. **Update Provider Modules**: Convert each module to support `forRoot()` method
4. **Update Provider Services**: Modify services to accept injected configuration with ConfigService fallback
5. **Update AlertModule**: Modify AlertModule to use dynamic provider imports
6. **Add Configuration Validation**: Implement validation for each provider's configuration
7. **Update Documentation**: Add examples and documentation for the new dynamic configuration

### Backward Compatibility Strategy

To ensure backward compatibility:

1. **Optional Injection**: Use `@Optional()` decorator for injected configuration
2. **ConfigService Fallback**: Fall back to existing ConfigService behavior when no config is injected
3. **Static Module Support**: Continue to support static module imports alongside dynamic imports
4. **Gradual Migration**: Allow mixed usage during migration period

This approach ensures that existing code continues to work while providing the new dynamic configuration capabilities.