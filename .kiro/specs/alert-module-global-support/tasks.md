# Implementation Plan

- [x] 1. Add forRoot static method to AlertModule

  - Implement forRoot method that returns DynamicModule with global: true
  - Reuse existing getImports private method for conditional imports
  - Add comprehensive JSDoc documentation
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2. Add forRootAsync static method to AlertModule

  - Implement forRootAsync method that returns DynamicModule with global: true
  - Reuse existing createAsyncProviders private method
  - Import all alert modules since enabled status is determined at runtime
  - Add comprehensive JSDoc documentation
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 3. Update documentation with global registration examples

  - Add JSDoc examples showing how to use forRoot in app.module.ts
  - Add JSDoc examples showing how to use forRootAsync in app.module.ts
  - Document the difference between register/registerAsync and forRoot/forRootAsync
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 4. Create usage example file for global registration
  - Create example showing forRoot usage in a sample application
  - Create example showing forRootAsync usage with ConfigService
  - Show how to inject AlertService in other modules without importing AlertModule
  - Demonstrate the global availability of the service
  - _Requirements: 4.1, 4.2, 4.3, 4.4_
