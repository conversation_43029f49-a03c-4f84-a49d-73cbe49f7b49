# Design Document

## Overview

This design implements global module support for the AlertModule by adding `forRoot` and `forRootAsync` static methods. The implementation follows NestJS conventions for global modules, making the AlertService available application-wide without requiring imports in individual modules.

## Architecture

The solution extends the existing AlertModule with two new static methods while maintaining full backward compatibility:

- `forRoot(options: AlertOptions)` - Synchronous global registration
- `forRootAsync(options: AlertAsyncOptions)` - Asynchronous global registration

The key architectural change is setting `global: true` in the returned DynamicModule, which instructs NestJS to make the module's exports available globally.

## Components and Interfaces

### AlertModule Extensions

The AlertModule will be extended with two new static methods:

```typescript
static forRoot(options: AlertOptions): DynamicModule
static forRootAsync(options: AlertAsyncOptions): DynamicModule
```

Both methods return a DynamicModule with `global: true`, making the AlertService available globally.

### Method Implementations

#### forRoot Method
- Takes the same `AlertOptions` as the existing `register` method
- Uses the existing `getImports` private method to conditionally import only enabled alert provider modules
- Returns a DynamicModule with `global: true`
- Reuses existing provider creation logic

#### forRootAsync Method
- Takes the same `AlertAsyncOptions` as the existing `registerAsync` method
- Imports all alert provider modules (same as registerAsync) since enabled status is determined at runtime
- Uses the existing `createAsyncProviders` private method
- Returns a DynamicModule with `global: true`

### Backward Compatibility

The existing `register` and `registerAsync` methods remain unchanged, ensuring full backward compatibility. The only difference between the existing methods and the new forRoot methods is the `global: true` flag.

## Data Models

No changes to existing data models are required. The AlertOptions and AlertAsyncOptions interfaces remain unchanged.

## Error Handling

Error handling remains the same as the existing implementation. The AlertService will continue to handle missing or disabled alert providers gracefully using the `@Optional()` decorator for dependency injection.

## Testing Strategy

### Unit Tests
- Test that forRoot returns a DynamicModule with global: true
- Test that forRootAsync returns a DynamicModule with global: true
- Test that the correct imports are included based on enabled options (forRoot)
- Test that all imports are included for forRootAsync
- Test that existing register/registerAsync methods continue to work unchanged

### Integration Tests
- Test that AlertService is available globally when using forRoot
- Test that AlertService works correctly with async configuration using forRootAsync
- Test that multiple modules can inject AlertService without importing AlertModule
- Test that the service maintains singleton behavior across the application

### Implementation Approach

The implementation will:

1. Add the two new static methods to AlertModule
2. Reuse existing private helper methods (`getImports`, `createAsyncProviders`)
3. Add comprehensive JSDoc documentation
4. Maintain the exact same functionality as existing methods, only adding global scope

This approach minimizes code duplication and ensures consistency between the local and global registration methods.