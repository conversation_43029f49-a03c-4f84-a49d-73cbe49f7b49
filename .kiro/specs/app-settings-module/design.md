# Design Document

## Overview

The app settings module provides a comprehensive configuration management system for the NestJS application. It supports both system-protected settings and user-manageable custom settings, with built-in encryption capabilities for sensitive values. The module follows the existing architectural patterns established in the codebase, including TypeORM entities, service-repository pattern, and RESTful API design.

## Architecture

The module follows a layered architecture consistent with the existing codebase:

- **Controller Layer**: RESTful API endpoints for settings management
- **Service Layer**: Business logic for settings operations, validation, and encryption
- **Repository Layer**: Data access through TypeORM repositories
- **Entity Layer**: Database models with proper relationships and constraints
- **DTO Layer**: Data transfer objects for API requests and responses

## Components and Interfaces

### Core Entities

#### AppSettingEntity
```typescript
@Entity('app_settings')
export class AppSettingEntity extends CustomBaseEntity {
  @Column({ unique: true })
  key: string;

  @Column({ type: 'text' })
  value: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  category?: string;

  @Column({ default: false })
  isSystem: boolean;

  @Column({ default: false })
  isEncrypted: boolean;

  @Column({ type: 'json', nullable: true })
  validationSchema?: object;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  updatedBy?: string;
}
```

#### SettingAuditEntity
```typescript
@Entity('setting_audits')
export class SettingAuditEntity extends CustomBaseEntity {
  @Column()
  settingKey: string;

  @Column()
  action: 'CREATE' | 'UPDATE' | 'DELETE';

  @Column({ type: 'text', nullable: true })
  oldValue?: string;

  @Column({ type: 'text', nullable: true })
  newValue?: string;

  @Column()
  userId: string;

  @Column({ type: 'json', nullable: true })
  metadata?: object;
}
```

### Service Architecture

#### AppSettingsService
Primary service handling all settings operations:
- CRUD operations with system/custom setting differentiation
- Encryption/decryption of sensitive values
- Validation against schemas
- Audit logging

#### EncryptionService
Dedicated service for handling encryption operations:
- AES-256-GCM encryption for setting values
- Key derivation and management
- Secure encryption/decryption methods

#### SettingValidationService
Service for validating setting values:
- JSON schema validation
- Type checking and constraints
- Custom validation rules

### Controller Design

#### AppSettingsController
RESTful endpoints following existing patterns:
- `GET /settings` - Retrieve all settings (with filtering)
- `GET /settings/:key` - Retrieve specific setting
- `POST /settings` - Create new custom setting
- `PUT /settings/:key` - Update existing setting
- `DELETE /settings/:key` - Delete custom setting (reject system settings)
- `GET /settings/audit/:key` - Retrieve audit history

## Data Models

### Setting Value Structure
```typescript
interface SettingValue {
  key: string;
  value: any;
  description?: string;
  category?: string;
  isSystem: boolean;
  isEncrypted: boolean;
  validationSchema?: object;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
  };
}
```

### Encryption Schema
```typescript
interface EncryptedValue {
  encrypted: string;
  iv: string;
  tag: string;
  algorithm: 'aes-256-gcm';
}
```

### Validation Schema Format
Using JSON Schema format for validation:
```typescript
interface ValidationSchema {
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  enum?: any[];
  properties?: Record<string, ValidationSchema>;
}
```

## Error Handling

### Custom Error Codes
Following the existing error code pattern:
```typescript
export const APP_SETTINGS_ERROR_CODES = {
  SETTING_NOT_FOUND: 'SETTING_NOT_FOUND',
  SYSTEM_SETTING_DELETE_FORBIDDEN: 'SYSTEM_SETTING_DELETE_FORBIDDEN',
  SETTING_KEY_ALREADY_EXISTS: 'SETTING_KEY_ALREADY_EXISTS',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
  DECRYPTION_FAILED: 'DECRYPTION_FAILED',
  INVALID_SETTING_KEY: 'INVALID_SETTING_KEY',
} as const;
```

### Error Response Format
Consistent with existing HttpErrorException pattern:
```typescript
throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.SETTING_NOT_FOUND, {
  description: `Setting with key '${key}' not found`,
  context: { key }
});
```

## Security Considerations

### Encryption Implementation
- Use AES-256-GCM for symmetric encryption
- Generate unique IV for each encrypted value
- Store encryption key securely via environment variables
- Implement key rotation capabilities

### Access Control
- System settings can only be modified by admin users
- Custom settings are user-scoped or global based on permissions
- Audit all setting modifications
- Validate user permissions before operations

### Data Protection
- Encrypt sensitive configuration values
- Mask encrypted values in logs
- Implement secure key management
- Regular security audits of stored settings

## Testing Strategy

### Unit Tests
- Service layer testing with mocked repositories
- Encryption/decryption functionality testing
- Validation logic testing
- Error handling scenarios

### Integration Tests
- Controller endpoint testing
- Database operations testing
- End-to-end setting management workflows
- Encryption/decryption integration

### Test Data Management
- Factory pattern for test data creation
- Separate test database configuration
- Mock encryption keys for testing
- Automated test data cleanup

### Performance Testing
- Load testing for bulk setting operations
- Encryption/decryption performance benchmarks
- Database query optimization validation
- Memory usage monitoring

## Configuration

### Environment Variables
```typescript
interface AppSettingsConfig {
  encryption: {
    key: string;
    algorithm: 'aes-256-gcm';
  };
  audit: {
    enabled: boolean;
    retentionDays: number;
  };
  validation: {
    enabled: boolean;
    strictMode: boolean;
  };
}
```

### Module Configuration
```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([AppSettingEntity, SettingAuditEntity]),
    ConfigModule,
  ],
  controllers: [AppSettingsController],
  providers: [
    AppSettingsService,
    EncryptionService,
    SettingValidationService,
  ],
  exports: [AppSettingsService],
})
export class AppSettingsModule {}
```

## Integration Points

### Database Integration
- TypeORM entities with proper migrations
- Indexes on frequently queried fields (key, category, isSystem)
- Foreign key relationships where applicable

### Authentication Integration
- Integration with existing JWT authentication
- User context for audit logging
- Permission-based access control

### Configuration Integration
- Integration with NestJS ConfigModule
- Environment-based configuration loading
- Dynamic configuration updates

### Caching Integration
- Redis caching for frequently accessed settings
- Cache invalidation on setting updates
- Performance optimization for read operations