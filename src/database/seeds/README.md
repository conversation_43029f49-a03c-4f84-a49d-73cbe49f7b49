# Database Seeding Framework

This directory contains a comprehensive seeding system for populating your database with initial data across multiple entities.

## 🏗️ Framework Architecture

The seeding system is built with a modular, extensible architecture:

```
src/database/seeds/
├── README.md                 # This documentation
├── base.seeder.ts           # Abstract base class for all seeders
├── master.seeder.ts         # Orchestrates multiple seeders
├── ai-models.seed.ts        # AI models seeder
├── users.seed.ts            # Users seeder
└── [entity].seed.ts         # Future entity seeders
```

### Core Components

#### 1. BaseSeeder (`base.seeder.ts`)

Abstract base class that provides:

- **Consistent Interface**: `seed()`, `forceSeed()`, `list()` methods
- **Logging Utilities**: Color-coded console output with emojis
- **Error Handling**: Standardized error reporting
- **TypeORM Integration**: Database connection management

#### 2. MasterSeeder (`master.seeder.ts`)

Orchestration layer that:

- **Manages Multiple Seeders**: Runs seeders in configured order
- **Bulk Operations**: Seed all entities or specific ones
- **Progress Tracking**: Reports seeding progress and results
- **Dependency Management**: Handles seeder dependencies

#### 3. Individual Seeders

Entity-specific seeders extending BaseSeeder:

- **AIModelSeeder**: Manages AI model configurations
- **UserSeeder**: Manages user accounts and profiles
- **[Future Seeders]**: Additional entities as needed

## 🚀 Quick Start

### Run All Seeders

```bash
# Seed all entities (skips if data exists)
npm run seed all

# Force seed all (clears and reseeds)
npm run seed all --force

# List all seeded data
npm run seed list
```

### Run Specific Seeders

```bash
# Seed specific entities
npm run seed ai-models users

# Force seed specific entities
npm run seed ai-models --force

# List specific entity data
npm run seed list ai-models
```

## 📋 Usage Examples

### Seed Fresh Database

```bash
npm run seed all
```

**Expected Output:**

```
🚀 Database Seeding CLI
═══════════════════════

✅ Database connected successfully

🌱 Running All Seeders...

📦 AI Models Seeder:
✅ Created: Gemini 2.5 Flash (gemini-2.5-flash) 🟢 ⭐ DEFAULT
✅ Created: Claude 3.7 Sonnet (claude-3-7-sonnet) 🟢
... (9 more models)
🎉 Successfully seeded 11 AI models!

🎯 Seeding Summary:
═══════════════════
✅ AI Models: 11 created
⏱️  Total time: 1.2s
```

### List All Data

```bash
npm run seed list
```

**Expected Output:**

```
📋 Database Seed Data Overview
════════════════════════════════

🤖 AI MODELS:
   Gemini 1.5 Flash (gemini-2.5-flash) - 🟢 Active ⭐ DEFAULT
   Claude 3.5 Sonnet (claude-3-7-sonnet) - 🟢 Active
   ... (9 more models)
   📊 Total: 11 models | Active: 8
```

## 🛠️ Adding New Seeders

### Step 1: Create Seeder Class

Create `src/database/seeds/[entity].seed.ts`:

```typescript
import { BaseSeeder } from './base.seeder';
import { DataSource } from 'typeorm';
import { YourEntity } from '../../entities/your-entity.entity';

interface SeedYourEntity {
  // Define your seed data interface
  name: string;
  description: string;
  isActive: boolean;
}

export const yourEntitySeedData: SeedYourEntity[] = [
  {
    name: 'Sample Item 1',
    description: 'First sample item',
    isActive: true,
  },
  {
    name: 'Sample Item 2',
    description: 'Second sample item',
    isActive: false,
  },
];

export class YourEntitySeeder extends BaseSeeder {
  constructor(dataSource: DataSource) {
    super(dataSource);
  }

  async seed(): Promise<void> {
    this.logInfo('🌱 Seeding your entities...');

    const repository = this.dataSource.getRepository(YourEntity);
    let createdCount = 0;

    for (const data of yourEntitySeedData) {
      const existing = await repository.findOne({
        where: { name: data.name },
      });

      if (!existing) {
        const entity = repository.create(data);
        await repository.save(entity);

        this.logSuccess(`Created: ${data.name} ${data.isActive ? '🟢' : '🔴'}`);
        createdCount++;
      } else {
        this.logWarning(`Skipped: ${data.name} (already exists)`);
      }
    }

    this.logSuccess(`🎉 Successfully seeded ${createdCount} entities!`);
  }

  async forceSeed(): Promise<void> {
    this.logInfo('🗑️ Clearing existing data...');

    const repository = this.dataSource.getRepository(YourEntity);
    await repository.clear();

    this.logInfo('✅ Data cleared, proceeding with fresh seed...');
    await this.seed();
  }

  async list(): Promise<void> {
    const repository = this.dataSource.getRepository(YourEntity);
    const entities = await repository.find({
      order: { name: 'ASC' },
    });

    this.logInfo(`📋 Current Your Entities:`);
    this.logInfo(`════════════════════════════════════════════════`);

    if (entities.length === 0) {
      this.logWarning('No entities found in database');
      return;
    }

    entities.forEach((entity) => {
      const status = entity.isActive ? '🟢 Active' : '🔴 Inactive';
      console.log(`   ${entity.name} - ${status}`);
    });

    this.logInfo(`════════════════════════════════════════════════`);
    this.logInfo(`📊 Total: ${entities.length} entities`);
  }
}
```

### Step 2: Register in MasterSeeder

Update `src/database/seeds/master.seeder.ts`:

```typescript
import { YourEntitySeeder } from './your-entity.seed';

export class MasterSeeder {
  private seeders = {
    'ai-models': () => new AIModelSeeder(this.dataSource),
    users: () => new UserSeeder(this.dataSource),
    'your-entity': () => new YourEntitySeeder(this.dataSource), // Add here
  };

  private seederOrder = [
    'ai-models',
    'users',
    'your-entity', // Add to order (dependencies matter!)
  ];

  // ... rest of the class
}
```

### Step 3: Update CLI Script (Optional)

If you want a dedicated CLI for your seeder, create `src/scripts/seed-your-entity.ts`:

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DataSource } from 'typeorm';
import { YourEntitySeeder } from '../database/seeds/your-entity.seed';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: false,
  });

  const dataSource = app.get(DataSource);
  await dataSource.initialize();

  const seeder = new YourEntitySeeder(dataSource);
  const command = process.argv[2];

  console.log('🚀 Your Entity Seeder CLI');
  console.log('═══════════════════════════\n');

  try {
    switch (command) {
      case 'seed':
        const isForce = process.argv.includes('--force');
        if (isForce) {
          await seeder.forceSeed();
        } else {
          await seeder.seed();
        }
        break;
      case 'list':
        await seeder.list();
        break;
      default:
        console.log('Available commands:');
        console.log('  seed      - Seed data (skip if exists)');
        console.log('  seed --force - Force seed (clear and reseed)');
        console.log('  list      - List current data');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await app.close();
  }
}

bootstrap();
```

Add to `package.json`:

```json
{
  "scripts": {
    "seed:your-entity": "ts-node src/scripts/seed-your-entity.ts"
  }
}
```

## 🔧 Troubleshooting

### Database Connection Issues

**Problem**: `Connection failed` or `ECONNREFUSED`

**Solutions**:

1. Verify database server is running:

   ```bash
   # For PostgreSQL
   brew services start postgresql
   # For Docker
   docker-compose up database
   ```

2. Check environment variables in `.env`:

   ```env
   DATABASE_HOST=localhost
   DATABASE_PORT=5432
   DATABASE_USERNAME=postgres
   DATABASE_PASSWORD=your_password
   DATABASE_NAME=grade_right
   ```

3. Test connection manually:
   ```bash
   psql -h localhost -U postgres -d grade_right
   ```

### TypeORM Entity Issues

**Problem**: `Entity not found` or `Repository not defined`

**Solutions**:

1. Ensure entity is properly imported in your module
2. Check entity decorators are correct
3. Verify the entity is in your TypeORM configuration

### Seeding Conflicts

**Problem**: `Duplicate key value violates unique constraint`

**Solutions**:

1. Use `--force` flag to clear existing data:

   ```bash
   npm run seed all --force
   ```

2. Check your seed data for duplicates
3. Update your seeder logic to handle conflicts:
   ```typescript
   const existing = await repository.findOne({
     where: { uniqueField: data.uniqueField },
   });
   if (!existing) {
     // Create new record
   }
   ```

### Performance Issues

**Problem**: Seeding takes too long

**Solutions**:

1. Use bulk operations:

   ```typescript
   await repository.save(entities); // Bulk save
   ```

2. Disable unnecessary logging in production
3. Use database transactions for large datasets

### CLI Script Issues

**Problem**: `Command not found` or `Module not found`

**Solutions**:

1. Ensure TypeScript compilation:

   ```bash
   npm run build
   ```

2. Check import paths are correct
3. Verify script is executable:
   ```bash
   chmod +x src/scripts/seed.ts
   ```

### Foreign Key Constraints

**Problem**: `Foreign key constraint violation`

**Solutions**:

1. Ensure seeder order respects dependencies
2. Seed parent entities before child entities
3. Use proper relationship handling in your seeders

### Memory Issues

**Problem**: `JavaScript heap out of memory`

**Solutions**:

1. Increase Node.js memory limit:

   ```bash
   node --max-old-space-size=4096 src/scripts/seed.ts
   ```

2. Process data in chunks:
   ```typescript
   const chunks = chunkArray(seedData, 100);
   for (const chunk of chunks) {
     await repository.save(chunk);
   }
   ```

## 🔍 Advanced Configuration

### Custom Seeder Order

Modify `seederOrder` in `master.seeder.ts` to handle dependencies:

```typescript
private seederOrder = [
  'users',      // No dependencies
  'ai-models',  // No dependencies
  'assignments', // Depends on users
  'submissions', // Depends on assignments
];
```

### Conditional Seeding

Add environment-based seeding:

```typescript
async seed(): Promise<void> {
  if (process.env.NODE_ENV === 'production') {
    this.logWarning('Skipping seeding in production');
    return;
  }

  // Proceed with seeding
}
```

### Custom Seed Data

Load seed data from external files:

```typescript
import * as fs from 'fs';
import * as path from 'path';

const seedDataPath = path.join(__dirname, 'data', 'your-entity.json');
export const yourEntitySeedData = JSON.parse(
  fs.readFileSync(seedDataPath, 'utf8'),
);
```

## 📚 Related Documentation

- [TypeORM Documentation](https://typeorm.io/)
- [NestJS CLI Documentation](https://docs.nestjs.com/cli/overview)
- [Database Migration Guide](../migrations/README.md)

## 🤝 Contributing

When adding new seeders:

1. Follow the established patterns
2. Include comprehensive logging
3. Handle edge cases gracefully
4. Update this documentation
5. Add tests for complex seeding logic
