import {
  PolicyEntity,
  RoleEntity,
  RolePolicyEntity,
} from '@app/modules/authorization/entities';
import { PolicyEffect, RoleType } from '@app/modules/authorization/enums';
import { PolicyDocument } from '@app/modules/authorization/interfaces';
import { DataSource, Repository } from 'typeorm';
import { BaseSeeder } from './base.seeder';

export class AuthorizationSeeder extends BaseSeeder {
  private roleRepository: Repository<RoleEntity>;
  private policyRepository: Repository<PolicyEntity>;
  private rolePolicyRepository: Repository<RolePolicyEntity>;
  private contextPrefix: string;

  constructor(dataSource: DataSource) {
    super(dataSource, 'Authorization');
    this.roleRepository = this.dataSource.getRepository(RoleEntity);
    this.policyRepository = this.dataSource.getRepository(PolicyEntity);
    this.rolePolicyRepository = this.dataSource.getRepository(RolePolicyEntity);

    // Get context prefix from configuration (defaults to 'aws' for backward compatibility)
    this.contextPrefix = process.env.AUTHORIZATION_CONTEXT_PREFIX || 'aws';
  }

  async seed(): Promise<void> {
    this.log('🔐 Seeding Authorization roles and policies...');

    try {
      // Create default roles
      const adminRole = await this.createAdminRole();
      const userRole = await this.createUserRole();
      const moderatorRole = await this.createModeratorRole();
      const guestRole = await this.createGuestRole();

      // Create default policies
      const adminPolicy = await this.createAdminPolicy();
      const userSelfManagementPolicy =
        await this.createUserSelfManagementPolicy();
      const userReadOnlyPolicy = await this.createUserReadOnlyPolicy();
      const moderatorPolicy = await this.createModeratorPolicy();
      const publicResourcesPolicy = await this.createPublicResourcesPolicy();

      // Attach policies to roles
      await this.attachPoliciesToRoles(adminRole, [adminPolicy]);
      await this.attachPoliciesToRoles(userRole, [
        userSelfManagementPolicy,
        userReadOnlyPolicy,
      ]);
      await this.attachPoliciesToRoles(moderatorRole, [
        moderatorPolicy,
        userReadOnlyPolicy,
      ]);
      await this.attachPoliciesToRoles(guestRole, [publicResourcesPolicy]);

      this.logSuccess('Authorization seeding completed successfully');
    } catch (error) {
      this.logError(`Error seeding Authorization data: ${error.message}`);
      throw error;
    }
  }

  async forceSeed(): Promise<void> {
    this.log('🧹 Force seeding Authorization - cleaning existing data...');
    await this.cleanup();
    await this.seed();
  }

  async list(): Promise<void> {
    this.log('📋 Listing Authorization data...');

    const roles = await this.roleRepository.find({
      order: { priority: 'DESC', name: 'ASC' },
    });

    const policies = await this.policyRepository.find({
      order: { priority: 'DESC', name: 'ASC' },
    });

    const attachments = await this.rolePolicyRepository.find({
      relations: ['role', 'policy'],
      where: { isActive: true },
    });

    console.log('\n🏷️  ROLES:');
    roles.forEach((role) => {
      const status = role.isActive ? '🟢' : '🔴';
      const builtin = role.isBuiltIn ? '🔒' : '📝';
      console.log(
        `   ${status} ${builtin} ${role.name} (${role.type}) - Priority: ${role.priority}`,
      );
      if (role.permissions.length > 0) {
        console.log(`      Permissions: ${role.permissions.join(', ')}`);
      }
    });

    console.log('\n📋 POLICIES:');
    policies.forEach((policy) => {
      const status = policy.isActive ? '🟢' : '🔴';
      const builtin = policy.isBuiltIn ? '🔒' : '📝';
      console.log(
        `   ${status} ${builtin} ${policy.name} (${policy.effect}) - Priority: ${policy.priority}`,
      );
      console.log(`      Actions: ${policy.actions.join(', ')}`);
      console.log(`      Resources: ${policy.resources.join(', ')}`);
    });

    console.log('\n🔗 ROLE-POLICY ATTACHMENTS:');
    attachments.forEach((attachment) => {
      console.log(`   ${attachment.role.name} ← ${attachment.policy.name}`);
    });

    console.log(
      `\n📊 Summary: ${roles.length} roles, ${policies.length} policies, ${attachments.length} attachments`,
    );
  }

  private async createAdminRole(): Promise<RoleEntity> {
    const existingRole = await this.roleRepository.findOne({
      where: { name: 'admin' },
    });
    if (existingRole) {
      console.log('   Admin role already exists, skipping...');
      return existingRole;
    }

    const adminRole = this.roleRepository.create({
      name: 'admin',
      description: 'System administrator with full access to all resources',
      type: RoleType.SYSTEM,
      isBuiltIn: true,
      isActive: true,
      priority: 1000,
      permissions: [
        '*', // Full access to everything
        'user:*',
        'role:*',
        'policy:*',
        'admin:*',
        'system:*',
      ],
    });

    const savedRole = await this.roleRepository.save(adminRole);
    this.logSuccess('Created admin role');
    return savedRole;
  }

  private async createUserRole(): Promise<RoleEntity> {
    const existingRole = await this.roleRepository.findOne({
      where: { name: 'user' },
    });
    if (existingRole) {
      console.log('   User role already exists, skipping...');
      return existingRole;
    }

    const userRole = this.roleRepository.create({
      name: 'user',
      description: 'Standard user with basic access permissions',
      type: RoleType.SYSTEM,
      isBuiltIn: true,
      isActive: true,
      priority: 100,
      permissions: [
        'user:read-own',
        'user:update-own',
        'profile:read',
        'profile:update',
        'app:use',
      ],
    });

    const savedRole = await this.roleRepository.save(userRole);
    this.logSuccess('Created user role');
    return savedRole;
  }

  private async createModeratorRole(): Promise<RoleEntity> {
    const existingRole = await this.roleRepository.findOne({
      where: { name: 'moderator' },
    });
    if (existingRole) {
      console.log('   Moderator role already exists, skipping...');
      return existingRole;
    }

    const moderatorRole = this.roleRepository.create({
      name: 'moderator',
      description: 'Content moderator with elevated permissions',
      type: RoleType.SYSTEM,
      isBuiltIn: true,
      isActive: true,
      priority: 500,
      permissions: [
        'user:read',
        'user:read-own',
        'user:update-own',
        'content:moderate',
        'content:read',
        'content:update',
        'report:create',
        'report:read',
        'app:use',
      ],
    });

    const savedRole = await this.roleRepository.save(moderatorRole);
    this.logSuccess('Created moderator role');
    return savedRole;
  }

  private async createGuestRole(): Promise<RoleEntity> {
    const existingRole = await this.roleRepository.findOne({
      where: { name: 'guest' },
    });
    if (existingRole) {
      console.log('   Guest role already exists, skipping...');
      return existingRole;
    }

    const guestRole = this.roleRepository.create({
      name: 'guest',
      description: 'Guest user with minimal access permissions',
      type: RoleType.SYSTEM,
      isBuiltIn: true,
      isActive: true,
      priority: 10,
      permissions: ['app:view-public', 'content:read-public'],
    });

    const savedRole = await this.roleRepository.save(guestRole);
    this.logSuccess('Created guest role');
    return savedRole;
  }

  private async createAdminPolicy(): Promise<PolicyEntity> {
    const existingPolicy = await this.policyRepository.findOne({
      where: { name: 'AdminFullAccessPolicy' },
    });
    if (existingPolicy) {
      console.log('   Admin policy already exists, skipping...');
      return existingPolicy;
    }

    const adminPolicyDocument: PolicyDocument = {
      version: '2023-11-01',
      id: 'AdminFullAccess',
      statement: [
        {
          sid: 'AllowFullAccess',
          effect: PolicyEffect.ALLOW,
          action: ['*'],
          resource: ['*'],
        },
      ],
    };

    const adminPolicy = this.policyRepository.create({
      name: 'AdminFullAccessPolicy',
      description:
        'Grants full administrative access to all resources and actions',
      document: adminPolicyDocument,
      version: '2023-11-01',
      effect: PolicyEffect.ALLOW,
      actions: ['*'],
      resources: ['*'],
      isActive: true,
      isBuiltIn: true,
      priority: 1000,
      tags: { category: 'system', level: 'admin' },
    });

    const savedPolicy = await this.policyRepository.save(adminPolicy);
    this.logSuccess('Created admin policy');
    return savedPolicy;
  }

  private async createUserSelfManagementPolicy(): Promise<PolicyEntity> {
    const existingPolicy = await this.policyRepository.findOne({
      where: { name: 'UserSelfManagementPolicy' },
    });
    if (existingPolicy) {
      console.log('   User self-management policy already exists, skipping...');
      return existingPolicy;
    }

    const userSelfManagementDocument: PolicyDocument = {
      version: '2023-11-01',
      id: 'UserSelfManagement',
      statement: [
        {
          sid: 'AllowReadOwnProfile',
          effect: PolicyEffect.ALLOW,
          action: ['user:read', 'profile:read'],
          resource: [
            'arn:app:user:*:user/${user.id}',
            'arn:app:profile:*:profile/${user.id}',
          ],
          condition: {
            StringEquals: {
              'user:id': `\${${this.contextPrefix}:userid}`,
            },
          },
        },
        {
          sid: 'AllowUpdateOwnProfile',
          effect: PolicyEffect.ALLOW,
          action: ['user:update', 'profile:update'],
          resource: [
            'arn:app:user:*:user/${user.id}',
            'arn:app:profile:*:profile/${user.id}',
          ],
          condition: {
            StringEquals: {
              'user:id': `\${${this.contextPrefix}:userid}`,
            },
          },
        },
      ],
    };

    const userPolicy = this.policyRepository.create({
      name: 'UserSelfManagementPolicy',
      description:
        'Allows users to read and update their own profile and account information',
      document: userSelfManagementDocument,
      version: '2023-11-01',
      effect: PolicyEffect.ALLOW,
      actions: ['user:read', 'user:update', 'profile:read', 'profile:update'],
      resources: ['arn:app:user:*:user/*', 'arn:app:profile:*:profile/*'],
      isActive: true,
      isBuiltIn: true,
      priority: 100,
      tags: { category: 'user', level: 'self-service' },
    });

    const savedPolicy = await this.policyRepository.save(userPolicy);
    this.logSuccess('Created user self-management policy');
    return savedPolicy;
  }

  private async createUserReadOnlyPolicy(): Promise<PolicyEntity> {
    const existingPolicy = await this.policyRepository.findOne({
      where: { name: 'UserReadOnlyPolicy' },
    });
    if (existingPolicy) {
      console.log('   User read-only policy already exists, skipping...');
      return existingPolicy;
    }

    const userReadOnlyDocument: PolicyDocument = {
      version: '2023-11-01',
      id: 'UserReadOnly',
      statement: [
        {
          sid: 'AllowReadPublicContent',
          effect: PolicyEffect.ALLOW,
          action: ['content:read', 'app:view'],
          resource: ['arn:app:content:*:*', 'arn:app:app:*:*'],
          condition: {
            StringEquals: {
              'content:visibility': 'public',
            },
          },
        },
        {
          sid: 'AllowBasicAppUsage',
          effect: PolicyEffect.ALLOW,
          action: ['app:use', 'app:navigate'],
          resource: ['arn:app:app:*:*'],
        },
      ],
    };

    const readOnlyPolicy = this.policyRepository.create({
      name: 'UserReadOnlyPolicy',
      description:
        'Grants basic read access to public content and application features',
      document: userReadOnlyDocument,
      version: '2023-11-01',
      effect: PolicyEffect.ALLOW,
      actions: ['content:read', 'app:view', 'app:use', 'app:navigate'],
      resources: ['arn:app:content:*:*', 'arn:app:app:*:*'],
      isActive: true,
      isBuiltIn: true,
      priority: 50,
      tags: { category: 'user', level: 'read-only' },
    });

    const savedPolicy = await this.policyRepository.save(readOnlyPolicy);
    this.logSuccess('Created user read-only policy');
    return savedPolicy;
  }

  private async createModeratorPolicy(): Promise<PolicyEntity> {
    const existingPolicy = await this.policyRepository.findOne({
      where: { name: 'ModeratorPolicy' },
    });
    if (existingPolicy) {
      console.log('   Moderator policy already exists, skipping...');
      return existingPolicy;
    }

    const moderatorDocument: PolicyDocument = {
      version: '2023-11-01',
      id: 'ModeratorAccess',
      statement: [
        {
          sid: 'AllowContentModeration',
          effect: PolicyEffect.ALLOW,
          action: [
            'content:moderate',
            'content:read',
            'content:update',
            'content:flag',
          ],
          resource: ['arn:app:content:*:*'],
        },
        {
          sid: 'AllowUserReadAccess',
          effect: PolicyEffect.ALLOW,
          action: ['user:read', 'profile:read'],
          resource: ['arn:app:user:*:*', 'arn:app:profile:*:*'],
        },
        {
          sid: 'AllowReportManagement',
          effect: PolicyEffect.ALLOW,
          action: ['report:read', 'report:create', 'report:update'],
          resource: ['arn:app:report:*:*'],
        },
        {
          sid: 'DenyAdminOperations',
          effect: PolicyEffect.DENY,
          action: ['admin:*', 'system:*', 'user:delete'],
          resource: ['*'],
        },
      ],
    };

    const moderatorPolicy = this.policyRepository.create({
      name: 'ModeratorPolicy',
      description:
        'Grants content moderation permissions while restricting administrative access',
      document: moderatorDocument,
      version: '2023-11-01',
      effect: PolicyEffect.ALLOW,
      actions: [
        'content:moderate',
        'content:read',
        'content:update',
        'content:flag',
        'user:read',
        'profile:read',
        'report:read',
        'report:create',
        'report:update',
      ],
      resources: [
        'arn:app:content:*:*',
        'arn:app:user:*:*',
        'arn:app:profile:*:*',
        'arn:app:report:*:*',
      ],
      isActive: true,
      isBuiltIn: true,
      priority: 200,
      tags: { category: 'moderator', level: 'elevated' },
    });

    const savedPolicy = await this.policyRepository.save(moderatorPolicy);
    this.logSuccess('Created moderator policy');
    return savedPolicy;
  }

  private async createPublicResourcesPolicy(): Promise<PolicyEntity> {
    const existingPolicy = await this.policyRepository.findOne({
      where: { name: 'PublicResourcesPolicy' },
    });
    if (existingPolicy) {
      console.log('   Public resources policy already exists, skipping...');
      return existingPolicy;
    }

    const publicResourcesDocument: PolicyDocument = {
      version: '2023-11-01',
      id: 'PublicResourcesAccess',
      statement: [
        {
          sid: 'AllowPublicContentRead',
          effect: PolicyEffect.ALLOW,
          action: ['content:read'],
          resource: ['arn:app:content:*:*'],
          condition: {
            StringEquals: {
              'content:visibility': 'public',
            },
          },
        },
        {
          sid: 'AllowPublicAppAccess',
          effect: PolicyEffect.ALLOW,
          action: ['app:view-public', 'app:navigate-public'],
          resource: ['arn:app:app:*:public/*'],
        },
        {
          sid: 'DenyPrivateAccess',
          effect: PolicyEffect.DENY,
          action: ['*'],
          resource: ['*'],
          condition: {
            StringEquals: {
              'resource:visibility': 'private',
            },
          },
        },
      ],
    };

    const publicPolicy = this.policyRepository.create({
      name: 'PublicResourcesPolicy',
      description:
        'Grants access to public resources only, denies private resource access',
      document: publicResourcesDocument,
      version: '2023-11-01',
      effect: PolicyEffect.ALLOW,
      actions: ['content:read', 'app:view-public', 'app:navigate-public'],
      resources: ['arn:app:content:*:*', 'arn:app:app:*:public/*'],
      isActive: true,
      isBuiltIn: true,
      priority: 25,
      tags: { category: 'guest', level: 'public' },
    });

    const savedPolicy = await this.policyRepository.save(publicPolicy);
    this.logSuccess('Created public resources policy');
    return savedPolicy;
  }

  private async attachPoliciesToRoles(
    role: RoleEntity,
    policies: PolicyEntity[],
  ): Promise<void> {
    for (const policy of policies) {
      const existingAttachment = await this.rolePolicyRepository.findOne({
        where: { roleId: role.id, policyId: policy.id },
      });

      if (!existingAttachment) {
        const attachment = this.rolePolicyRepository.create({
          roleId: role.id,
          policyId: policy.id,
          isActive: true,
          attachedAt: new Date(),
        });

        await this.rolePolicyRepository.save(attachment);
        console.log(
          `   ✓ Attached policy "${policy.name}" to role "${role.name}"`,
        );
      } else {
        console.log(
          `   Policy "${policy.name}" already attached to role "${role.name}", skipping...`,
        );
      }
    }
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Authorization seed data...');

    // Note: Be careful with cleanup in production!
    // This should only be used in development/testing environments

    await this.rolePolicyRepository.delete({ role: { isBuiltIn: true } });
    await this.policyRepository.delete({ isBuiltIn: true });
    await this.roleRepository.delete({ isBuiltIn: true });

    console.log('✅ Authorization cleanup completed');
  }
}
