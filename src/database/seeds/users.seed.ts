import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { DataSource } from 'typeorm';
import { BaseSeeder } from './base.seeder';

export interface SeedUser {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  isActive: boolean;
  verifiedAt?: Date;
}

export const usersSeedData: SeedUser[] = [
  {
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    isActive: true,
    password: 'Test@123',
    verifiedAt: new Date(),
  },
];

export class UserSeeder extends BaseSeeder {
  constructor(dataSource: DataSource) {
    super(dataSource, 'Users');
  }

  async seed(): Promise<void> {
    const userRepository = this.dataSource.getRepository(UserEntity);

    this.log('👥 Seeding users...');

    // Check if we already have users
    const existingUsersCount = await userRepository.count();

    if (existingUsersCount > 0) {
      this.logInfo(
        `Found ${existingUsersCount} existing users. Skipping seed.`,
      );
      console.log('💡 Use --force flag to reseed or clear the table manually.');
      return;
    }

    await this.performSeed();
  }

  async forceSeed(): Promise<void> {
    const userRepository = this.dataSource.getRepository(UserEntity);

    this.log('🔄 Force seeding users (clearing existing data)...');

    // Clear existing data
    await userRepository.delete({});
    this.logWarning('Cleared existing users');

    await this.performSeed();
  }

  async list(): Promise<void> {
    const userRepository = this.dataSource.getRepository(UserEntity);

    const users = await userRepository.find({
      order: { firstName: 'ASC' },
    });

    if (users.length === 0) {
      this.logInfo('No users found in database');
      return;
    }

    console.log('\n👥 Current Users:');
    console.log('════════════════════════════════════════════════');

    for (const user of users) {
      const status = user.isActive ? '🟢 Active' : '🔴 Inactive';
      const verified = user.isEmailVerified ? '✅ Verified' : '⏳ Pending';

      console.log(
        `${user.firstName} ${user.lastName} (${user.email}) - ${status} ${verified}`,
      );
    }

    const activeCount = users.filter((u) => u.isActive).length;
    const verifiedCount = users.filter((u) => u.isEmailVerified).length;

    console.log('════════════════════════════════════════════════');
    console.log(
      `📊 Total: ${users.length} users | Active: ${activeCount} | Verified: ${verifiedCount}`,
    );
  }

  private async performSeed(): Promise<void> {
    const userRepository = this.dataSource.getRepository(UserEntity);
    const userLoginRepository = this.dataSource.getRepository(UserLoginEntity);

    for (const seedUser of usersSeedData) {
      const existingUser = await userRepository.findOne({
        where: { email: seedUser.email },
      });

      if (existingUser) {
        this.logWarning(`User '${seedUser.email}' already exists, skipping...`);
        continue;
      }

      const newUser = userRepository.create(seedUser);
      const passwordHash = await bcrypt.hash(seedUser.password, 10);
      const newUserLogin = userLoginRepository.create({
        username: seedUser.email,
        password: passwordHash,
        isActive: seedUser.isActive,
        user: newUser,
      });
      await this.dataSource.transaction(async (manager) => {
        await manager.save(newUser);
        await manager.save(newUserLogin);
      });

      const status = newUser.isActive ? '🟢' : '🔴';

      this.logSuccess(
        `Created: ${newUser.firstName} ${newUser.lastName} (${newUser.email}) ${status}`,
      );
    }

    const totalSeeded = usersSeedData.length;
    const activeCount = usersSeedData.filter((u) => u.isActive).length;

    console.log(`\n🎉 Successfully seeded ${totalSeeded} users!`);
    console.log(`📊 Active users: ${activeCount}/${totalSeeded}`);
  }
}
