import { AuthorizationSeeder } from '@app/database/seeds/authorization.seed';
import { UserSeeder } from '@app/database/seeds/users.seed';
import { DataSource } from 'typeorm';
import { BaseSeeder } from './base.seeder';

export interface SeederConfig {
  name: string;
  seeder: BaseSeeder;
  enabled: boolean;
  order: number;
}

export class MasterSeeder {
  private dataSource: DataSource;
  private seeders: SeederConfig[];

  constructor(dataSource: DataSource) {
    this.dataSource = dataSource;
    this.initializeSeeders();
  }

  private initializeSeeders(): void {
    this.seeders = [
      {
        name: 'user',
        seeder: new UserSeeder(this.dataSource),
        enabled: true,
        order: 1,
      },
      {
        name: 'authorization',
        seeder: new AuthorizationSeeder(this.dataSource),
        enabled: true,
        order: 2,
      },
    ];

    // Sort by order
    this.seeders.sort((a, b) => a.order - b.order);
  }

  async seedAll(force = false): Promise<void> {
    console.log('🚀 Master Seeder - Running All Seeds');
    console.log('════════════════════════════════════════');

    const enabledSeeders = this.seeders.filter((s) => s.enabled);

    for (const seederConfig of enabledSeeders) {
      console.log(`\n📋 Running ${seederConfig.name} seeder...`);
      try {
        if (force) {
          await seederConfig.seeder.forceSeed();
        } else {
          await seederConfig.seeder.seed();
        }
        console.log(`✅ ${seederConfig.name} seeder completed`);
      } catch (error) {
        console.error(
          `❌ ${seederConfig.name} seeder failed:`,
          error instanceof Error ? error.message : String(error),
        );
        throw error; // Stop execution on error
      }
    }

    console.log('\n🎉 All seeders completed successfully!');
    console.log(`📊 Total seeders run: ${enabledSeeders.length}`);
  }

  async seedSpecific(seederName: string, force = false): Promise<void> {
    console.log(`🎯 Running specific seeder: ${seederName}`);
    console.log('═══════════════════════════════════════');

    const seederConfig = this.seeders.find((s) => s.name === seederName);

    if (!seederConfig) {
      throw new Error(
        `Seeder '${seederName}' not found. Available seeders: ${this.getAvailableSeeders().join(', ')}`,
      );
    }

    if (!seederConfig.enabled) {
      console.log(`⚠️ Seeder '${seederName}' is disabled`);
      return;
    }

    try {
      if (force) {
        await seederConfig.seeder.forceSeed();
      } else {
        await seederConfig.seeder.seed();
      }
      console.log(`✅ ${seederName} seeder completed`);
    } catch (error) {
      console.error(
        `❌ ${seederName} seeder failed:`,
        error instanceof Error ? error.message : String(error),
      );
      throw error;
    }
  }

  async listSpecific(seederName: string): Promise<void> {
    console.log(`📋 Listing data for: ${seederName}`);
    console.log('═══════════════════════════════════════');

    const seederConfig = this.seeders.find((s) => s.name === seederName);

    if (!seederConfig) {
      throw new Error(
        `Seeder '${seederName}' not found. Available seeders: ${this.getAvailableSeeders().join(', ')}`,
      );
    }

    await seederConfig.seeder.list();
  }

  async listAll(): Promise<void> {
    console.log('📋 Listing All Seeded Data');
    console.log('════════════════════════════════════════');

    const enabledSeeders = this.seeders.filter((s) => s.enabled);

    for (const seederConfig of enabledSeeders) {
      console.log(`\n🔍 ${seederConfig.name.toUpperCase()} DATA:`);
      try {
        await seederConfig.seeder.list();
      } catch (error) {
        console.error(
          `❌ Failed to list ${seederConfig.name}:`,
          error instanceof Error ? error.message : String(error),
        );
      }
    }
  }

  getAvailableSeeders(): string[] {
    return this.seeders.map((s) => s.name);
  }

  getEnabledSeeders(): string[] {
    return this.seeders.filter((s) => s.enabled).map((s) => s.name);
  }

  getSeederInfo(): SeederConfig[] {
    return this.seeders.map((s) => ({
      name: s.name,
      seeder: s.seeder,
      enabled: s.enabled,
      order: s.order,
    }));
  }

  showSeedersStatus(): void {
    console.log('\n📊 Available Seeders:');
    console.log('════════════════════════════════════════');

    this.seeders.forEach((seederConfig) => {
      const status = seederConfig.enabled ? '🟢 Enabled' : '🔴 Disabled';
      const order = `[Order: ${seederConfig.order}]`;
      console.log(`   ${seederConfig.name} - ${status} ${order}`);
    });

    const enabledCount = this.seeders.filter((s) => s.enabled).length;
    console.log('════════════════════════════════════════');
    console.log(`Total: ${this.seeders.length} | Enabled: ${enabledCount}`);
  }
}
