import { DataSource } from 'typeorm';

export interface SeederInterface {
  seed(): Promise<void>;
  forceSeed(): Promise<void>;
  list(): Promise<void>;
}

export abstract class BaseSeeder implements SeederInterface {
  protected dataSource: DataSource;
  protected seederName: string;

  constructor(dataSource: DataSource, seederName: string) {
    this.dataSource = dataSource;
    this.seederName = seederName;
  }

  abstract seed(): Promise<void>;
  abstract forceSeed(): Promise<void>;
  abstract list(): Promise<void>;

  protected log(message: string): void {
    console.log(`[${this.seederName}] ${message}`);
  }

  protected logSuccess(message: string): void {
    console.log(`✅ [${this.seederName}] ${message}`);
  }

  protected logWarning(message: string): void {
    console.log(`⚠️ [${this.seederName}] ${message}`);
  }

  protected logError(message: string): void {
    console.log(`❌ [${this.seederName}] ${message}`);
  }

  protected logInfo(message: string): void {
    console.log(`ℹ️ [${this.seederName}] ${message}`);
  }
}
