import 'module-alias/register';

import { GlobalExceptionFilter } from '@app/common/filter';
import { TransformInterceptor } from '@app/common/interceptors/transform.interceptor';
import { GlobalApiLoggerMiddleware } from '@app/common/middleware';
import { isProduction } from '@app/common/utils/env';
import { RedisIoAdapter } from '@app/common/websocket/socketio/adapter/redis-io.adapter';
import { AlertInterceptor } from '@app/modules/alert/alert.interceptor';
import { AlertService } from '@app/modules/alert/alert.service';
import { setupSwagger } from '@app/setup-swagger';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import * as httpContext from 'express-http-context';
import * as session from 'express-session';
import { AppModule } from './app.module';

const PORT = process.env.PORT || 1998;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Set up WebSocket adapter with Redis
  const configService = app.get(ConfigService);
  const redisIoAdapter = new RedisIoAdapter(app, {
    url: `redis://${configService.getOrThrow('redis.host')}:${configService.getOrThrow('redis.port')}`,
  });
  await redisIoAdapter.connectToRedis();
  app.useWebSocketAdapter(redisIoAdapter);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        excludeExtraneousValues: true,
      },
    }),
  );

  app.enableCors('*');
  app.use(httpContext.middleware);
  app.use('/api', GlobalApiLoggerMiddleware);

  // OAuth 2.0 authentication requires session support when using state.
  app.use(
    session({
      secret: process.env.SESSION_SECRET || 'secret',
      resave: false,
      saveUninitialized: false,
    }),
  );

  app.useGlobalInterceptors(
    new AlertInterceptor(app.get(AlertService)),
    new TransformInterceptor(),
  );
  app.useGlobalFilters(new GlobalExceptionFilter());

  app.setGlobalPrefix('api');
  app.enableVersioning({
    type: VersioningType.URI,
  });

  setupSwagger(app);

  await app.listen(PORT, () => {
    console.log(`🚀 Server ready at http://localhost:${PORT}`);
    if (!isProduction()) {
      console.log(`🚀 Swagger at http://localhost:${PORT}/api/docs`);
    }
  });
}
bootstrap()
  .then()
  .catch((err) => console.error(`Application start error:\n`, err));
