import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import alertConfig, { AlertConfigType } from './alert.config';
import { AlertModule } from './alert.module';

/**
 * GLOBAL REGISTRATION EXAMPLES
 * Use these in your main AppModule to make AlertService available globally
 */

/**
 * Example 1: Global registration with forRoot (synchronous configuration)
 * AlertService will be available in all modules without importing AlertModule
 */
@Module({
  imports: [
    AlertModule.forRoot({
      email: {
        enabled: true,
        templateDir: './templates',
      },
      slack: {
        enabled: true,
        channel: '#alerts',
        token: 'xoxb-your-slack-token',
      },
      telegram: {
        enabled: true,
        token: '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
        chatId: '-100123456789',
      },
      discord: {
        enabled: false, // Disabled in this example
        webhookUrl: '',
      },
    }),
  ],
})
export class AppModuleWithGlobalConfig {}

/**
 * Example 2: Global registration with forRootAsync (asynchronous configuration)
 * Perfect for environment-based configuration using ConfigService
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [alertConfig],
      isGlobal: true,
    }),
    AlertModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<{ alert: AlertConfigType }>,
      ) => {
        const config = configService.get('alert', { infer: true });
        return {
          email: {
            enabled: true,
            templateDir: 'templates',
          },
          slack: {
            enabled: true,
            channel: '#alerts',
            token: '',
          },
          telegram: {
            enabled: true,
            token: '',
            chatId: '',
          },
          discord: {
            enabled: true,
            webhookUrl: '',
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModuleWithGlobalAsyncConfig {}

/**
 * LOCAL REGISTRATION EXAMPLES (Backward Compatible)
 * Use these when you want AlertService only in specific modules
 */

/**
 * Example 3: Local registration with register (synchronous configuration)
 */
@Module({
  imports: [
    AlertModule.register({
      email: {
        enabled: true,
      },
      slack: {
        enabled: true,
        channel: 'alerts',
        token: 'xoxb-your-token',
      },
      telegram: {
        enabled: true,
        token: '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
        chatId: '-100123456789',
      },
      discord: {
        enabled: true,
        webhookUrl: 'https://discord.com/api/webhooks/123456789/abcdefg',
      },
    }),
  ],
})
export class AppModuleWithDirectConfig {}

/**
 * Example 4: Local registration with registerAsync (asynchronous configuration)
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [alertConfig],
    }),
    AlertModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<{ alert: AlertConfigType }>,
      ) => {
        const config = configService.get('alert', { infer: true });
        return {
          email: config?.email || { enabled: false },
          slack: config?.slack || { enabled: false, channel: '', token: '' },
          telegram: config?.telegram || {
            enabled: false,
            token: '',
            chatId: '',
          },
          discord: config?.discord || { enabled: false, webhookUrl: '' },
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModuleWithAsyncConfig {}

/**
 * USAGE EXAMPLES WITH GLOBAL REGISTRATION
 * When using forRoot() or forRootAsync(), AlertService is available everywhere
 */

/**
 * Example: Using AlertService in any service without importing AlertModule
 */
import { Injectable } from '@nestjs/common';
import { AlertService } from './alert.service';

@Injectable()
export class UserService {
  constructor(private readonly alertService: AlertService) {}

  async createUser(userData: any) {
    try {
      // User creation logic
      const user = await this.saveUser(userData);

      // Send success notification
      await this.alertService.sendAlert(
        `New user registered: ${user.email}`,
        'User Registration',
        { userId: user.id, timestamp: new Date() },
      );

      return user;
    } catch (error) {
      // Send error alert to all configured channels
      await this.alertService.sendErrorAlert(error, {
        action: 'createUser',
        userData: userData.email,
      });
      throw error;
    }
  }

  private async saveUser(userData: any) {
    // Mock user creation
    return { id: 1, email: userData.email };
  }
}

@Injectable()
export class OrderService {
  constructor(private readonly alertService: AlertService) {}

  async processPayment(orderId: string, amount: number) {
    try {
      // Payment processing logic
      await this.chargePayment(orderId, amount);

      // Send payment success alert
      await this.alertService.sendAlert(
        `Payment processed successfully for order ${orderId}`,
        'Payment Success',
        { orderId, amount, timestamp: new Date() },
      );
    } catch (error) {
      // Send critical payment failure alert
      await this.alertService.sendErrorAlert(error, {
        orderId,
        amount,
        action: 'processPayment',
        severity: 'critical',
      });
      throw error;
    }
  }

  private async chargePayment(orderId: string, amount: number) {
    // Mock payment processing
    if (Math.random() > 0.8) {
      throw new Error('Payment gateway timeout');
    }
  }
}

/**
 * Example: Using AlertService in a controller
 */
import { Body, Controller, Post } from '@nestjs/common';

@Controller('notifications')
export class NotificationController {
  constructor(private readonly alertService: AlertService) {}

  @Post('send')
  async sendNotification(@Body() body: { message: string; subject: string }) {
    await this.alertService.sendAlert(body.message, body.subject, {
      source: 'api',
      timestamp: new Date(),
    });
    return { success: true };
  }
}

/**
 * DYNAMIC PROVIDER CONFIGURATION (NEW FEATURE)
 *
 * The AlertModule now passes configuration directly to individual provider modules,
 * allowing each provider to receive its specific configuration options instead of
 * relying on global ConfigService.
 *
 * ## How It Works
 *
 * ### Before (ConfigService-based):
 * ```typescript
 * // AlertModule passes options but providers still use ConfigService
 * AlertModule.forRoot({
 *   discord: { enabled: true, webhookUrl: 'https://...' }
 * })
 * // DiscordAlertService gets config from ConfigService, not the passed options
 * ```
 *
 * ### After (Direct Configuration):
 * ```typescript
 * // AlertModule passes specific config to each provider
 * AlertModule.forRoot({
 *   discord: { enabled: true, webhookUrl: 'https://...' }
 * })
 * // DiscordAlertService receives the discord config directly via dependency injection
 * ```
 *
 * ## Benefits
 *
 * - **Direct Configuration**: Providers receive configuration directly from AlertModule
 * - **Type Safety**: Each provider has strongly-typed configuration interfaces
 * - **Validation**: Configuration is validated at module registration time
 * - **Backward Compatibility**: Existing ConfigService-based configurations still work
 * - **Flexibility**: Mix and match direct config with ConfigService fallback
 *
 * ## Individual Provider Usage
 *
 * You can also use individual provider modules directly:
 *
 * ```typescript
 * import { DiscordAlertModule } from './discord-alert/discord-alert.module';
 * import { EmailAlertModule } from './email-alert/email-alert.module';
 *
 * @Module({
 *   imports: [
 *     DiscordAlertModule.forRoot({
 *       enabled: true,
 *       webhookUrl: 'https://discord.com/api/webhooks/123456789/abcdefg'
 *     }),
 *     EmailAlertModule.forRoot({
 *       enabled: true,
 *       adminEmail: '<EMAIL>',
 *       templateName: 'alert-template'
 *     })
 *   ]
 * })
 * export class FeatureModule {}
 * ```
 */

/**
 * COMPARISON: Local vs Global Registration
 *
 * LOCAL REGISTRATION (register/registerAsync):
 * - Each module must import AlertModule
 * - AlertService only available in modules that import it
 * - Good for feature-specific alert configurations
 *
 * GLOBAL REGISTRATION (forRoot/forRootAsync):
 * - Register once in AppModule
 * - AlertService available in all modules automatically
 * - Single configuration for entire application
 * - Recommended for most use cases
 */

/**
 * MIGRATION GUIDE: From ConfigService to Direct Configuration
 *
 * ## Before (ConfigService-based)
 * ```typescript
 * // Configuration in environment variables or config files
 * ALERT_EMAIL_ENABLED=true
 * EMAIL_ALERT_ADMIN_EMAIL=<EMAIL>
 * ALERT_DISCORD_ENABLED=true
 * DISCORD_ALERT_WEBHOOK_URL=https://discord.com/api/webhooks/...
 *
 * // Module registration
 * AlertModule.forRoot({
 *   email: { enabled: true },  // Config comes from ConfigService
 *   discord: { enabled: true } // Config comes from ConfigService
 * })
 * ```
 *
 * ## After (Direct Configuration)
 * ```typescript
 * // Configuration passed directly to providers
 * AlertModule.forRoot({
 *   email: {
 *     enabled: true,
 *     adminEmail: '<EMAIL>',
 *     templateName: 'alert-template'
 *   },
 *   discord: {
 *     enabled: true,
 *     webhookUrl: 'https://discord.com/api/webhooks/...'
 *   }
 * })
 * ```
 *
 * ## Backward Compatibility
 *
 * Existing configurations continue to work! If you don't provide direct configuration,
 * the providers will automatically fall back to ConfigService behavior.
 *
 * ```typescript
 * // This still works - providers will use ConfigService
 * AlertModule.forRoot({
 *   email: { enabled: true },    // Falls back to ConfigService
 *   discord: { enabled: false }  // Disabled, no fallback needed
 * })
 * ```
 */

/**
 * Environment Variables Example for forRootAsync:
 *
 * .env file:
 * ALERT_EMAIL_ENABLED=true
 * EMAIL_TEMPLATE_DIR=./templates
 * ALERT_SLACK_ENABLED=true
 * SLACK_CHANNEL=#alerts
 * SLACK_TOKEN=xoxb-your-slack-token
 * ALERT_TELEGRAM_ENABLED=false
 * ALERT_DISCORD_ENABLED=false
 */
