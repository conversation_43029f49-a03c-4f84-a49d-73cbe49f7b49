import { ALERT_MODULE_OPTIONS } from '@app/modules/alert/alert.constant';
import { AlertProvider } from '@app/modules/alert/alert.provider';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { AlertOptions, AlertType } from './alert.interface';
import { DiscordAlertService } from './modules/discord-alert/discord-alert.service';
import { EmailAlertService } from './modules/email-alert/email-alert.service';
import { SlackAlertService } from './modules/slack-alert/slack-alert.service';
import { TelegramAlertService } from './modules/telegram-alert/telegram-alert.service';

@Injectable()
export class AlertService implements AlertProvider {
  private readonly logger = new Logger(AlertService.name);
  private alertType: AlertType = 'all';

  constructor(
    @Optional() private readonly emailAlertService: EmailAlertService,
    @Optional() private readonly slackAlertService: SlackAlertService,
    @Optional() private readonly telegramAlertService: TelegramAlertService,
    @Optional() private readonly discordAlertService: DiscordAlertService,
    @Inject(ALERT_MODULE_OPTIONS) private readonly options: AlertOptions,
  ) {
    // Determine which alert services are available based on configuration
    const enabledServices = [
      options.email?.enabled,
      options.slack?.enabled,
      options.telegram?.enabled,
      options.discord?.enabled,
    ].filter(Boolean);

    if (enabledServices.length === 0) {
      this.logger.warn('No alert services are enabled');
      return;
    }

    if (enabledServices.length === 1) {
      if (options.email?.enabled) this.alertType = 'email';
      else if (options.slack?.enabled) this.alertType = 'slack';
      else if (options.telegram?.enabled) this.alertType = 'telegram';
      else if (options.discord?.enabled) this.alertType = 'discord';
    } else {
      this.alertType = 'all';
    }

    this.logger.log(`Alert service initialized with type: ${this.alertType}`);
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    switch (this.alertType) {
      case 'email':
        if (this.emailAlertService) {
          await this.emailAlertService.sendAlert(message, subject, context);
        }
        break;
      case 'slack':
        if (this.slackAlertService) {
          // Slack alert service expects error objects, so we need to adapt
          const error = new Error(message);
          await this.slackAlertService.sendErrorAlert(error, {
            subject,
            ...context,
          });
        }
        break;
      case 'telegram':
        if (this.telegramAlertService) {
          await this.telegramAlertService.sendAlert(message, subject, context);
        }
        break;
      case 'discord':
        if (this.discordAlertService) {
          await this.discordAlertService.sendAlert(message, subject, context);
        }
        break;
      case 'all':
        // Send to all enabled channels
        if (this.emailAlertService && this.options.email?.enabled) {
          await this.emailAlertService.sendAlert(message, subject, context);
        }
        if (this.slackAlertService && this.options.slack?.enabled) {
          const error = new Error(message);
          await this.slackAlertService.sendErrorAlert(error, {
            subject,
            ...context,
          });
        }
        if (this.telegramAlertService && this.options.telegram?.enabled) {
          await this.telegramAlertService.sendAlert(message, subject, context);
        }
        if (this.discordAlertService && this.options.discord?.enabled) {
          await this.discordAlertService.sendAlert(message, subject, context);
        }
        break;
      default:
        this.logger.warn('No alert channels available');
    }
  }

  // Additional utility methods for specific alert types
  async sendErrorAlert(
    error: Error,
    context: Record<string, any> = {},
  ): Promise<void> {
    if (this.slackAlertService && this.options.slack?.enabled) {
      await this.slackAlertService.sendErrorAlert(error, context);
    }

    if (this.telegramAlertService && this.options.telegram?.enabled) {
      await this.telegramAlertService.sendAlert(error.message, 'Error Alert', {
        stack: error.stack,
        ...context,
      });
    }

    if (this.discordAlertService && this.options.discord?.enabled) {
      await this.discordAlertService.sendAlert(error.message, 'Error Alert', {
        stack: error.stack,
        ...context,
      });
    }

    if (
      this.emailAlertService &&
      this.options.email?.enabled &&
      (this.alertType === 'email' || this.alertType === 'all')
    ) {
      await this.emailAlertService.sendAlert(error.message, 'Error Alert', {
        stack: error.stack,
        ...context,
      });
    }
  }
}
