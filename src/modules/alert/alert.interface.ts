import { ModuleMetadata } from '@nestjs/common';
import { DiscordAlertConfig } from './modules/discord-alert/discord-alert.interface';
import { EmailAlertConfig } from './modules/email-alert/email-alert.interface';
import { SlackAlertConfig } from './modules/slack-alert/slack-alert.interface';
import { TelegramAlertConfig } from './modules/telegram-alert/telegram-alert.interface';

// Re-export provider-specific configuration interfaces for convenience
export type { DiscordAlertConfig } from './modules/discord-alert/discord-alert.interface';
export type { EmailAlertConfig } from './modules/email-alert/email-alert.interface';
export type { SlackAlertConfig } from './modules/slack-alert/slack-alert.interface';
export type { TelegramAlertConfig } from './modules/telegram-alert/telegram-alert.interface';

export interface AlertOptions {
  email?: EmailAlertConfig;
  slack?: SlackAlertConfig;
  telegram?: TelegramAlertConfig;
  discord?: DiscordAlertConfig;
}

export interface AlertAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
  useFactory: (...args: any[]) => Promise<AlertOptions> | AlertOptions;
  inject?: any[];
}

export type AlertType = 'email' | 'slack' | 'telegram' | 'discord' | 'all';
