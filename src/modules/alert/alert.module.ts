import { ALERT_MODULE_OPTIONS } from '@app/modules/alert/alert.constant';
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { AlertDynamicFactory } from './alert-dynamic.factory';
import { AlertAsyncOptions, AlertOptions } from './alert.interface';
import { AlertService } from './alert.service';
import { DiscordAlertModule } from './modules/discord-alert/discord-alert.module';
import { EmailAlertModule } from './modules/email-alert/email-alert.module';
import { SlackAlertModule } from './modules/slack-alert/slack-alert.module';
import { TelegramAlertModule } from './modules/telegram-alert/telegram-alert.module';

/**
 * AlertModule provides a flexible alert system supporting multiple providers (Email, Slack, Telegram, Discord).
 *
 * ## Registration Methods
 *
 * ### Local Registration (Module-specific)
 * - `register(options)` - Synchronous configuration, module must be imported in each consuming module
 * - `registerAsync(options)` - Asynchronous configuration, module must be imported in each consuming module
 *
 * ### Global Registration (Application-wide)
 * - `forRoot(options)` - Synchronous configuration, AlertService available globally without imports
 * - `forRootAsync(options)` - Asynchronous configuration, AlertService available globally without imports
 *
 * ## Key Differences
 *
 * **Local vs Global:**
 * - Local: Each module that needs AlertService must import AlertModule
 * - Global: Register once in AppModule, use AlertService anywhere without additional imports
 *
 * **Sync vs Async:**
 * - Sync: Configuration provided directly as object
 * - Async: Configuration provided via factory function (useful with ConfigService)
 *
 * ## Usage Examples
 *
 * ### Global Registration with forRoot
 * ```typescript
 * // app.module.ts
 * @Module({
 *   imports: [
 *     AlertModule.forRoot({
 *       email: { enabled: true, templateDir: './templates' },
 *       slack: { enabled: true, channel: '#alerts', token: process.env.SLACK_TOKEN }
 *     })
 *   ]
 * })
 * export class AppModule {}
 *
 * // user.service.ts - No need to import AlertModule
 * @Injectable()
 * export class UserService {
 *   constructor(private alertService: AlertService) {}
 *
 *   async createUser(userData: any) {
 *     // ... user creation logic
 *     await this.alertService.sendAlert('New user created', 'User Registration');
 *   }
 * }
 * ```
 *
 * ### Global Registration with forRootAsync
 * ```typescript
 * // app.module.ts
 * @Module({
 *   imports: [
 *     ConfigModule.forRoot(),
 *     AlertModule.forRootAsync({
 *       imports: [ConfigModule],
 *       useFactory: (configService: ConfigService) => ({
 *         email: {
 *           enabled: configService.get('ALERT_EMAIL_ENABLED') === 'true',
 *           templateDir: configService.get('EMAIL_TEMPLATE_DIR')
 *         },
 *         slack: {
 *           enabled: configService.get('ALERT_SLACK_ENABLED') === 'true',
 *           channel: configService.get('SLACK_CHANNEL'),
 *           token: configService.get('SLACK_TOKEN')
 *         },
 *         telegram: {
 *           enabled: configService.get('ALERT_TELEGRAM_ENABLED') === 'true',
 *           token: configService.get('TELEGRAM_TOKEN'),
 *           chatId: configService.get('TELEGRAM_CHAT_ID')
 *         }
 *       }),
 *       inject: [ConfigService]
 *     })
 *   ]
 * })
 * export class AppModule {}
 * ```
 *
 * ### Local Registration (Backward Compatible)
 * ```typescript
 * // feature.module.ts
 * @Module({
 *   imports: [
 *     AlertModule.register({
 *       email: { enabled: true, templateDir: './templates' }
 *     })
 *   ],
 *   providers: [FeatureService]
 * })
 * export class FeatureModule {}
 * ```
 */
@Module({})
export class AlertModule {
  /**
   * Register the AlertModule with synchronous configuration for local use.
   * The AlertService will only be available in modules that import this AlertModule.
   * Use forRoot() instead for global availability.
   *
   * @param options - Alert configuration options
   * @returns DynamicModule configured for local use
   *
   * @example
   * ```typescript
   * // feature.module.ts
   * @Module({
   *   imports: [
   *     AlertModule.register({
   *       email: { enabled: true, templateDir: './templates' },
   *       slack: { enabled: false }
   *     })
   *   ],
   *   providers: [FeatureService]
   * })
   * export class FeatureModule {}
   * ```
   */
  static register(options: AlertOptions): DynamicModule {
    return {
      module: AlertModule,
      imports: this.getImports(options),
      providers: [
        {
          provide: ALERT_MODULE_OPTIONS,
          useValue: options,
        },
        AlertService,
      ],
      exports: [AlertService],
    };
  }

  /**
   * Register the AlertModule globally with synchronous configuration.
   * This makes the AlertService available throughout the entire application
   * without needing to import AlertModule in individual modules.
   *
   * @param options - Alert configuration options
   * @returns DynamicModule configured as global
   *
   * @example
   * ```typescript
   * // In app.module.ts
   * @Module({
   *   imports: [
   *     AlertModule.forRoot({
   *       email: { enabled: true, templateDir: './templates' },
   *       slack: { enabled: true, channel: '#alerts', token: 'your-token' }
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In any other module, inject AlertService without importing AlertModule
   * @Injectable()
   * export class SomeService {
   *   constructor(private alertService: AlertService) {}
   * }
   * ```
   */
  static forRoot(options: AlertOptions): DynamicModule {
    return {
      module: AlertModule,
      global: true,
      imports: this.getImports(options),
      providers: [
        {
          provide: ALERT_MODULE_OPTIONS,
          useValue: options,
        },
        AlertService,
      ],
      exports: [AlertService],
    };
  }

  private static createAsyncProviders(options: AlertAsyncOptions): Provider[] {
    return [
      {
        provide: ALERT_MODULE_OPTIONS,
        useFactory: options.useFactory,
        inject: options.inject || [],
      },
    ];
  }

  /**
   * Get imports based on enabled options (used for register() and forRoot() methods)
   * Uses dynamic module imports to pass specific configuration to each provider.
   */
  private static getImports(options: AlertOptions): Array<any> {
    const imports: Array<any> = [];

    if (options.slack?.enabled) {
      imports.push(SlackAlertModule.forRoot(options.slack));
    }

    if (options.email?.enabled) {
      imports.push(EmailAlertModule.forRoot(options.email));
    }

    if (options.telegram?.enabled) {
      imports.push(TelegramAlertModule.forRoot(options.telegram));
    }

    if (options.discord?.enabled) {
      imports.push(DiscordAlertModule.forRoot(options.discord));
    }

    return imports;
  }

  /**
   * Register the AlertModule with async configuration and dynamic imports.
   * This combines the benefits of async configuration with true dynamic imports.
   * Only imports the modules that are determined to be enabled at runtime.
   *
   * @param options - Async alert configuration options
   * @returns Promise<DynamicModule> configured for local use
   *
   * @example
   * ```typescript
   * // feature.module.ts
   * @Module({
   *   imports: [
   *     ConfigModule,
   *     AlertModule.registerAsync({
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         email: {
   *           enabled: configService.get('FEATURE_EMAIL_ENABLED') === 'true',
   *           templateDir: './feature-templates'
   *         },
   *         slack: {
   *           enabled: configService.get('FEATURE_SLACK_ENABLED') === 'true',
   *           // Only imported if enabled
   *         }
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ],
   *   providers: [FeatureService]
   * })
   * export class FeatureModule {}
   * ```
   */
  static async registerAsync(
    options: AlertAsyncOptions,
  ): Promise<DynamicModule> {
    // Create a temporary context to resolve the async configuration
    const { NestFactory } = await import('@nestjs/core');
    const tempApp = await NestFactory.createApplicationContext({
      module: class TempModule {},
      imports: options.imports || [],
      providers: this.createAsyncProviders(options),
    });

    try {
      // Resolve the configuration
      const config = await tempApp.get(ALERT_MODULE_OPTIONS);
      const dynamicImports =
        await AlertDynamicFactory.createDynamicImports(config);

      return {
        module: AlertModule,
        imports: [...(options.imports || []), ...dynamicImports],
        providers: [
          {
            provide: ALERT_MODULE_OPTIONS,
            useValue: config,
          },
          AlertService,
        ],
        exports: [AlertService],
      };
    } finally {
      await tempApp.close();
    }
  }

  /**
   * Register the AlertModule globally with async configuration and dynamic imports.
   * This combines the benefits of async configuration with true dynamic imports.
   * Only imports the modules that are determined to be enabled at runtime.
   *
   * @param options - Async alert configuration options
   * @returns Promise<DynamicModule> configured as global
   *
   * @example
   * ```typescript
   * // app.module.ts
   * @Module({
   *   imports: [
   *     ConfigModule.forRoot(),
   *     AlertModule.forRootAsync({
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         email: {
   *           enabled: configService.get('ALERT_EMAIL_ENABLED') === 'true',
   *           templateDir: configService.get('EMAIL_TEMPLATE_DIR')
   *         },
   *         slack: {
   *           enabled: configService.get('ALERT_SLACK_ENABLED') === 'true',
   *           channel: configService.get('SLACK_CHANNEL'),
   *           token: configService.get('SLACK_TOKEN')
   *         }
   *         // Only enabled providers will have their modules imported
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   */
  static async forRootAsync(
    options: AlertAsyncOptions,
  ): Promise<DynamicModule> {
    // Create a temporary context to resolve the async configuration
    const { NestFactory } = await import('@nestjs/core');
    const tempApp = await NestFactory.createApplicationContext({
      module: class TempModule {},
      imports: options.imports || [],
      providers: this.createAsyncProviders(options),
    });

    try {
      // Resolve the configuration
      const config = await tempApp.get(ALERT_MODULE_OPTIONS);
      const dynamicImports =
        await AlertDynamicFactory.createDynamicImports(config);

      return {
        module: AlertModule,
        global: true,
        imports: [...(options.imports || []), ...dynamicImports],
        providers: [
          {
            provide: ALERT_MODULE_OPTIONS,
            useValue: config,
          },
          AlertService,
        ],
        exports: [AlertService],
      };
    } finally {
      await tempApp.close();
    }
  }
}
