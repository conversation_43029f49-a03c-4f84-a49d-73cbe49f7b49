import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SLACK_ALERT_MODULE_OPTION } from './slack-alert.constant';
import { SlackAlertConfig } from './slack-alert.interface';
import { SlackAlertService } from './slack-alert.service';

/**
 * SlackAlertModule provides Slack webhook alert functionality.
 *
 * ## Usage
 *
 * ### Dynamic Configuration (Recommended)
 * ```typescript
 * SlackAlertModule.forRoot({
 *   enabled: true,
 *   channel: '#alerts',
 *   token: 'xoxb-your-slack-token'
 * })
 * ```
 *
 * ### Static Configuration (Backward Compatible)
 * ```typescript
 * // Uses ConfigService for configuration
 * SlackAlertModule
 * ```
 */
@Module({
  imports: [ConfigModule],
  providers: [SlackAlertService],
  exports: [SlackAlertService],
})
export class SlackAlertModule {
  /**
   * Register the SlackAlertModule with specific configuration.
   * This allows the module to receive configuration directly instead of relying on ConfigService.
   *
   * @param config - Slack alert configuration options
   * @returns DynamicModule configured with the provided options
   *
   * @example
   * ```typescript
   * SlackAlertModule.forRoot({
   *   enabled: true,
   *   channel: '#alerts',
   *   token: 'xoxb-your-slack-token'
   * })
   * ```
   */
  static forRoot(config: SlackAlertConfig): DynamicModule {
    // Validate configuration
    if (config.enabled && !config.token) {
      throw new Error('Slack token is required when Slack alerts are enabled');
    }

    if (config.enabled && !config.channel) {
      throw new Error(
        'Slack channel is required when Slack alerts are enabled',
      );
    }

    return {
      module: SlackAlertModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: SLACK_ALERT_MODULE_OPTION,
          useValue: config,
        },
        SlackAlertService,
      ],
      exports: [SlackAlertService],
    };
  }
}
