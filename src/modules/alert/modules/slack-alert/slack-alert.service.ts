import { getCircularReplacer } from '@app/common/utils/json';
import { AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertProvider } from '@app/modules/alert/alert.provider';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KnownBlock, WebClient } from '@slack/web-api';
import { SLACK_ALERT_MODULE_OPTION } from './slack-alert.constant';
import { SlackAlertConfig } from './slack-alert.interface';

@Injectable()
export class SlackAlertService implements AlertProvider {
  private readonly logger = new Logger(SlackAlertService.name);
  private client: WebClient;
  private readonly config: SlackAlertConfig;

  constructor(
    @Optional()
    @Inject(SLACK_ALERT_MODULE_OPTION)
    injectedConfig: SlackAlertConfig,
    private configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    // Use injected config if available, otherwise fall back to ConfigService
    this.config = injectedConfig || this.getConfigFromService();
    this.client = new WebClient(this.config.token);
  }

  /**
   * Get configuration from ConfigService (fallback behavior)
   */
  private getConfigFromService(): SlackAlertConfig {
    const slackAlertConfig = this.configService.get('alert.slack', {
      infer: true,
    });

    return {
      enabled: slackAlertConfig?.enabled || false,
      channel: slackAlertConfig?.channel || '',
      token: slackAlertConfig?.token || '',
    };
  }

  async sendErrorAlert(
    error: Error,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      if (!this.config.enabled) {
        this.logger.debug('Slack alerts are disabled');
        return;
      }

      if (!this.config.channel) {
        this.logger.error('Slack channel not configured');
        return;
      }

      const blocks: KnownBlock[] = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🚨 Error Alert',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Error:*\n>${error.message.substring(0, 1000)}`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Stack:*\n\`\`\`${error.stack?.substring(0, 1000)}\`\`\``,
          },
        },
      ];

      // Add context if provided
      if (Object.keys(context).length > 0) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Additional Context:*\n\`\`\`${JSON.stringify(context, getCircularReplacer(), 2).substring(0, 1000)}\`\`\``,
          },
        });
      }

      await this.client.chat.postMessage({
        channel: this.config.channel,
        blocks,
        text: '🚨 Error Alert',
      });
      this.logger.debug('Slack error alert sent');
    } catch (err) {
      this.logger.error('Failed to send Slack error alert', err);
    }
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      if (!this.config.enabled) {
        this.logger.debug('Slack alerts are disabled');
        return;
      }

      if (!this.config.channel) {
        this.logger.error('Slack channel not configured');
        return;
      }

      const blocks: KnownBlock[] = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `📢 ${subject}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: message,
          },
        },
      ];

      // Add context if provided
      if (Object.keys(context).length > 0) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Additional Context:*\n\`\`\`${JSON.stringify(context, getCircularReplacer(), 2).substring(0, 1000)}\`\`\``,
          },
        });
      }

      await this.client.chat.postMessage({
        channel: this.config.channel,
        blocks,
        text: `${subject}: ${message}`,
      });

      this.logger.debug('Slack alert sent');
    } catch (err) {
      this.logger.error('Failed to send Slack alert', err);
    }
  }
}
