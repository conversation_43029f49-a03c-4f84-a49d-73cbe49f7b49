import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DISCORD_ALERT_MODULE_OPTION } from './discord-alert.constant';
import { DiscordAlertConfig } from './discord-alert.interface';
import { DiscordAlertService } from './discord-alert.service';

/**
 * DiscordAlertModule provides Discord webhook alert functionality.
 *
 * ## Usage
 *
 * ### Dynamic Configuration (Recommended)
 * ```typescript
 * DiscordAlertModule.forRoot({
 *   enabled: true,
 *   webhookUrl: 'https://discord.com/api/webhooks/123456789/abcdefg'
 * })
 * ```
 *
 * ### Static Configuration (Backward Compatible)
 * ```typescript
 * // Uses ConfigService for configuration
 * DiscordAlertModule
 * ```
 */
@Module({
  imports: [ConfigModule],
  providers: [DiscordAlertService],
  exports: [DiscordAlertService],
})
export class DiscordAlertModule {
  /**
   * Register the DiscordAlertModule with specific configuration.
   * This allows the module to receive configuration directly instead of relying on ConfigService.
   *
   * @param config - Discord alert configuration options
   * @returns DynamicModule configured with the provided options
   *
   * @example
   * ```typescript
   * DiscordAlertModule.forRoot({
   *   enabled: true,
   *   webhookUrl: 'https://discord.com/api/webhooks/123456789/abcdefg'
   * })
   * ```
   */
  static forRoot(config: DiscordAlertConfig): DynamicModule {
    // Validate configuration
    if (config.enabled && !config.webhookUrl) {
      throw new Error(
        'Discord webhook URL is required when Discord alerts are enabled',
      );
    }

    if (
      config.enabled &&
      !config.webhookUrl.startsWith('https://discord.com/api/webhooks/')
    ) {
      throw new Error('Invalid Discord webhook URL format');
    }

    return {
      module: DiscordAlertModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: DISCORD_ALERT_MODULE_OPTION,
          useValue: config,
        },
        DiscordAlertService,
      ],
      exports: [DiscordAlertService],
    };
  }
}
