import { getCircularReplacer } from '@app/common/utils/json';
import { AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertProvider } from '@app/modules/alert/alert.provider';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { DISCORD_ALERT_MODULE_OPTION } from './discord-alert.constant';
import { DiscordAlertConfig } from './discord-alert.interface';

interface DiscordEmbed {
  title: string;
  description: string;
  color: number;
  timestamp: string;
  fields: Array<{
    name: string;
    value: string;
  }>;
}

@Injectable()
export class DiscordAlertService implements AlertProvider {
  private readonly logger = new Logger(DiscordAlertService.name);
  private readonly config: DiscordAlertConfig;

  constructor(
    @Optional()
    @Inject(DISCORD_ALERT_MODULE_OPTION)
    injectedConfig: DiscordAlertConfig,
    private configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    // Use injected config if available, otherwise fall back to ConfigService
    this.config = injectedConfig || this.getConfigFromService();

    if (this.config.enabled && !this.config.webhookUrl) {
      this.logger.warn('Discord alerts are enabled but webhookUrl is missing');
    }
  }

  /**
   * Get configuration from ConfigService (fallback behavior)
   */
  private getConfigFromService(): DiscordAlertConfig {
    const discordConfig = this.configService.get('alert.discord', {
      infer: true,
    });

    return {
      enabled: discordConfig?.enabled || false,
      webhookUrl: discordConfig?.webhookUrl || '',
    };
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      if (!this.config.enabled) {
        this.logger.debug('Discord alerts are disabled');
        return;
      }

      if (!this.config.webhookUrl || this.config.webhookUrl === '') {
        this.logger.error('Discord webhook URL not configured');
        return;
      }

      // Format the Discord embed message
      const embed: DiscordEmbed = {
        title: subject,
        description: message,
        color: 0xff0000, // Red color for alerts
        timestamp: new Date().toISOString(),
        fields: [],
      };

      // Add context as fields if provided
      if (Object.keys(context).length > 0) {
        for (const [key, value] of Object.entries(context)) {
          if (key === 'stack') {
            embed.fields.push({
              name: 'Stack Trace',
              value: `\`\`\`\n${value}\n\`\`\``.substring(0, 1024), // Discord field value has 1024 char limit
            });
          } else {
            const stringValue =
              typeof value === 'object'
                ? JSON.stringify(value, getCircularReplacer(), 2)
                : String(value);

            embed.fields.push({
              name: key,
              value: `\`\`\`\n${stringValue}\n\`\`\``.substring(0, 1024),
            });
          }
        }
      }

      // Send the message using Discord Webhook API
      await axios.post(this.config.webhookUrl, {
        embeds: [embed],
      });

      this.logger.debug('Discord alert sent');
    } catch (err) {
      this.logger.error('Failed to send Discord alert', err);
    }
  }
}
