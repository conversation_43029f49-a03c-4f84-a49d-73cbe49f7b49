import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TELEGRAM_ALERT_MODULE_OPTION } from './telegram-alert.constant';
import { TelegramAlertConfig } from './telegram-alert.interface';
import { TelegramAlertService } from './telegram-alert.service';

/**
 * TelegramAlertModule provides Telegram bot alert functionality.
 *
 * ## Usage
 *
 * ### Dynamic Configuration (Recommended)
 * ```typescript
 * TelegramAlertModule.forRoot({
 *   enabled: true,
 *   token: '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
 *   chatId: '-100123456789'
 * })
 * ```
 *
 * ### Static Configuration (Backward Compatible)
 * ```typescript
 * // Uses ConfigService for configuration
 * TelegramAlertModule
 * ```
 */
@Module({
  imports: [ConfigModule],
  providers: [TelegramAlertService],
  exports: [TelegramAlertService],
})
export class TelegramAlertModule {
  /**
   * Register the TelegramAlertModule with specific configuration.
   * This allows the module to receive configuration directly instead of relying on ConfigService.
   *
   * @param config - Telegram alert configuration options
   * @returns DynamicModule configured with the provided options
   *
   * @example
   * ```typescript
   * TelegramAlertModule.forRoot({
   *   enabled: true,
   *   token: '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
   *   chatId: '-100123456789'
   * })
   * ```
   */
  static forRoot(config: TelegramAlertConfig): DynamicModule {
    // Validate configuration
    if (config.enabled && !config.token) {
      throw new Error(
        'Telegram token is required when Telegram alerts are enabled',
      );
    }

    if (config.enabled && !config.chatId) {
      throw new Error(
        'Telegram chat ID is required when Telegram alerts are enabled',
      );
    }

    if (config.enabled && config.token && !config.token.includes(':')) {
      throw new Error(
        'Invalid Telegram token format. Token should contain ":"',
      );
    }

    return {
      module: TelegramAlertModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: TELEGRAM_ALERT_MODULE_OPTION,
          useValue: config,
        },
        TelegramAlertService,
      ],
      exports: [TelegramAlertService],
    };
  }
}
