import { getCircularReplacer } from '@app/common/utils/json';
import { AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertProvider } from '@app/modules/alert/alert.provider';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as TelegramBot from 'node-telegram-bot-api';
import { TELEGRAM_ALERT_MODULE_OPTION } from './telegram-alert.constant';
import { TelegramAlertConfig } from './telegram-alert.interface';

@Injectable()
export class TelegramAlertService implements AlertProvider {
  private readonly logger = new Logger(TelegramAlertService.name);
  private readonly config: TelegramAlertConfig;
  private readonly apiBaseUrl: string;
  private readonly bot: TelegramBot;

  constructor(
    @Optional()
    @Inject(TELEGRAM_ALERT_MODULE_OPTION)
    injectedConfig: TelegramAlertConfig,
    private configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    // Use injected config if available, otherwise fall back to ConfigService
    this.config = injectedConfig || this.getConfigFromService();

    this.apiBaseUrl = `https://api.telegram.org/bot${this.config.token}`;
    this.bot = new TelegramBot(this.config.token, { polling: false });

    if (this.config.enabled && (!this.config.token || !this.config.chatId)) {
      this.logger.warn(
        'Telegram alerts are enabled but token or chatId is missing',
      );
    }
  }

  /**
   * Get configuration from ConfigService (fallback behavior)
   */
  private getConfigFromService(): TelegramAlertConfig {
    const telegramConfig = this.configService.get('alert.telegram', {
      infer: true,
    });

    return {
      enabled: telegramConfig?.enabled || false,
      token: telegramConfig?.token || '',
      chatId: telegramConfig?.chatId || '',
    };
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      if (!this.config.enabled) {
        this.logger.debug('Telegram alerts are disabled');
        return;
      }

      if (!this.config.token || !this.config.chatId) {
        this.logger.error('Telegram token or chatId not configured');
        return;
      }

      // Format the message
      let formattedMessage = `*${subject}*\n\n${message}`;

      // Add context if provided
      if (Object.keys(context).length > 0) {
        const contextString = JSON.stringify(
          context,
          getCircularReplacer(),
          2,
        ).substring(0, 1000);
        formattedMessage += `\n\n*Additional Context:*\n\`\`\`\n${contextString}\n\`\`\``;
      }

      // Send the message using Telegram API
      await axios.post(`${this.apiBaseUrl}/sendMessage`, {
        chat_id: this.config.chatId,
        text: formattedMessage,
        parse_mode: 'Markdown',
      });

      // await this.bot.sendMessage(this.config.chatId, formattedMessage, {
      //   parse_mode: 'Markdown',
      // });

      this.logger.debug('Telegram alert sent');
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to send Telegram alert');
    }
  }
}
