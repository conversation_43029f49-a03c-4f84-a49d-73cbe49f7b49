import { MailerModule } from '@app/modules/mailer/mailer.module';
import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EMAIL_ALERT_MODULE_OPTION } from './email-alert.constant';
import { EmailAlertConfig } from './email-alert.interface';
import { EmailAlertService } from './email-alert.service';

/**
 * EmailAlertModule provides email alert functionality.
 *
 * ## Usage
 *
 * ### Dynamic Configuration (Recommended)
 * ```typescript
 * EmailAlertModule.forRoot({
 *   enabled: true,
 *   templateDir: './templates',
 *   templateName: 'alert-template',
 *   adminEmail: '<EMAIL>'
 * })
 * ```
 *
 * ### Static Configuration (Backward Compatible)
 * ```typescript
 * // Uses ConfigService for configuration
 * EmailAlertModule
 * ```
 */
@Module({
  imports: [ConfigModule, MailerModule],
  providers: [EmailAlertService],
  exports: [EmailAlertService],
})
export class EmailAlertModule {
  /**
   * Register the EmailAlertModule with specific configuration.
   * This allows the module to receive configuration directly instead of relying on ConfigService.
   *
   * @param config - Email alert configuration options
   * @returns DynamicModule configured with the provided options
   *
   * @example
   * ```typescript
   * EmailAlertModule.forRoot({
   *   enabled: true,
   *   templateDir: './templates',
   *   templateName: 'alert-template',
   *   adminEmail: '<EMAIL>'
   * })
   * ```
   */
  static forRoot(config: EmailAlertConfig): DynamicModule {
    // Validate configuration
    if (config.enabled && !config.adminEmail) {
      throw new Error('Admin email is required when email alerts are enabled');
    }

    if (
      config.enabled &&
      config.adminEmail &&
      !config.adminEmail.includes('@')
    ) {
      throw new Error('Invalid admin email format');
    }

    return {
      module: EmailAlertModule,
      imports: [ConfigModule, MailerModule],
      providers: [
        {
          provide: EMAIL_ALERT_MODULE_OPTION,
          useValue: config,
        },
        EmailAlertService,
      ],
      exports: [EmailAlertService],
    };
  }
}
