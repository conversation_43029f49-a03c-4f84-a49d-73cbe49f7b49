import { getCircularReplacer } from '@app/common/utils/json';
import { AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertProvider } from '@app/modules/alert/alert.provider';
import { MailerService } from '@app/modules/mailer/mailer.service';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EMAIL_ALERT_MODULE_OPTION } from './email-alert.constant';
import { EmailAlertConfig } from './email-alert.interface';

@Injectable()
export class EmailAlertService implements AlertProvider {
  private readonly logger = new Logger(EmailAlertService.name);
  private readonly config: EmailAlertConfig;

  constructor(
    private readonly mailerService: MailerService,
    @Optional()
    @Inject(EMAIL_ALERT_MODULE_OPTION)
    injectedConfig: EmailAlertConfig,
    private readonly configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    // Use injected config if available, otherwise fall back to ConfigService
    this.config = injectedConfig || this.getConfigFromService();
  }

  /**
   * Get configuration from ConfigService (fallback behavior)
   */
  private getConfigFromService(): EmailAlertConfig {
    const emailConfig = this.configService.get('alert.email', {
      infer: true,
    });

    return {
      enabled: emailConfig?.enabled ?? false,
      templateName: emailConfig?.templateName,
      adminEmail: emailConfig?.adminEmail ?? '',
      templateDir: undefined, // Not available in ConfigService, only in direct config
    };
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      if (!this.config.enabled) {
        this.logger.debug('Email alerts are disabled');
        return;
      }

      // Get admin email from config
      if (!this.config.adminEmail) {
        this.logger.warn('Admin email not configured for email alerts');
        return;
      }

      // Merge the message into the context
      const alertContext = {
        ...context,
        message,
        timestamp: new Date().toISOString(),
      };

      // Using a template for the email alert if template is specified
      if (this.config.templateName) {
        await this.mailerService.sendMailWithTemplate({
          to: this.config.adminEmail,
          subject,
          template: this.config.templateName,
          context: alertContext,
        });
      } else {
        // Otherwise send a simple text email
        await this.mailerService.sendMail({
          to: this.config.adminEmail,
          subject: subject,
          html: this.renderHtml(message, new Date().toISOString(), context),
        });
      }

      this.logger.debug('Email alert sent');
    } catch (err) {
      this.logger.error('Failed to send email alert', err);
    }
  }

  private renderHtml(
    message: string,
    timestamp: string,
    context: Record<string, any>,
  ): string {
    return `
    <h1>Alert</h1>
    <p>Message: ${message}</p>
    <p>Timestamp: ${timestamp}</p>
    <p>Context: ${JSON.stringify(context, getCircularReplacer(), 2)}</p>
    `;
  }
}
