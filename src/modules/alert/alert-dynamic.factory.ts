import { DynamicModule } from '@nestjs/common';
import { AlertOptions } from './alert.interface';

/**
 * Factory for creating dynamic alert modules based on runtime configuration.
 * This approach uses dynamic imports to only load the modules that are actually needed.
 */
export class AlertDynamicFactory {
  /**
   * Create dynamic imports based on configuration.
   * Only imports modules for enabled providers.
   */
  static async createDynamicImports(
    options: AlertOptions,
  ): Promise<DynamicModule[]> {
    const imports: DynamicModule[] = [];
    // Define module loaders for dynamic imports
    const moduleLoaders = {
      slack: async () => {
        const { SlackAlertModule } = await import(
          './modules/slack-alert/slack-alert.module'
        );
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return SlackAlertModule.forRoot(options.slack);
      },
      email: async () => {
        const { EmailAlertModule } = await import(
          './modules/email-alert/email-alert.module'
        );
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return EmailAlertModule.forRoot(options.email);
      },
      telegram: async () => {
        const { TelegramAlertModule } = await import(
          './modules/telegram-alert/telegram-alert.module'
        );
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return TelegramAlertModule.forRoot(options.telegram);
      },
      discord: async () => {
        const { DiscordAlertModule } = await import(
          './modules/discord-alert/discord-alert.module'
        );
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return DiscordAlertModule.forRoot(options.discord);
      },
    };

    // Load only enabled modules
    const loadPromises: Promise<DynamicModule>[] = [];

    if (options.slack?.enabled) {
      loadPromises.push(moduleLoaders.slack());
    }

    if (options.email?.enabled) {
      loadPromises.push(moduleLoaders.email());
    }

    if (options.telegram?.enabled) {
      loadPromises.push(moduleLoaders.telegram());
    }

    if (options.discord?.enabled) {
      loadPromises.push(moduleLoaders.discord());
    }

    // Wait for all dynamic imports to complete
    const loadedModules = await Promise.all(loadPromises);
    imports.push(...loadedModules);

    return imports;
  }

  /**
   * Create a dynamic module with lazy-loaded providers.
   * This is an alternative approach that creates providers on-demand.
   */
  static async createDynamicModule(
    options: AlertOptions,
    isGlobal: boolean = false,
  ): Promise<DynamicModule> {
    const dynamicImports = await this.createDynamicImports(options);
    return {
      module: class DynamicAlertModule {},
      global: isGlobal,
      imports: dynamicImports,
      providers: [],
      exports: [],
    };
  }
}
