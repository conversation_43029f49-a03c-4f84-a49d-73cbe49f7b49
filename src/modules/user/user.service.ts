import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { NullableType } from '@app/common/types';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserInput } from './dto/create-user.input';
import { UpdateUserProfileInput } from './dto/update-user-profile.input';
import { UserEntity } from './entities/user.entity';
import { USER_ERROR_CODES } from './user.error-code';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async create(input: CreateUserInput): Promise<UserEntity> {
    const existingUser = await this.userRepository.findOne({
      where: { email: input.email },
    });

    if (existingUser) {
      throw new HttpErrorException(USER_ERROR_CODES.EMAIL_ALREADY_EXISTS, {
        description: `Email ${input.email} already exists`,
      });
    }

    const user = this.userRepository.create({
      ...input,
    });

    return this.userRepository.save(user);
  }

  async findAll(): Promise<UserEntity[]> {
    return this.userRepository.find();
  }

  async findById(id: string): Promise<NullableType<UserEntity>> {
    const user = await this.userRepository.findOne({ where: { id } });
    return user;
  }

  async findByIdOrThrow(id: string): Promise<UserEntity> {
    const user = await this.findById(id);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND, {
        description: `User with ID ${id} not found`,
      });
    }
    return user;
  }

  async findByEmail(email: string): Promise<UserEntity | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  async findByEmailOrThrow(email: string): Promise<UserEntity> {
    const user = await this.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND, {
        description: `User with email ${email} not found`,
      });
    }
    return user;
  }

  async updateProfile(
    id: string,
    updateUserDto: UpdateUserProfileInput,
  ): Promise<UserEntity> {
    const user = await this.findByIdOrThrow(id);
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  async remove(id: string): Promise<void> {
    const result = await this.userRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
  }

  async verifyEmail(userId: string): Promise<UserEntity> {
    const user = await this.findByIdOrThrow(userId);
    user.isEmailVerified = true;
    return this.userRepository.save(user);
  }

  async update(id: string, data: Partial<UserEntity>): Promise<UserEntity> {
    const user = await this.findByIdOrThrow(id);
    Object.assign(user, data);
    return this.userRepository.save(user);
  }
}
