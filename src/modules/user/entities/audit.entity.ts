import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { NullableType } from '@app/common/types';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Column, Index, ManyToOne } from 'typeorm';

@Index(['createdById'])
@Index(['updatedById'])
export abstract class AuditEntity extends CustomBaseEntity {
  @Column({ name: 'created_by_id', type: 'uuid', nullable: true })
  createdById: NullableType<string>;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  createdBy: NullableType<UserEntity>;

  @Column({ name: 'updated_by_id', type: 'uuid', nullable: true })
  updatedById: NullableType<string>;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  updatedBy: NullableType<UserEntity>;
}
