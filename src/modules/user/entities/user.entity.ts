import { SoftDeleteEntity } from '@app/common/entities/soft-delete.entity';
import { Column, Entity } from 'typeorm';

@Entity('users')
export class UserEntity extends SoftDeleteEntity {
  @Column({ name: 'email', unique: true })
  email: string;

  @Column({ name: 'first_name' })
  firstName: string;

  @Column({ name: 'last_name' })
  lastName: string;

  @Column({ name: 'is_email_verified', default: false })
  isEmailVerified: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;
}
