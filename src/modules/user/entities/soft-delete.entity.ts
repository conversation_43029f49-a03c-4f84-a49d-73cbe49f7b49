import { SoftDeleteEntity } from '@app/common/entities/soft-delete.entity';
import { NullableType } from '@app/common/types';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Column, Index, ManyToOne } from 'typeorm';

@Index(['createdById'])
@Index(['updatedById'])
@Index(['updatedById'])
export abstract class SoftDeleteAuditEntity extends SoftDeleteEntity {
  @Column({ name: 'created_by_id', type: 'uuid', nullable: true })
  createdById: NullableType<string>;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  createdBy: NullableType<UserEntity>;

  @Column({ name: 'updated_by_id', type: 'uuid', nullable: true })
  updatedById: NullableType<string>;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  updatedBy: NullableType<UserEntity>;

  @Column({ name: 'deleted_by_id', type: 'uuid', nullable: true })
  deletedById: NullableType<string>;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  deletedBy: NullableType<UserEntity>;
}
