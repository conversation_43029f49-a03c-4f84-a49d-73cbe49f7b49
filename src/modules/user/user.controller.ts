import { toDto } from '@app/common/dto/to-dto';
import { UserProfileDto } from '@app/modules/user/dto/user-profile.dto';
import { UserService } from '@app/modules/user/user.service';
import { User } from '@modules/auth/decorators/user.decorator';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { JwtPayloadType } from '@modules/auth/strategies/types/jwt-payload.type';
import { Body, Controller, Get, Patch, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UpdateUserProfileInput } from './dto/update-user-profile.input';

@ApiTags('Users')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'The user profile',
    type: UserProfileDto,
  })
  @UseGuards(JwtAuthGuard)
  @Get('/profile')
  async getProfile(@User() user: JwtPayloadType) {
    const userProfile = await this.userService.findById(user.sub);
    return toDto(UserProfileDto, userProfile);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'The updated user profile',
    type: UserProfileDto,
  })
  @UseGuards(JwtAuthGuard)
  @Patch('/profile')
  async updateProfile(
    @User() user: JwtPayloadType,
    @Body() updateUserProfileDto: UpdateUserProfileInput,
  ) {
    const updatedUser = await this.userService.updateProfile(
      user.sub,
      updateUserProfileDto,
    );
    return toDto(UserProfileDto, updatedUser);
  }
}
