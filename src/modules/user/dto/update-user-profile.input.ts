import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, MinLength } from 'class-validator';

export class UpdateUserProfileInput {
  @ApiProperty({
    description: 'The first name of the user',
    example: '<PERSON>',
    required: false,
  })
  @IsString()
  @MinLength(2)
  @IsOptional()
  @Expose()
  firstName?: string;

  @ApiProperty({
    description: 'The last name of the user',
    example: '<PERSON><PERSON>',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @Expose()
  lastName?: string;

  @ApiProperty({
    description: 'The location of the user',
    example: 'New York',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @Expose()
  location?: string;

  @ApiProperty({
    description: 'The bio of the user',
    example: 'I am a software engineer',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Expose()
  bio?: string;
}
