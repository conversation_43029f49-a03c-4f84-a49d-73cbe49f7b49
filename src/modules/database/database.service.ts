import { HttpErrorException } from '@common/exception';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { DATABASE_ERROR_CODES } from './database.error-codes';
import {
  ArchiveOptions,
  ArchiveResult,
  BackupResult,
} from './database.interfaces';

@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Archive data from a table based on time range criteria
   * Archive table format: [original_table_name]_archive_[YYYY_MM_DD_HH_mm_ss]
   */
  async archiveData(
    tableName: string,
    options: ArchiveOptions,
  ): Promise<ArchiveResult> {
    this.validateTableName(tableName);
    await this.validateTableExists(tableName);
    this.validateTimeRange(options.timeRange);

    const timestamp = this.generateTimestamp();
    const archiveTableName = `${tableName}_archive_${timestamp}`;

    return this.dataSource.transaction(async (manager) => {
      try {
        // Create archive table with same structure as original
        await manager.query(`
          CREATE TABLE ${archiveTableName} AS 
          SELECT * FROM ${tableName} WHERE 1=0
        `);

        // Move data based on time range criteria
        const whereClause = this.buildTimeRangeWhereClause(options);
        const result = await manager.query(
          `
          WITH archived_data AS (
            DELETE FROM ${tableName}
            WHERE ${whereClause}
            RETURNING *
          )
          INSERT INTO ${archiveTableName}
          SELECT * FROM archived_data
        `,
          [options.timeRange.startDate, options.timeRange.endDate],
        );

        const archivedRecordCount = Array.isArray(result) ? result.length : 0;

        this.logger.log(
          `Archived ${archivedRecordCount} records from ${tableName} to ${archiveTableName}`,
        );

        return {
          originalTableName: tableName,
          archiveTableName,
          archivedRecordCount,
          timestamp: new Date(),
        };
      } catch (error) {
        this.logger.error(
          `Failed to archive data from ${tableName}: ${error.message}`,
        );
        throw new HttpErrorException(
          DATABASE_ERROR_CODES.ARCHIVE_OPERATION_FAILED,
          { description: `Archive operation failed: ${error.message}` },
        );
      }
    });
  }

  /**
   * Backup all data from a table
   * Backup table format: [original_table_name]_bkp_[YYYY_MM_DD_HH_mm_ss]
   */
  async backupTable(tableName: string): Promise<BackupResult> {
    this.validateTableName(tableName);
    await this.validateTableExists(tableName);

    const timestamp = this.generateTimestamp();
    const backupTableName = `${tableName}_bkp_${timestamp}`;

    try {
      // Create backup table with all data
      await this.dataSource.query(`
        CREATE TABLE ${backupTableName} AS 
        SELECT * FROM ${tableName}
      `);

      // Get record count
      const countResult = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM ${backupTableName}
      `);
      const recordCount = parseInt(countResult[0].count, 10);

      this.logger.log(
        `Created backup of ${tableName} with ${recordCount} records as ${backupTableName}`,
      );

      return {
        originalTableName: tableName,
        backupTableName,
        totalRecordCount: recordCount,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to backup table ${tableName}: ${error.message}`,
      );
      throw new HttpErrorException(
        DATABASE_ERROR_CODES.BACKUP_OPERATION_FAILED,
        { description: `Backup operation failed: ${error.message}` },
      );
    }
  }

  /**
   * Generate timestamp in format YYYY_MM_DD_HH_mm_ss
   */
  private generateTimestamp(): string {
    const now = new Date();
    return [
      now.getFullYear(),
      String(now.getMonth() + 1).padStart(2, '0'),
      String(now.getDate()).padStart(2, '0'),
      String(now.getHours()).padStart(2, '0'),
      String(now.getMinutes()).padStart(2, '0'),
      String(now.getSeconds()).padStart(2, '0'),
    ].join('_');
  }

  /**
   * Build WHERE clause for time range filtering
   */
  private buildTimeRangeWhereClause(options: ArchiveOptions): string {
    const { dateColumn } = options;
    return `${dateColumn} >= $1 AND ${dateColumn} <= $2`;
  }

  /**
   * Check if table exists in the database
   */
  private async validateTableExists(tableName: string): Promise<void> {
    try {
      const result = await this.dataSource.query<
        {
          table_exists: boolean;
        }[]
      >(
        `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        ) as table_exists
        `,
        [tableName],
      );

      const tableExists = result[0]?.table_exists;
      if (!tableExists) {
        this.logger.error(`Table '${tableName}' does not exist`);
        throw new HttpErrorException(DATABASE_ERROR_CODES.TABLE_NOT_FOUND, {
          description: `Table '${tableName}' does not exist`,
        });
      }
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }
      this.logger.error(
        `Error checking table existence for '${tableName}': ${error.message}`,
      );
      throw new HttpErrorException(
        DATABASE_ERROR_CODES.DATABASE_CONNECTION_ERROR,
        { description: `Failed to check table existence: ${error.message}` },
      );
    }
  }

  /**
   * Validate table name to prevent SQL injection
   */
  private validateTableName(tableName: string): void {
    if (!tableName || typeof tableName !== 'string') {
      throw new HttpErrorException(DATABASE_ERROR_CODES.INVALID_TABLE_NAME);
    }

    // Basic validation to prevent SQL injection
    const tableNameRegex = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
    if (!tableNameRegex.test(tableName)) {
      throw new HttpErrorException(DATABASE_ERROR_CODES.INVALID_TABLE_NAME);
    }
  }

  /**
   * Validate time range options
   */
  private validateTimeRange(timeRange: {
    startDate: Date;
    endDate: Date;
  }): void {
    if (!timeRange.startDate || !timeRange.endDate) {
      throw new HttpErrorException(DATABASE_ERROR_CODES.INVALID_TIME_RANGE);
    }

    if (timeRange.startDate >= timeRange.endDate) {
      throw new HttpErrorException(DATABASE_ERROR_CODES.INVALID_TIME_RANGE);
    }
  }
}
