export interface TimeRangeOptions {
  startDate: Date;
  endDate: Date;
}

export interface ArchiveOptions {
  timeRange: TimeRangeOptions;
  dateColumn: 'created_at' | 'updated_at';
}

export interface ArchiveResult {
  originalTableName: string;
  archiveTableName: string;
  archivedRecordCount: number;
  timestamp: Date;
}

export interface BackupResult {
  originalTableName: string;
  backupTableName: string;
  totalRecordCount: number;
  timestamp: Date;
}
