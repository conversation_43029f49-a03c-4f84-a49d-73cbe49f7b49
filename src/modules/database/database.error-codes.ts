import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '../../common/types';

const DATABASE_NAMESPACE = 'DATABASE';

type DatabaseErrorCodes =
  | 'ARCHIVE_OPERATION_FAILED'
  | 'BACKUP_OPERATION_FAILED'
  | 'INVALID_TABLE_NAME'
  | 'INVALID_TIME_RANGE'
  | 'TABLE_NOT_FOUND'
  | 'DATABASE_CONNECTION_ERROR';

/**
 * Error codes for database module
 */
export const DATABASE_ERROR_CODES: Record<DatabaseErrorCodes, ErrorCode> = {
  // Archive operation errors
  ARCHIVE_OPERATION_FAILED: {
    code: `${DATABASE_NAMESPACE}:10000`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Archive operation failed',
  },

  // Backup operation errors
  BACKUP_OPERATION_FAILED: {
    code: `${DATABASE_NAMESPACE}:10001`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Backup operation failed',
  },

  // Validation errors
  INVALID_TABLE_NAME: {
    code: `${DATABASE_NAMESPACE}:10002`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid table name provided',
  },
  INVALID_TIME_RANGE: {
    code: `${DATABASE_NAMESPACE}:10003`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid time range specified',
  },

  // Table operation errors
  TABLE_NOT_FOUND: {
    code: `${DATABASE_NAMESPACE}:10004`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Table not found',
  },

  // Connection errors
  DATABASE_CONNECTION_ERROR: {
    code: `${DATABASE_NAMESPACE}:10005`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Database connection error',
  },
};
