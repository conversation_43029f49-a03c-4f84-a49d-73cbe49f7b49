import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsOptional, IsString } from 'class-validator';
import * as ms from 'ms';

export type PasskeyConfigType = {
  rpName: string;
  rpId: string;
  challengeExpirationTime: ms.StringValue;
  challengePrefix: string;
};

class EnvironmentVariablesValidator {
  @IsString()
  PASSKEY_RP_NAME: string;

  @IsString()
  PASSKEY_RP_ID: string;

  @IsString()
  @IsOptional()
  PASSKEY_CHALLENGE_EXPIRATION_TIME: string;

  @IsString()
  @IsOptional()
  PASSKEY_CHALLENGE_PREFIX: string;
}

export const passkeyConfig = registerAs<PasskeyConfigType>('passkey', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    rpName: process.env.PASSKEY_RP_NAME || 'Passkey Auth Demo',
    rpId: process.env.PASSKEY_RP_ID || 'localhost',
    challengeExpirationTime:
      (process.env.PASSKEY_CHALLENGE_EXPIRATION_TIME as ms.StringValue) || '5m',
    challengePrefix: process.env.PASSKEY_CHALLENGE_PREFIX || 'passkey_',
  };
});
