import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class ArchiveResponseDto {
  @ApiProperty({
    description: 'Original table name',
    example: 'users',
  })
  @Expose()
  originalTableName: string;

  @ApiProperty({
    description: 'Name of the created archive table',
    example: 'users_archive_2024_08_22_10_30_45',
  })
  @Expose()
  archiveTableName: string;

  @ApiProperty({
    description: 'Number of records archived',
    example: 1500,
  })
  @Expose()
  archivedRecordCount: number;

  @ApiProperty({
    description: 'Timestamp when the archive was created',
    example: '2024-08-22T10:30:45.123Z',
  })
  @Expose()
  timestamp: Date;
}
