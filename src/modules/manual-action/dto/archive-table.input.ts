import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsDateString, IsEnum, IsNotEmpty, IsString } from 'class-validator';

export class ArchiveTableInput {
  @ApiProperty({
    description: 'Name of the table to archive',
    example: 'users',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  tableName: string;

  @ApiProperty({
    description: 'Start date for the archive time range',
    example: '2023-01-01T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  @IsDateString()
  @Expose()
  startDate: string;

  @ApiProperty({
    description: 'End date for the archive time range',
    example: '2023-12-31T23:59:59.999Z',
    type: String,
    format: 'date-time',
  })
  @IsDateString()
  @Expose()
  endDate: string;

  @ApiProperty({
    description: 'Column name to use for date filtering',
    example: 'created_at',
    enum: ['created_at', 'updated_at'],
  })
  @IsEnum(['created_at', 'updated_at'])
  @Expose()
  dateColumn: 'created_at' | 'updated_at';
}
