import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class BackupResponseDto {
  @ApiProperty({
    description: 'Original table name',
    example: 'users',
  })
  @Expose()
  originalTableName: string;

  @ApiProperty({
    description: 'Name of the created backup table',
    example: 'users_bkp_2024_08_22_10_30_45',
  })
  @Expose()
  backupTableName: string;

  @ApiProperty({
    description: 'Total number of records backed up',
    example: 5000,
  })
  totalRecordCount: number;

  @ApiProperty({
    description: 'Timestamp when the backup was created',
    example: '2024-08-22T10:30:45.123Z',
  })
  @Expose()
  timestamp: Date;
}
