import validateConfig from '@common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsString } from 'class-validator';

export type ManualActionConfigType = {
  username: string;
  password: string;
};

class EnvironmentVariablesValidator {
  @IsString()
  MANUAL_ACTION_USERNAME: string;

  @IsString()
  MANUAL_ACTION_PASSWORD: string;
}

export const manualActionConfig = registerAs<ManualActionConfigType>(
  'manualAction',
  () => {
    validateConfig(process.env, EnvironmentVariablesValidator);
    return {
      username: process.env.MANUAL_ACTION_USERNAME ?? '',
      password: process.env.MANUAL_ACTION_PASSWORD ?? '',
    };
  },
);
