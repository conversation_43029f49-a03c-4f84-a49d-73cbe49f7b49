import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiBasicAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ArchiveResponseDto } from './dto/archive-response.dto';
import { ArchiveTableInput } from './dto/archive-table.input';
import { BackupResponseDto } from './dto/backup-response.dto';
import { BackupTableInput } from './dto/backup-table.input';
import { ManualActionService } from './manual-action.service';

@ApiTags('Manual Actions')
@ApiBasicAuth()
@UseGuards(AuthGuard('basic'))
@Controller({
  path: 'manual-actions',
  version: '1',
})
export class ManualActionController {
  private readonly logger = new Logger(ManualActionController.name);

  constructor(private readonly manualActionService: ManualActionService) {}

  @Post('archive-table')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Archive table data',
    description:
      'Archive data from a table based on time range criteria. ' +
      'Creates a new archive table with format: [original_table_name]_archive_[YYYY_MM_DD_HH_mm_ss]',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Table data archived successfully',
    type: ArchiveResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Table not found',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Archive operation failed',
  })
  async archiveTable(
    @Body() archiveInput: ArchiveTableInput,
  ): Promise<ArchiveResponseDto> {
    this.logger.log(
      `Received archive request for table: ${archiveInput.tableName}`,
    );
    return await this.manualActionService.archiveTable(archiveInput);
  }

  @Post('backup-table')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Backup table data',
    description:
      'Create a complete backup of a table. ' +
      'Creates a new backup table with format: [original_table_name]_bkp_[YYYY_MM_DD_HH_mm_ss]',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Table backed up successfully',
    type: BackupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Table not found',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Backup operation failed',
  })
  async backupTable(
    @Body() backupInput: BackupTableInput,
  ): Promise<BackupResponseDto> {
    this.logger.log(
      `Received backup request for table: ${backupInput.tableName}`,
    );
    return await this.manualActionService.backupTable(backupInput);
  }
}
