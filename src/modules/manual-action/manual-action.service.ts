import { Injectable, Logger } from '@nestjs/common';
import {
  ArchiveOptions,
  ArchiveResult,
  BackupResult,
} from '../database/database.interfaces';
import { DatabaseService } from '../database/database.service';
import { ArchiveTableInput } from './dto/archive-table.input';
import { BackupTableInput } from './dto/backup-table.input';

@Injectable()
export class ManualActionService {
  private readonly logger = new Logger(ManualActionService.name);

  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * Archive data from a table based on time range criteria
   */
  async archiveTable(archiveInput: ArchiveTableInput): Promise<ArchiveResult> {
    this.logger.log(
      `Starting archive operation for table: ${archiveInput.tableName}`,
    );

    const archiveOptions: ArchiveOptions = {
      timeRange: {
        startDate: new Date(archiveInput.startDate),
        endDate: new Date(archiveInput.endDate),
      },
      dateColumn: archiveInput.dateColumn,
    };

    const result = await this.databaseService.archiveData(
      archiveInput.tableName,
      archiveOptions,
    );

    this.logger.log(
      `Archive operation completed for table: ${archiveInput.tableName}. ` +
        `Created archive table: ${result.archiveTableName} with ${result.archivedRecordCount} records`,
    );

    return result;
  }

  /**
   * Create a backup of a table
   */
  async backupTable(backupInput: BackupTableInput): Promise<BackupResult> {
    this.logger.log(
      `Starting backup operation for table: ${backupInput.tableName}`,
    );

    const result = await this.databaseService.backupTable(
      backupInput.tableName,
    );

    this.logger.log(
      `Backup operation completed for table: ${backupInput.tableName}. ` +
        `Created backup table: ${result.backupTableName} with ${result.totalRecordCount} records`,
    );

    return result;
  }
}
