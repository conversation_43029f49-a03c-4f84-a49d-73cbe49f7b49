import {
  cacheConfig,
  CacheConfigType,
  CacheStoreEnum,
} from '@app/modules/cache-store/cache.config';
import KeyvRedis from '@keyv/redis';
import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Keyv, KeyvCacheableMemory } from 'cacheable';
import * as ms from 'ms';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [cacheConfig],
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (
        configService: ConfigService<{
          cache: CacheConfigType;
        }>,
      ) => {
        const cacheConfig = configService.getOrThrow('cache', {
          infer: true,
        });
        const stores: Keyv[] = [];
        for (const store of cacheConfig.stores) {
          if (store === CacheStoreEnum.REDIS) {
            if (!cacheConfig.redis) {
              throw new Error('Cache Store Redis configuration is required');
            }
            stores.push(
              new Keyv({
                store: new KeyvRedis({
                  socket: {
                    host: cacheConfig.redis?.host,
                    port: cacheConfig.redis?.port,
                  },
                  password: cacheConfig.redis?.password,
                  username: cacheConfig.redis?.username,
                  database: cacheConfig.redis?.database,
                }),
              }),
            );
          } else if (store === CacheStoreEnum.MEMORY) {
            stores.push(
              new Keyv({
                store: new KeyvCacheableMemory({
                  ttl: cacheConfig.ttl,
                  lruSize: cacheConfig.lru,
                }),
              }),
            );
          }
        }
        return {
          ttl: ms(cacheConfig.ttl),
          stores,
        };
      },
    }),
  ],
  exports: [CacheModule],
})
export class CacheStoreModule {}
