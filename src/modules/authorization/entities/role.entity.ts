import { AuditEntity } from '@app/modules/user/entities/audit.entity';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { RoleType } from '../enums/role-type.enum';
import { RolePolicyEntity } from './role-policy.entity';
import { UserRoleEntity } from './user-role.entity';

@Entity('roles')
@Index(['name'], { unique: true })
export class RoleEntity extends AuditEntity {
  @Column({ name: 'name', unique: true, length: 100 })
  name: string;

  @Column({ name: 'description', nullable: true, length: 500 })
  description?: string;

  @Column({
    name: 'type',
    type: 'enum',
    enum: RoleType,
    default: RoleType.CUSTOM,
  })
  type: RoleType;

  @Column({ name: 'is_built_in', default: false })
  isBuiltIn: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({
    name: 'permissions',
    type: 'text',
    array: true,
    default: () => 'ARRAY[]::text[]',
  })
  permissions: string[];

  @Column({ name: 'priority', type: 'integer', default: 0 })
  priority: number;

  @OneToMany(() => UserRoleEntity, (userRole) => userRole.role)
  userRoles: UserRoleEntity[];

  @OneToMany(() => RolePolicyEntity, (rolePolicy) => rolePolicy.role)
  rolePolicies: RolePolicyEntity[];

  // Virtual properties for easier access
  get users(): Promise<UserRoleEntity[]> {
    return Promise.resolve(this.userRoles);
  }

  get policies(): Promise<RolePolicyEntity[]> {
    return Promise.resolve(this.rolePolicies);
  }
}
