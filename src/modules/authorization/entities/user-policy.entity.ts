import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../../common/entities/base.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { PolicyEntity } from './policy.entity';

@Entity('user_policies')
@Index(['userId', 'policyId'], { unique: true })
@Index(['userId'])
@Index(['policyId'])
@Index(['expiresAt'])
export class UserPolicyEntity extends CustomBaseEntity {
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'policy_id', type: 'uuid' })
  policyId: string;

  @Column({
    name: 'attached_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  attachedAt: Date;

  @Column({ name: 'attached_by', type: 'uuid', nullable: true })
  attachedBy?: string;

  @Column({
    name: 'expires_at',
    type: 'timestamp',
    nullable: true,
    comment: 'Optional expiration date for temporary policy assignments',
  })
  expiresAt?: Date;

  @Column({
    name: 'is_active',
    default: true,
    comment: 'Whether this policy attachment is currently active',
  })
  isActive: boolean;

  @Column({
    name: 'conditions',
    type: 'jsonb',
    nullable: true,
    comment: 'Optional conditions for when this policy is applied',
  })
  conditions?: Record<string, any>;

  @Column({
    name: 'priority_override',
    type: 'integer',
    nullable: true,
    comment: 'Override priority for this specific user-policy attachment',
  })
  priorityOverride?: number;

  @Column({
    name: 'reason',
    nullable: true,
    comment:
      'Reason for direct policy assignment (e.g., temporary access, exception)',
  })
  reason?: string;

  // Relationships
  @ManyToOne(() => UserEntity, {
    onDelete: 'CASCADE',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => PolicyEntity, (policy) => policy.userPolicies, {
    onDelete: 'CASCADE',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'policy_id' })
  policy: PolicyEntity;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'attached_by' })
  attachedByUser?: UserEntity;

  // Helper methods
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return this.expiresAt < new Date();
  }

  isCurrentlyActive(): boolean {
    return (
      this.isActive && !this.isExpired() && this.policy?.isActive !== false
    );
  }

  getEffectivePriority(): number {
    return this.priorityOverride ?? this.policy?.priority ?? 0;
  }

  daysUntilExpiration(): number | null {
    if (!this.expiresAt) return null;
    const now = new Date();
    const diffTime = this.expiresAt.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
