import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../../common/entities/base.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { RoleEntity } from './role.entity';

@Entity('user_roles')
@Index(['userId', 'roleId'], { unique: true })
@Index(['userId'])
@Index(['roleId'])
@Index(['expiresAt'])
export class UserRoleEntity extends CustomBaseEntity {
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'role_id', type: 'uuid' })
  roleId: string;

  @Column({
    name: 'assigned_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  assignedAt: Date;

  @Column({ name: 'assigned_by', type: 'uuid', nullable: true })
  assignedBy?: string;

  @Column({
    name: 'expires_at',
    type: 'timestamp',
    nullable: true,
    comment: 'Optional expiration date for temporary role assignments',
  })
  expiresAt?: Date;

  @Column({
    name: 'is_active',
    default: true,
    comment: 'Whether this role assignment is currently active',
  })
  isActive: boolean;

  @Column({
    name: 'conditions',
    type: 'jsonb',
    nullable: true,
    comment:
      'Optional conditions for when this role is active (e.g., IP ranges, time windows)',
  })
  conditions?: Record<string, any>;

  // Relationships
  @ManyToOne(() => UserEntity, {
    onDelete: 'CASCADE',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => RoleEntity, (role) => role.userRoles, {
    onDelete: 'CASCADE',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'role_id' })
  role: RoleEntity;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'assigned_by' })
  assignedByUser?: UserEntity;

  // Helper methods
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return this.expiresAt < new Date();
  }

  isCurrentlyActive(): boolean {
    return this.isActive && !this.isExpired();
  }

  daysUntilExpiration(): number | null {
    if (!this.expiresAt) return null;
    const now = new Date();
    const diffTime = this.expiresAt.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
