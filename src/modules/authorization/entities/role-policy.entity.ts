import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../../common/entities/base.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { PolicyEntity } from './policy.entity';
import { RoleEntity } from './role.entity';

@Entity('role_policies')
@Index(['roleId', 'policyId'], { unique: true })
@Index(['roleId'])
@Index(['policyId'])
export class RolePolicyEntity extends CustomBaseEntity {
  @Column({ name: 'role_id', type: 'uuid' })
  roleId: string;

  @Column({ name: 'policy_id', type: 'uuid' })
  policyId: string;

  @Column({
    name: 'attached_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  attachedAt: Date;

  @Column({ name: 'attached_by', type: 'uuid', nullable: true })
  attachedBy?: string;

  @Column({
    name: 'is_active',
    default: true,
    comment: 'Whether this policy attachment is currently active',
  })
  isActive: boolean;

  @Column({
    name: 'conditions',
    type: 'jsonb',
    nullable: true,
    comment:
      'Optional conditions for when this policy is applied (e.g., time windows, context)',
  })
  conditions?: Record<string, any>;

  @Column({
    name: 'priority_override',
    type: 'integer',
    nullable: true,
    comment: 'Override priority for this specific role-policy attachment',
  })
  priorityOverride?: number;

  // Relationships
  @ManyToOne(() => RoleEntity, (role) => role.rolePolicies, {
    onDelete: 'CASCADE',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'role_id' })
  role: RoleEntity;

  @ManyToOne(() => PolicyEntity, (policy) => policy.rolePolicies, {
    onDelete: 'CASCADE',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'policy_id' })
  policy: PolicyEntity;

  @ManyToOne(() => UserEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'attached_by' })
  attachedByUser?: UserEntity;

  // Helper methods
  getEffectivePriority(): number {
    return this.priorityOverride ?? this.policy?.priority ?? 0;
  }

  isCurrentlyActive(): boolean {
    return (
      this.isActive &&
      this.policy?.isActive !== false &&
      this.role?.isActive !== false
    );
  }
}
