import { AuditEntity } from '@app/modules/user/entities/audit.entity';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { PolicyEffect } from '../enums/policy-effect.enum';
import { PolicyDocument } from '../interfaces/policy.interface';
import { RolePolicyEntity } from './role-policy.entity';
import { UserPolicyEntity } from './user-policy.entity';

@Entity('policies')
@Index(['name'], { unique: true })
@Index(['version'])
export class PolicyEntity extends AuditEntity {
  @Column({ name: 'name', unique: true, length: 128 })
  name: string;

  @Column({ name: 'description', nullable: true, length: 1000 })
  description?: string;

  @Column({ name: 'version', length: 32, default: '2023-11-01' })
  version: string;

  @Column({
    name: 'document',
    type: 'jsonb',
    comment: 'AWS-style policy document in JSON format',
  })
  document: PolicyDocument;

  @Column({
    name: 'effect',
    type: 'enum',
    enum: PolicyEffect,
    default: PolicyEffect.ALLOW,
  })
  effect: PolicyEffect;

  @Column({
    name: 'actions',
    type: 'text',
    array: true,
    default: () => 'ARRAY[]::text[]',
    comment: 'Cached actions from policy document for quick filtering',
  })
  actions: string[];

  @Column({
    name: 'resources',
    type: 'text',
    array: true,
    default: () => 'ARRAY[]::text[]',
    comment: 'Cached resources from policy document for quick filtering',
  })
  resources: string[];

  @Column({
    name: 'principals',
    type: 'text',
    array: true,
    default: () => 'ARRAY[]::text[]',
    nullable: true,
    comment: 'Cached principals from policy document',
  })
  principals?: string[];

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_built_in', default: false })
  isBuiltIn: boolean;

  @Column({ name: 'priority', type: 'integer', default: 0 })
  priority: number;

  @Column({
    name: 'tags',
    type: 'jsonb',
    nullable: true,
    comment: 'Additional metadata tags for policy organization',
  })
  tags?: Record<string, string>;

  @OneToMany(() => RolePolicyEntity, (rolePolicy) => rolePolicy.policy)
  rolePolicies: RolePolicyEntity[];

  @OneToMany(() => UserPolicyEntity, (userPolicy) => userPolicy.policy)
  userPolicies: UserPolicyEntity[];

  // Helper methods
  hasAction(action: string): boolean {
    return this.actions.some((a) => {
      if (a === '*' || a === action) return true;
      if (a.includes('*')) {
        const pattern = a.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(action);
      }
      return false;
    });
  }

  hasResource(resource: string): boolean {
    return this.resources.some((r) => {
      if (r === '*' || r === resource) return true;
      if (r.includes('*')) {
        const pattern = r.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(resource);
      }
      return false;
    });
  }
}
