import { Injectable } from '@nestjs/common';
import { ResourceHandler } from '../interfaces/policy.interface';

/**
 * Default resource handler for standard resource patterns
 */
@Injectable()
export class DefaultResourceHandler implements ResourceHandler {
  getResourceArn(request: any, pattern: string): string {
    return this.interpolateVariables(pattern, request);
  }

  extractAttributes(request: any): Record<string, any> {
    return {
      'request:method': request.method,
      'request:path': request.path || request.url,
      'request:ipAddress': request.ip || request.connection?.remoteAddress,
      'request:userAgent': request.headers?.['user-agent'],
      'request:timestamp': new Date().toISOString(),
      'request:userId': request.user?.id,
      'request:userEmail': request.user?.email,
      ...this.extractParamsAndQuery(request),
    };
  }

  private interpolateVariables(pattern: string, request: any): string {
    return pattern.replace(/\$\{([^}]+)\}/g, (match, variable) => {
      return this.getVariableValue(variable, request) || match;
    });
  }

  private getVariableValue(variable: string, request: any): string | undefined {
    const parts = variable.split('.');
    let value = request;

    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value ? String(value) : undefined;
  }

  private extractParamsAndQuery(request: any): Record<string, any> {
    const attributes: Record<string, any> = {};

    // Extract route parameters
    if (request.params) {
      Object.entries(request.params).forEach(([key, value]) => {
        attributes[`params.${key}`] = value;
      });
    }

    // Extract query parameters
    if (request.query) {
      Object.entries(request.query).forEach(([key, value]) => {
        attributes[`query.${key}`] = value;
      });
    }

    return attributes;
  }
}

/**
 * Registry for custom resource handlers
 */
@Injectable()
export class ResourceHandlerRegistry {
  private handlers = new Map<string, ResourceHandler>();
  private defaultHandler: ResourceHandler;

  constructor() {
    this.defaultHandler = new DefaultResourceHandler();
  }

  /**
   * Register a custom resource handler for a specific resource type
   */
  registerHandler(resourceType: string, handler: ResourceHandler): void {
    this.handlers.set(resourceType, handler);
  }

  /**
   * Get resource handler for a specific resource type
   */
  getHandler(resourceType: string): ResourceHandler {
    return this.handlers.get(resourceType) || this.defaultHandler;
  }

  /**
   * Build resource ARN using appropriate handler
   */
  buildResourceArn(pattern: string, request: any): string {
    const resourceType = this.extractResourceType(pattern);
    const handler = this.getHandler(resourceType);
    return handler.getResourceArn(request, pattern);
  }

  /**
   * Extract resource attributes using appropriate handler
   */
  extractResourceAttributes(
    pattern: string,
    request: any,
  ): Record<string, any> {
    const resourceType = this.extractResourceType(pattern);
    const handler = this.getHandler(resourceType);
    return handler.extractAttributes(request);
  }

  /**
   * Extract resource type from ARN pattern
   */
  private extractResourceType(pattern: string): string {
    // Extract resource type from ARN: arn:app:service:region:account:resource
    const arnParts = pattern.split(':');
    if (arnParts.length >= 3) {
      return arnParts[2]; // service part
    }
    return 'default';
  }
}

/**
 * Specialized resource handler for user resources
 */
@Injectable()
export class UserResourceHandler implements ResourceHandler {
  getResourceArn(request: any, pattern: string): string {
    let arn = pattern;

    // Replace user ID variables
    if (request.params?.userId) {
      arn = arn.replace(/\$\{params\.userId\}/g, request.params.userId);
    }
    if (request.params?.id) {
      arn = arn.replace(/\$\{params\.id\}/g, request.params.id);
    }
    if (request.user?.id) {
      arn = arn.replace(/\$\{user\.id\}/g, request.user.id);
    }

    return arn;
  }

  extractAttributes(request: any): Record<string, any> {
    const baseAttributes = new DefaultResourceHandler().extractAttributes(
      request,
    );

    return {
      ...baseAttributes,
      'user:id': request.user?.id,
      'user:email': request.user?.email,
      'user:roles': request.user?.roles || [],
      'user:isActive': request.user?.isActive,
      'user:isEmailVerified': request.user?.isEmailVerified,
      'resource:ownerId': request.params?.userId || request.params?.id,
      'resource:type': 'user',
    };
  }

  async validateAccess(context: any): Promise<boolean> {
    // Custom validation for user resources
    const { user, resource } = context;

    // Users can always access their own resources
    if (resource.includes(`user/${user.id}`)) {
      return true;
    }

    // Additional business logic can be added here
    return false;
  }
}

/**
 * Specialized resource handler for document/file resources
 */
@Injectable()
export class DocumentResourceHandler implements ResourceHandler {
  getResourceArn(request: any, pattern: string): string {
    let arn = pattern;

    // Replace document-specific variables
    const replacements = [
      {
        pattern: /\$\{params\.documentId\}/g,
        value: request.params?.documentId,
      },
      { pattern: /\$\{params\.fileId\}/g, value: request.params?.fileId },
      { pattern: /\$\{params\.ownerId\}/g, value: request.params?.ownerId },
      { pattern: /\$\{user\.id\}/g, value: request.user?.id },
    ];

    replacements.forEach(({ pattern, value }) => {
      if (value) {
        arn = arn.replace(pattern, String(value));
      }
    });

    return arn;
  }

  extractAttributes(request: any): Record<string, any> {
    const baseAttributes = new DefaultResourceHandler().extractAttributes(
      request,
    );

    return {
      ...baseAttributes,
      'document:id': request.params?.documentId || request.params?.fileId,
      'document:ownerId': request.params?.ownerId,
      'document:type': request.body?.type || request.query?.type,
      'document:visibility': request.body?.visibility || 'private',
      'resource:type': 'document',
    };
  }
}

/**
 * Specialized resource handler for administrative resources
 */
@Injectable()
export class AdminResourceHandler implements ResourceHandler {
  getResourceArn(request: any, pattern: string): string {
    // Admin resources typically don't need variable substitution
    return pattern;
  }

  extractAttributes(request: any): Record<string, any> {
    const baseAttributes = new DefaultResourceHandler().extractAttributes(
      request,
    );

    return {
      ...baseAttributes,
      'admin:operation': this.extractOperationType(request),
      'admin:targetResource': request.params?.resourceType,
      'admin:targetId': request.params?.id,
      'resource:type': 'admin',
    };
  }

  private extractOperationType(request: any): string {
    const method = request.method?.toUpperCase();
    const path = request.path || request.url;

    if (method === 'GET' && path.includes('/list')) return 'list';
    if (method === 'GET') return 'read';
    if (method === 'POST') return 'create';
    if (method === 'PUT' || method === 'PATCH') return 'update';
    if (method === 'DELETE') return 'delete';

    return 'unknown';
  }
}
