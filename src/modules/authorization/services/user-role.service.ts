import { HttpErrorException } from '@common/exception/http-error.exception';
import { Injectable, Logger } from '@nestjs/common';
import { AUTHORIZATION_ERROR_CODES } from '../authorization.error-codes';
import {
  DirectPolicyDto,
  PolicyPermissionsDto,
  RecentActivityDto,
  RolePermissionsDto,
  RolePolicyDto,
  RoleSummaryDto,
  UserAccessSummaryDto,
  UserAccessSummaryResponseDto,
  UserCapabilitiesDto,
  UserPermissionsResponseDto,
  UserPoliciesResponseDto,
  UserRoleDto,
  UserRolesResponseDto,
} from '../dto/user-role.dto';
import { PolicyService } from './policy.service';
import { RoleService } from './role.service';

/**
 * Service for managing user role operations and access information
 */
@Injectable()
export class UserRoleService {
  private readonly logger = new Logger(UserRoleService.name);

  constructor(
    private readonly roleService: RoleService,
    private readonly policyService: PolicyService,
  ) {}

  /**
   * Get all roles assigned to a user
   * @param userId - User ID
   * @returns Promise<UserRolesResponseDto> - User roles information
   */
  async getUserRoles(userId: string): Promise<UserRolesResponseDto> {
    try {
      const roles = await this.roleService.getUserRoles(userId);

      const userRoles: UserRoleDto[] = roles.map((role) => ({
        id: role.id,
        name: role.name,
        description: role.description,
        type: role.type,
        permissions: role.permissions,
        isActive: role.isActive,
        priority: role.priority,
      }));

      return {
        userId,
        roles: userRoles,
        totalRoles: userRoles.length,
      };
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(`Failed to get user roles for user: ${userId}`, error);
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.EVALUATION_FAILED,
        {
          description: `Failed to retrieve roles for user '${userId}'`,
        },
      );
    }
  }

  /**
   * Get effective permissions for a user from roles and policies
   * @param userId - User ID
   * @returns Promise<UserPermissionsResponseDto> - User permissions information
   */
  async getUserPermissions(
    userId: string,
  ): Promise<UserPermissionsResponseDto> {
    try {
      // Get roles and their permissions
      const roles = await this.roleService.getUserRoles(userId);
      const fromRoles: RolePermissionsDto[] = roles.map((role) => ({
        roleName: role.name,
        permissions: role.permissions,
      }));

      // Get policies and their permissions
      const { directPolicies, rolePolicies } =
        await this.policyService.getUserPolicies(userId);

      const fromPolicies: PolicyPermissionsDto[] = [
        ...directPolicies.map((policy) => ({
          policyName: policy.name,
          actions: policy.actions,
          resources: policy.resources,
          effect: policy.effect,
          source: 'direct',
        })),
        ...rolePolicies.flatMap((rp) =>
          rp.policies.map((policy) => ({
            policyName: policy.name,
            actions: policy.actions,
            resources: policy.resources,
            effect: policy.effect,
            source: `role:${rp.role}`,
          })),
        ),
      ];

      // Calculate effective permissions
      const effectivePermissions = this.calculateEffectivePermissions(
        fromRoles,
        fromPolicies,
      );

      return {
        userId,
        permissions: {
          fromRoles,
          fromPolicies,
          effectivePermissions,
        },
        totalPermissions: effectivePermissions.length,
      };
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(
        `Failed to get user permissions for user: ${userId}`,
        error,
      );
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.EVALUATION_FAILED,
        {
          description: `Failed to retrieve permissions for user '${userId}'`,
        },
      );
    }
  }

  /**
   * Get all policies affecting a user (direct and role-based)
   * @param userId - User ID
   * @returns Promise<UserPoliciesResponseDto> - User policies information
   */
  async getUserPolicies(userId: string): Promise<UserPoliciesResponseDto> {
    try {
      const { directPolicies, rolePolicies } =
        await this.policyService.getUserPolicies(userId);

      const directPoliciesResponse: DirectPolicyDto[] = directPolicies.map(
        (policy) => ({
          id: policy.id,
          name: policy.name,
          description: policy.description,
          effect: policy.effect,
          actions: policy.actions,
          resources: policy.resources,
          isActive: policy.isActive,
          priority: policy.priority,
        }),
      );

      const rolePoliciesResponse: RolePolicyDto[] = rolePolicies.map((rp) => ({
        roleName: rp.role,
        policies: rp.policies.map((policy) => ({
          id: policy.id,
          name: policy.name,
          description: policy.description,
          effect: policy.effect,
          actions: policy.actions,
          resources: policy.resources,
          isActive: policy.isActive,
          priority: policy.priority,
        })),
      }));

      const totalPolicies =
        directPolicies.length +
        rolePolicies.reduce((sum, rp) => sum + rp.policies.length, 0);

      return {
        userId,
        directPolicies: directPoliciesResponse,
        rolePolicies: rolePoliciesResponse,
        totalPolicies,
      };
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(
        `Failed to get user policies for user: ${userId}`,
        error,
      );
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.EVALUATION_FAILED,
        {
          description: `Failed to retrieve policies for user '${userId}'`,
        },
      );
    }
  }

  /**
   * Get comprehensive access summary for a user
   * @param userId - User ID
   * @returns Promise<UserAccessSummaryResponseDto> - User access summary
   */
  async getUserAccessSummary(
    userId: string,
  ): Promise<UserAccessSummaryResponseDto> {
    try {
      const [roles, policies, isAdmin] = await Promise.all([
        this.roleService.getUserRoles(userId),
        this.policyService.getUserPolicies(userId),
        this.roleService.userHasRole(userId, 'admin'),
      ]);

      // Calculate effective permissions
      const fromRoles: RolePermissionsDto[] = roles.map((role) => ({
        roleName: role.name,
        permissions: role.permissions,
      }));

      const fromPolicies: PolicyPermissionsDto[] = [
        ...policies.directPolicies.map((policy) => ({
          policyName: policy.name,
          actions: policy.actions,
          resources: policy.resources,
          effect: policy.effect,
        })),
        ...policies.rolePolicies.flatMap((rp) =>
          rp.policies.map((policy) => ({
            policyName: policy.name,
            actions: policy.actions,
            resources: policy.resources,
            effect: policy.effect,
          })),
        ),
      ];

      const effectivePermissions = this.calculateEffectivePermissions(
        fromRoles,
        fromPolicies,
      );

      // Identify high-level capabilities
      const capabilities: UserCapabilitiesDto = {
        canManageUsers:
          effectivePermissions.includes('user:*') ||
          effectivePermissions.includes('user:manage') ||
          isAdmin,
        canManageRoles:
          effectivePermissions.includes('role:*') ||
          effectivePermissions.includes('role:manage') ||
          isAdmin,
        canManagePolicies:
          effectivePermissions.includes('policy:*') ||
          effectivePermissions.includes('policy:manage') ||
          isAdmin,
        isAdministrator: isAdmin,
        hasElevatedAccess:
          isAdmin ||
          effectivePermissions.includes('*') ||
          effectivePermissions.some((p) => p.includes('*')),
      };

      const summary: UserAccessSummaryDto = {
        rolesCount: roles.length,
        directPoliciesCount: policies.directPolicies.length,
        rolePoliciesCount: policies.rolePolicies.reduce(
          (sum, rp) => sum + rp.policies.length,
          0,
        ),
        totalPermissions: effectivePermissions.length,
        capabilities,
      };

      const roleSummaries: RoleSummaryDto[] = roles.map((role) => ({
        name: role.name,
        type: role.type,
        permissionsCount: role.permissions.length,
      }));

      const recentActivity: RecentActivityDto = {
        // This could be expanded to include recent role assignments, policy attachments, etc.
        lastRoleAssignment: null,
        lastPolicyAttachment: null,
      };

      return {
        userId,
        summary,
        roles: roleSummaries,
        recentActivity,
      };
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(
        `Failed to get user access summary for user: ${userId}`,
        error,
      );
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.EVALUATION_FAILED,
        {
          description: `Failed to retrieve access summary for user '${userId}'`,
        },
      );
    }
  }

  /**
   * Calculate effective permissions from roles and policies
   * @private
   * @param fromRoles - Role permissions
   * @param fromPolicies - Policy permissions
   * @returns string[] - Array of effective permissions
   */
  private calculateEffectivePermissions(
    fromRoles: RolePermissionsDto[],
    fromPolicies: PolicyPermissionsDto[],
  ): string[] {
    const effectivePermissions = new Set<string>();

    // Add role permissions
    fromRoles.forEach((rolePermissions) => {
      rolePermissions.permissions.forEach((permission) => {
        effectivePermissions.add(permission);
      });
    });

    // Add policy actions as permissions (only for ALLOW policies)
    fromPolicies.forEach((policy) => {
      if (policy.effect === 'Allow') {
        policy.actions.forEach((action) => {
          effectivePermissions.add(action);
        });
      }
    });

    return Array.from(effectivePermissions);
  }
}
