import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import {
  PolicyEntity,
  RoleEntity,
  RolePolicyEntity,
  UserRoleEntity,
} from '../entities';
import { RoleType } from '../enums/role-type.enum';
import {
  AssignRoleInput,
  BulkRoleOperation,
  CreateRoleInput,
  RolePermissionsSummary,
  RoleQueryParams,
  RoleWithUsers,
  UpdateRoleInput,
  UserRoleAssignment,
} from '../interfaces/role.interface';
import { AUTHORIZATION_ERROR_CODES } from '../authorization.error-codes';

@Injectable()
export class RoleService {
  private readonly logger = new Logger(RoleService.name);

  constructor(
    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,
    @InjectRepository(UserRoleEntity)
    private readonly userRoleRepository: Repository<UserRoleEntity>,
    @InjectRepository(RolePolicyEntity)
    private readonly rolePolicyRepository: Repository<RolePolicyEntity>,
    @InjectRepository(PolicyEntity)
    private readonly policyRepository: Repository<PolicyEntity>,
  ) {}

  /**
   * Create a new role
   */
  async createRole(
    input: CreateRoleInput,
    createdBy?: string,
  ): Promise<RoleEntity> {
    this.logger.log(`Creating role: ${input.name}`);

    // Check if role name already exists
    const existingRole = await this.roleRepository.findOne({
      where: { name: input.name },
    });

    if (existingRole) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.ROLE_ALREADY_EXISTS,
      );
    }

    // Validate permissions if provided
    if (input.permissions) {
      this.validatePermissions(input.permissions);
    }

    const role = this.roleRepository.create({
      name: input.name.toLowerCase().trim(),
      description: input.description,
      type: input.type,
      permissions: input.permissions || [],
      isActive: input.isActive ?? true,
      priority: input.priority ?? 0,
      isBuiltIn: input.type === RoleType.SYSTEM,
    });

    const savedRole = await this.roleRepository.save(role);

    this.logger.log(
      `Role created successfully: ${savedRole.name} (${savedRole.id})`,
    );
    return savedRole;
  }

  /**
   * Update an existing role
   */
  async updateRole(
    roleId: string,
    input: UpdateRoleInput,
    updatedBy?: string,
  ): Promise<RoleEntity> {
    this.logger.log(`Updating role: ${roleId}`);

    const role = await this.findRoleById(roleId);

    // Prevent modification of built-in roles
    if (role.isBuiltIn) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.ROLE_CANNOT_MODIFY_BUILTIN,
      );
    }

    // Check for name conflicts if name is being changed
    if (input.name && input.name !== role.name) {
      const existingRole = await this.roleRepository.findOne({
        where: { name: input.name, id: Not(roleId) },
      });

      if (existingRole) {
        throw new HttpErrorException(
          AUTHORIZATION_ERROR_CODES.ROLE_ALREADY_EXISTS,
        );
      }
    }

    // Validate permissions if provided
    if (input.permissions) {
      this.validatePermissions(input.permissions);
    }

    // Update role properties
    Object.assign(role, {
      ...(input.name && { name: input.name.toLowerCase().trim() }),
      ...(input.description !== undefined && {
        description: input.description,
      }),
      ...(input.permissions && { permissions: input.permissions }),
      ...(input.isActive !== undefined && { isActive: input.isActive }),
      ...(input.priority !== undefined && { priority: input.priority }),
    });

    const updatedRole = await this.roleRepository.save(role);

    this.logger.log(
      `Role updated successfully: ${updatedRole.name} (${updatedRole.id})`,
    );
    return updatedRole;
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: string, deletedBy?: string): Promise<void> {
    this.logger.log(`Deleting role: ${roleId}`);

    const role = await this.findRoleById(roleId);

    // Prevent deletion of built-in roles
    if (role.isBuiltIn) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.ROLE_CANNOT_DELETE_BUILTIN,
      );
    }

    // Check for active user assignments
    const activeAssignments = await this.userRoleRepository.count({
      where: { roleId, isActive: true },
    });

    if (activeAssignments > 0) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.ROLE_HAS_ACTIVE_USERS,
      );
    }

    // Delete role and related data
    await this.roleRepository.manager.transaction(async (manager) => {
      // Delete role policy attachments
      await manager.delete(RolePolicyEntity, { roleId });

      // Delete user role assignments (if any inactive ones exist)
      await manager.delete(UserRoleEntity, { roleId });

      // Delete the role itself
      await manager.delete(RoleEntity, { id: roleId });
    });

    this.logger.log(`Role deleted successfully: ${role.name} (${roleId})`);
  }

  /**
   * Find role by ID
   */
  async findRoleById(roleId: string): Promise<RoleEntity> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
    });

    if (!role) {
      throw new HttpErrorException(AUTHORIZATION_ERROR_CODES.ROLE_NOT_FOUND);
    }

    return role;
  }

  /**
   * Find role by name
   */
  async findRoleByName(name: string): Promise<RoleEntity | null> {
    return this.roleRepository.findOne({
      where: { name: name.toLowerCase().trim() },
    });
  }

  /**
   * List roles with filtering and pagination
   */
  async listRoles(params: RoleQueryParams = {}): Promise<{
    data: RoleEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      type,
      isActive,
      search,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortOrder = 'ASC',
    } = params;

    const queryBuilder = this.roleRepository.createQueryBuilder('role');

    // Apply filters
    if (type !== undefined) {
      queryBuilder.andWhere('role.type = :type', { type });
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('role.isActive = :isActive', { isActive });
    }

    if (search) {
      queryBuilder.andWhere(
        '(role.name ILIKE :search OR role.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`role.${sortBy}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(
    input: AssignRoleInput,
    assignedBy?: string,
  ): Promise<UserRoleEntity> {
    this.logger.log(`Assigning role ${input.roleId} to user ${input.userId}`);

    // Validate role exists and is active
    const role = await this.findRoleById(input.roleId);
    if (!role.isActive) {
      throw new BadRequestException('Cannot assign inactive role');
    }

    // Check if assignment already exists
    const existingAssignment = await this.userRoleRepository.findOne({
      where: { userId: input.userId, roleId: input.roleId },
    });

    if (existingAssignment && existingAssignment.isCurrentlyActive()) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.ROLE_ASSIGNMENT_ALREADY_EXISTS,
      );
    }

    // Create or reactivate assignment
    let assignment: UserRoleEntity;

    if (existingAssignment) {
      // Reactivate existing assignment
      existingAssignment.isActive = true;
      existingAssignment.assignedAt = new Date();
      existingAssignment.assignedBy = assignedBy;
      existingAssignment.expiresAt = input.expiresAt;
      existingAssignment.conditions = input.conditions;
      assignment = await this.userRoleRepository.save(existingAssignment);
    } else {
      // Create new assignment
      assignment = this.userRoleRepository.create({
        userId: input.userId,
        roleId: input.roleId,
        assignedBy,
        expiresAt: input.expiresAt,
        conditions: input.conditions,
        isActive: true,
      });
      assignment = await this.userRoleRepository.save(assignment);
    }

    this.logger.log(
      `Role assigned successfully: ${input.roleId} to ${input.userId}`,
    );
    return assignment;
  }

  /**
   * Revoke role from user
   */
  async revokeRoleFromUser(
    userId: string,
    roleId: string,
    revokedBy?: string,
  ): Promise<void> {
    this.logger.log(`Revoking role ${roleId} from user ${userId}`);

    const assignment = await this.userRoleRepository.findOne({
      where: { userId, roleId, isActive: true },
    });

    if (!assignment) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.ROLE_ASSIGNMENT_NOT_FOUND,
      );
    }

    assignment.isActive = false;
    await this.userRoleRepository.save(assignment);

    this.logger.log(`Role revoked successfully: ${roleId} from ${userId}`);
  }

  /**
   * Get user roles
   */
  async getUserRoles(
    userId: string,
    includeExpired = false,
  ): Promise<RoleEntity[]> {
    const queryBuilder = this.userRoleRepository
      .createQueryBuilder('userRole')
      .leftJoinAndSelect('userRole.role', 'role')
      .where('userRole.userId = :userId', { userId })
      .andWhere('userRole.isActive = true')
      .andWhere('role.isActive = true');

    if (!includeExpired) {
      queryBuilder.andWhere(
        '(userRole.expiresAt IS NULL OR userRole.expiresAt > :now)',
        { now: new Date() },
      );
    }

    const userRoles = await queryBuilder.getMany();
    return userRoles.map((ur) => ur.role);
  }

  /**
   * Check if user has specific role
   */
  async userHasRole(userId: string, roleName: string): Promise<boolean> {
    const count = await this.userRoleRepository
      .createQueryBuilder('userRole')
      .leftJoin('userRole.role', 'role')
      .where('userRole.userId = :userId', { userId })
      .andWhere('role.name = :roleName', { roleName: roleName.toLowerCase() })
      .andWhere('userRole.isActive = true')
      .andWhere('role.isActive = true')
      .andWhere('(userRole.expiresAt IS NULL OR userRole.expiresAt > :now)', {
        now: new Date(),
      })
      .getCount();

    return count > 0;
  }

  /**
   * Check if user has specific permission (from roles)
   */
  async userHasPermission(
    userId: string,
    permission: string,
  ): Promise<boolean> {
    const roles = await this.getUserRoles(userId);
    return roles.some((role) => role.permissions.includes(permission));
  }

  /**
   * Get role with user assignments
   */
  async getRoleWithUsers(roleId: string): Promise<RoleWithUsers> {
    const role = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect('role.userRoles', 'userRole')
      .leftJoinAndSelect('userRole.user', 'user')
      .where('role.id = :roleId', { roleId })
      .getOne();

    if (!role) {
      throw new HttpErrorException(AUTHORIZATION_ERROR_CODES.ROLE_NOT_FOUND);
    }

    const activeUserRoles =
      role.userRoles?.filter((ur) => ur.isCurrentlyActive()) || [];

    const users: UserRoleAssignment[] = activeUserRoles.map((ur) => ({
      userId: ur.userId,
      userEmail: ur.user?.email || '',
      userFirstName: ur.user?.firstName || '',
      userLastName: ur.user?.lastName || '',
      assignedAt: ur.assignedAt,
      assignedBy: ur.assignedBy,
      expiresAt: ur.expiresAt,
      isActive: ur.isActive,
      isExpired: ur.isExpired(),
    }));

    return {
      id: role.id,
      name: role.name,
      description: role.description,
      type: role.type,
      permissions: role.permissions,
      isActive: role.isActive,
      priority: role.priority,
      userCount: users.length,
      users,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  }

  /**
   * Get role permissions summary including policies
   */
  async getRolePermissionsSummary(
    roleId: string,
  ): Promise<RolePermissionsSummary> {
    const role = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect('role.rolePolicies', 'rolePolicy')
      .leftJoinAndSelect('rolePolicy.policy', 'policy')
      .where('role.id = :roleId', { roleId })
      .andWhere('rolePolicy.isActive = true')
      .andWhere('policy.isActive = true')
      .getOne();

    if (!role) {
      throw new HttpErrorException(AUTHORIZATION_ERROR_CODES.ROLE_NOT_FOUND);
    }

    const policyPermissions =
      role.rolePolicies?.map((rp) => ({
        policyId: rp.policy.id,
        policyName: rp.policy.name,
        effect: rp.policy.effect,
        actions: rp.policy.actions,
        resources: rp.policy.resources,
        conditions: rp.policy.document.statement[0]?.condition,
      })) || [];

    // Collect all effective permissions
    const effectivePermissions = new Set([
      ...role.permissions,
      ...policyPermissions.flatMap((pp) => pp.actions),
    ]);

    return {
      roleId: role.id,
      roleName: role.name,
      directPermissions: role.permissions,
      policyPermissions,
      effectivePermissions: Array.from(effectivePermissions),
      totalPermissions: effectivePermissions.size,
    };
  }

  /**
   * Attach policy to role
   */
  async attachPolicyToRole(
    roleId: string,
    policyId: string,
    attachedBy?: string,
    priorityOverride?: number,
  ): Promise<RolePolicyEntity> {
    this.logger.log(`Attaching policy ${policyId} to role ${roleId}`);

    // Validate role and policy exist
    await this.findRoleById(roleId);
    const policy = await this.policyRepository.findOne({
      where: { id: policyId },
    });

    if (!policy) {
      throw new NotFoundException(`Policy not found: ${policyId}`);
    }

    // Check if attachment already exists
    const existingAttachment = await this.rolePolicyRepository.findOne({
      where: { roleId, policyId },
    });

    if (existingAttachment && existingAttachment.isCurrentlyActive()) {
      throw new ConflictException('Policy is already attached to this role');
    }

    // Create or reactivate attachment
    let attachment: RolePolicyEntity;

    if (existingAttachment) {
      existingAttachment.isActive = true;
      existingAttachment.attachedAt = new Date();
      existingAttachment.attachedBy = attachedBy;
      existingAttachment.priorityOverride = priorityOverride;
      attachment = await this.rolePolicyRepository.save(existingAttachment);
    } else {
      attachment = this.rolePolicyRepository.create({
        roleId,
        policyId,
        attachedBy,
        priorityOverride,
        isActive: true,
      });
      attachment = await this.rolePolicyRepository.save(attachment);
    }

    this.logger.log(
      `Policy attached successfully: ${policyId} to role ${roleId}`,
    );
    return attachment;
  }

  /**
   * Detach policy from role
   */
  async detachPolicyFromRole(roleId: string, policyId: string): Promise<void> {
    this.logger.log(`Detaching policy ${policyId} from role ${roleId}`);

    const attachment = await this.rolePolicyRepository.findOne({
      where: { roleId, policyId, isActive: true },
    });

    if (!attachment) {
      throw new NotFoundException('Policy attachment not found');
    }

    attachment.isActive = false;
    await this.rolePolicyRepository.save(attachment);

    this.logger.log(
      `Policy detached successfully: ${policyId} from role ${roleId}`,
    );
  }

  /**
   * Validate permissions format
   */
  private validatePermissions(permissions: string[]): void {
    const validPermissionPattern =
      /^[a-zA-Z][a-zA-Z0-9]*:[a-zA-Z][a-zA-Z0-9*]*$/;

    for (const permission of permissions) {
      if (!validPermissionPattern.test(permission)) {
        throw new HttpErrorException(
          AUTHORIZATION_ERROR_CODES.ROLE_PERMISSION_INVALID,
        );
      }
    }
  }

  /**
   * Bulk role operations
   */
  async performBulkRoleOperation(
    operation: BulkRoleOperation,
    performedBy?: string,
  ): Promise<{
    successful: string[];
    failed: { userId: string; error: string }[];
  }> {
    const results: {
      successful: string[];
      failed: { userId: string; error: string }[];
    } = {
      successful: [],
      failed: [],
    };

    for (const userId of operation.userIds) {
      try {
        if (operation.operation === 'assign' && operation.roleIds) {
          for (const roleId of operation.roleIds) {
            await this.assignRoleToUser(
              {
                userId,
                roleId,
                expiresAt: operation.expiresAt,
              },
              performedBy,
            );
          }
        } else if (operation.operation === 'revoke' && operation.roleIds) {
          for (const roleId of operation.roleIds) {
            await this.revokeRoleFromUser(userId, roleId, performedBy);
          }
        }

        results.successful.push(userId);
      } catch (error) {
        results.failed.push({
          userId,
          error: error.message,
        });
      }
    }

    return results;
  }
}
