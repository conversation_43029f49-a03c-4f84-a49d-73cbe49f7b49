import { Inject, Injectable } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import {
  ConditionEvaluationResult,
  PolicyConditions,
} from '../interfaces/policy.interface';
import { authorizationConfig } from '../authorization.config';

/**
 * Service for evaluating policy conditions
 * Implements AWS-style condition operators
 */
@Injectable()
export class ConditionEvaluatorService {
  constructor(
    @Inject(authorizationConfig.KEY)
    private readonly config: ConfigType<typeof authorizationConfig>,
  ) {}

  /**
   * Evaluate all conditions in a policy statement
   */
  evaluateConditions(
    conditions: PolicyConditions,
    context: Record<string, any>,
  ): ConditionEvaluationResult[] {
    const results: ConditionEvaluationResult[] = [];

    for (const [operator, conditionMap] of Object.entries(conditions)) {
      for (const [key, expectedValue] of Object.entries(conditionMap || {})) {
        const actualValue = this.getContextValue(key, context);
        const result = this.evaluateCondition(
          operator,
          key,
          expectedValue,
          actualValue,
        );
        results.push(result);
      }
    }

    return results;
  }

  /**
   * Check if all conditions are satisfied
   */
  areConditionsSatisfied(
    conditions: PolicyConditions,
    context: Record<string, any>,
  ): boolean {
    const results = this.evaluateConditions(conditions, context);
    return results.every((result) => result.satisfied);
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(
    operator: string,
    key: string,
    expectedValue: any,
    actualValue: any,
  ): ConditionEvaluationResult {
    let satisfied = false;
    let details = '';

    try {
      switch (operator) {
        // String conditions
        case 'StringEquals':
          satisfied = this.stringEquals(actualValue, expectedValue);
          break;
        case 'StringNotEquals':
          satisfied = !this.stringEquals(actualValue, expectedValue);
          break;
        case 'StringLike':
          satisfied = this.stringLike(actualValue, expectedValue);
          break;
        case 'StringNotLike':
          satisfied = !this.stringLike(actualValue, expectedValue);
          break;

        // Numeric conditions
        case 'NumericEquals':
          satisfied = this.numericEquals(actualValue, expectedValue);
          break;
        case 'NumericNotEquals':
          satisfied = !this.numericEquals(actualValue, expectedValue);
          break;
        case 'NumericLessThan':
          satisfied = this.numericLessThan(actualValue, expectedValue);
          break;
        case 'NumericLessThanEquals':
          satisfied = this.numericLessThanEquals(actualValue, expectedValue);
          break;
        case 'NumericGreaterThan':
          satisfied = this.numericGreaterThan(actualValue, expectedValue);
          break;
        case 'NumericGreaterThanEquals':
          satisfied = this.numericGreaterThanEquals(actualValue, expectedValue);
          break;

        // Date conditions
        case 'DateEquals':
          satisfied = this.dateEquals(actualValue, expectedValue);
          break;
        case 'DateNotEquals':
          satisfied = !this.dateEquals(actualValue, expectedValue);
          break;
        case 'DateLessThan':
          satisfied = this.dateLessThan(actualValue, expectedValue);
          break;
        case 'DateLessThanEquals':
          satisfied = this.dateLessThanEquals(actualValue, expectedValue);
          break;
        case 'DateGreaterThan':
          satisfied = this.dateGreaterThan(actualValue, expectedValue);
          break;
        case 'DateGreaterThanEquals':
          satisfied = this.dateGreaterThanEquals(actualValue, expectedValue);
          break;

        // Boolean conditions
        case 'Bool':
          satisfied = this.boolEquals(actualValue, expectedValue);
          break;

        // IP address conditions
        case 'IpAddress':
          satisfied = this.ipAddressMatches(actualValue, expectedValue);
          break;
        case 'NotIpAddress':
          satisfied = !this.ipAddressMatches(actualValue, expectedValue);
          break;

        // Null conditions
        case 'Null':
          satisfied = this.nullCheck(actualValue, expectedValue);
          break;

        // Array conditions
        case 'ForAllValues':
          satisfied = this.forAllValues(actualValue, expectedValue);
          break;
        case 'ForAnyValue':
          satisfied = this.forAnyValue(actualValue, expectedValue);
          break;

        default:
          satisfied = false;
          details = `Unknown condition operator: ${operator}`;
      }
    } catch (error) {
      satisfied = false;
      details = `Error evaluating condition: ${error.message}`;
    }

    return {
      operator,
      key,
      expectedValue,
      actualValue,
      satisfied,
      details,
    };
  }

  /**
   * Get context value by key, supporting dot notation and variable substitution
   */
  private getContextValue(key: string, context: Record<string, any>): any {
    // Handle special context variables with configurable prefix
    const prefix = `${this.config.contextPrefix}:`;
    if (key.startsWith(prefix)) {
      return this.getContextVariableValue(key, context);
    }

    // Handle dot notation (e.g., 'user.id', 'request.ipAddress')
    const parts = key.split('.');
    let value = context;

    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Get context variable values with configurable prefix
   */
  private getContextVariableValue(
    key: string,
    context: Record<string, any>,
  ): any {
    const prefix = `${this.config.contextPrefix}:`;
    const variableName = key.substring(prefix.length);

    switch (variableName) {
      case 'userid':
        return context.user?.id;
      case 'username':
        return context.user?.email || context.user?.username;
      case 'sourceIp':
        return context.request?.ipAddress;
      case 'userAgent':
        return context.request?.userAgent;
      case 'currentTime':
        return context.request?.timestamp || new Date();
      case 'requestedRegion':
        return context.request?.region || 'us-east-1';
      default:
        return undefined;
    }
  }

  // String condition methods
  private stringEquals(actual: any, expected: any): boolean {
    if (Array.isArray(expected)) {
      return expected.some((exp) => String(actual) === String(exp));
    }
    return String(actual) === String(expected);
  }

  private stringLike(actual: any, expected: any): boolean {
    const actualStr = String(actual);
    if (Array.isArray(expected)) {
      return expected.some((exp) => this.matchPattern(actualStr, String(exp)));
    }
    return this.matchPattern(actualStr, String(expected));
  }

  private matchPattern(value: string, pattern: string): boolean {
    // Convert shell-style wildcards to regex
    const regexPattern = pattern
      .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
      .replace(/\\\*/g, '.*') // Convert * to .*
      .replace(/\\\?/g, '.'); // Convert ? to .

    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(value);
  }

  // Numeric condition methods
  private numericEquals(actual: any, expected: any): boolean {
    const actualNum = Number(actual);
    if (isNaN(actualNum)) return false;

    if (Array.isArray(expected)) {
      return expected.some((exp) => actualNum === Number(exp));
    }
    return actualNum === Number(expected);
  }

  private numericLessThan(actual: any, expected: any): boolean {
    const actualNum = Number(actual);
    const expectedNum = Number(expected);
    return !isNaN(actualNum) && !isNaN(expectedNum) && actualNum < expectedNum;
  }

  private numericLessThanEquals(actual: any, expected: any): boolean {
    const actualNum = Number(actual);
    const expectedNum = Number(expected);
    return !isNaN(actualNum) && !isNaN(expectedNum) && actualNum <= expectedNum;
  }

  private numericGreaterThan(actual: any, expected: any): boolean {
    const actualNum = Number(actual);
    const expectedNum = Number(expected);
    return !isNaN(actualNum) && !isNaN(expectedNum) && actualNum > expectedNum;
  }

  private numericGreaterThanEquals(actual: any, expected: any): boolean {
    const actualNum = Number(actual);
    const expectedNum = Number(expected);
    return !isNaN(actualNum) && !isNaN(expectedNum) && actualNum >= expectedNum;
  }

  // Date condition methods
  private dateEquals(actual: any, expected: any): boolean {
    const actualDate = new Date(actual);
    const expectedDate = new Date(expected);
    return actualDate.getTime() === expectedDate.getTime();
  }

  private dateLessThan(actual: any, expected: any): boolean {
    const actualDate = new Date(actual);
    const expectedDate = new Date(expected);
    return actualDate.getTime() < expectedDate.getTime();
  }

  private dateLessThanEquals(actual: any, expected: any): boolean {
    const actualDate = new Date(actual);
    const expectedDate = new Date(expected);
    return actualDate.getTime() <= expectedDate.getTime();
  }

  private dateGreaterThan(actual: any, expected: any): boolean {
    const actualDate = new Date(actual);
    const expectedDate = new Date(expected);
    return actualDate.getTime() > expectedDate.getTime();
  }

  private dateGreaterThanEquals(actual: any, expected: any): boolean {
    const actualDate = new Date(actual);
    const expectedDate = new Date(expected);
    return actualDate.getTime() >= expectedDate.getTime();
  }

  // Boolean condition methods
  private boolEquals(actual: any, expected: any): boolean {
    const actualBool = this.parseBoolean(actual);
    const expectedBool = this.parseBoolean(expected);
    return actualBool === expectedBool;
  }

  private parseBoolean(value: any): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  }

  // IP address condition methods
  private ipAddressMatches(actual: any, expected: any): boolean {
    const actualIp = String(actual);

    if (Array.isArray(expected)) {
      return expected.some((exp) => this.isIpInRange(actualIp, String(exp)));
    }

    return this.isIpInRange(actualIp, String(expected));
  }

  private isIpInRange(ip: string, range: string): boolean {
    try {
      if (!range.includes('/')) {
        // Single IP comparison
        return ip === range;
      }

      // CIDR range comparison
      const [network, prefixLength] = range.split('/');
      const prefix = parseInt(prefixLength, 10);

      const ipInt = this.ipToInt(ip);
      const networkInt = this.ipToInt(network);
      const mask = (0xffffffff << (32 - prefix)) >>> 0;

      return (ipInt & mask) === (networkInt & mask);
    } catch {
      return false;
    }
  }

  private ipToInt(ip: string): number {
    return (
      ip
        .split('.')
        .reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0
    );
  }

  // Null condition methods
  private nullCheck(actual: any, expected: any): boolean {
    const isActualNull = actual === null || actual === undefined;
    const expectedBool = this.parseBoolean(expected);
    return isActualNull === expectedBool;
  }

  // Array condition methods
  private forAllValues(actual: any, expected: any): boolean {
    if (!Array.isArray(actual)) return false;

    // For nested condition operators in ForAllValues
    if (typeof expected === 'object' && !Array.isArray(expected)) {
      return actual.every((value) => {
        for (const [operator, conditionMap] of Object.entries(expected)) {
          for (const [key, expectedValue] of Object.entries(
            conditionMap as Record<string, any>,
          )) {
            const result = this.evaluateCondition(
              operator,
              key,
              expectedValue,
              value,
            );
            if (!result.satisfied) return false;
          }
        }
        return true;
      });
    }

    return false;
  }

  private forAnyValue(actual: any, expected: any): boolean {
    if (!Array.isArray(actual)) return false;

    // For nested condition operators in ForAnyValue
    if (typeof expected === 'object' && !Array.isArray(expected)) {
      return actual.some((value) => {
        for (const [operator, conditionMap] of Object.entries(expected)) {
          for (const [key, expectedValue] of Object.entries(
            conditionMap as Record<string, any>,
          )) {
            const result = this.evaluateCondition(
              operator,
              key,
              expectedValue,
              value,
            );
            if (result.satisfied) return true;
          }
        }
        return false;
      });
    }

    return false;
  }
}
