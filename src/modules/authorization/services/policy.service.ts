import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import {
  PolicyEntity,
  RoleEntity,
  RolePolicyEntity,
  UserPolicyEntity,
  UserRoleEntity,
} from '../entities';
import { PolicyEffect } from '../enums/policy-effect.enum';
import {
  PolicyDocument,
  PolicyEvaluationContext,
  PolicyEvaluationResult,
  PolicyStatement,
} from '../interfaces/policy.interface';
import { AUTHORIZATION_ERROR_CODES } from '../authorization.error-codes';
import { PolicyEngineService } from './policy-engine.service';
import { RoleService } from './role.service';
import { toDto } from '@app/common/dto/to-dto';
import { PolicyResponseDto } from '@app/modules/authorization/dto';

export interface CreatePolicyInput {
  name: string;
  description?: string;
  document: PolicyDocument;
  isActive?: boolean;
  priority?: number;
  tags?: Record<string, string>;
}

export interface UpdatePolicyInput {
  name?: string;
  description?: string;
  document?: PolicyDocument;
  isActive?: boolean;
  priority?: number;
  tags?: Record<string, string>;
}

export interface PolicyQueryParams {
  isActive?: boolean;
  search?: string;
  action?: string;
  resource?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'priority';
  sortOrder?: 'ASC' | 'DESC';
}

export interface AttachPolicyInput {
  userId: string;
  policyId: string;
  expiresAt?: Date;
  priorityOverride?: number;
  reason?: string;
}

@Injectable()
export class PolicyService {
  private readonly logger = new Logger(PolicyService.name);

  constructor(
    @InjectRepository(PolicyEntity)
    private readonly policyRepo: Repository<PolicyEntity>,
    @InjectRepository(UserPolicyEntity)
    private readonly userPolicyRepo: Repository<UserPolicyEntity>,
    @InjectRepository(RolePolicyEntity)
    private readonly rolePolicyRepo: Repository<RolePolicyEntity>,
    @InjectRepository(UserRoleEntity)
    private readonly userRoleRepo: Repository<UserRoleEntity>,
    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,
    private readonly policyEngine: PolicyEngineService,
    private readonly roleService: RoleService,
  ) {}

  /**
   * Create a new policy
   */
  async createPolicy(
    input: CreatePolicyInput,
    createdById?: string,
  ): Promise<PolicyEntity> {
    this.logger.log(`Creating policy: ${input.name}`);

    // Check if policy name already exists
    const existingPolicy = await this.policyRepo.findOne({
      where: { name: input.name },
    });

    if (existingPolicy) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_ALREADY_EXISTS,
      );
    }

    // Validate policy document
    this.validatePolicyDocument(input.document);

    // Extract actions and resources for caching
    const { actions, resources, principals } = this.extractPolicyElements(
      input.document,
    );

    const policy = this.policyRepo.create({
      name: input.name,
      description: input.description,
      document: input.document,
      version: input.document.version,
      effect: this.determinePolicyEffect(input.document),
      actions,
      resources,
      principals,
      isActive: input.isActive ?? true,
      priority: input.priority ?? 0,
      tags: input.tags,
      isBuiltIn: false,
      createdById: createdById,
    });

    const savedPolicy = await this.policyRepo.save(policy);

    this.logger.log(
      `Policy created successfully: ${savedPolicy.name} (${savedPolicy.id})`,
    );
    return savedPolicy;
  }

  /**
   * Update an existing policy
   */
  async updatePolicy(
    policyId: string,
    input: UpdatePolicyInput,
    updatedById?: string,
  ): Promise<PolicyEntity> {
    this.logger.log(`Updating policy: ${policyId}`);

    const policy = await this.findPolicyById(policyId);

    // Prevent modification of built-in policies
    if (policy.isBuiltIn) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_CANNOT_MODIFY_BUILTIN,
      );
    }

    // Check for name conflicts if name is being changed
    if (input.name && input.name !== policy.name) {
      const existingPolicy = await this.policyRepo.findOne({
        where: { name: input.name, id: Not(policyId) },
      });

      if (existingPolicy) {
        throw new HttpErrorException(
          AUTHORIZATION_ERROR_CODES.POLICY_ALREADY_EXISTS,
        );
      }
    }

    // Validate policy document if provided
    if (input.document) {
      this.validatePolicyDocument(input.document);
    }

    // Update policy properties
    if (input.document) {
      const { actions, resources, principals } = this.extractPolicyElements(
        input.document,
      );
      Object.assign(policy, {
        document: input.document,
        version: input.document.version,
        effect: this.determinePolicyEffect(input.document),
        actions,
        resources,
        principals,
        updatedById,
      });
    }

    Object.assign(policy, {
      ...(input.name && { name: input.name }),
      ...(input.description !== undefined && {
        description: input.description,
      }),
      ...(input.isActive !== undefined && { isActive: input.isActive }),
      ...(input.priority !== undefined && { priority: input.priority }),
      ...(input.tags && { tags: { ...policy.tags, ...input.tags } }),
    });

    const updatedPolicy = await this.policyRepo.save(policy);

    this.logger.log(
      `Policy updated successfully: ${updatedPolicy.name} (${updatedPolicy.id})`,
    );
    return updatedPolicy;
  }

  /**
   * Delete a policy
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async deletePolicy(policyId: string, deletedById?: string): Promise<void> {
    this.logger.log(`Deleting policy: ${policyId}`);

    const policy = await this.findPolicyById(policyId);

    // Prevent deletion of built-in policies
    if (policy.isBuiltIn) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_CANNOT_DELETE_BUILTIN,
      );
    }

    // Check for active attachments
    const activeAttachments = await Promise.all([
      this.userPolicyRepo.count({ where: { policyId, isActive: true } }),
      this.rolePolicyRepo.count({ where: { policyId, isActive: true } }),
    ]);

    const totalActiveAttachments = activeAttachments.reduce(
      (sum, count) => sum + count,
      0,
    );

    if (totalActiveAttachments > 0) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_HAS_ACTIVE_ATTACHMENTS,
      );
    }

    // Delete the policy itself
    await this.policyRepo.delete({ id: policyId });

    this.logger.log(
      `Policy deleted successfully: ${policy.name} (${policyId})`,
    );
  }

  /**
   * Find policy by ID
   */
  async findPolicyById(policyId: string): Promise<PolicyEntity> {
    const policy = await this.policyRepo.findOne({
      where: { id: policyId },
    });

    if (!policy) {
      throw new HttpErrorException(AUTHORIZATION_ERROR_CODES.POLICY_NOT_FOUND);
    }

    return policy;
  }

  /**
   * Find policy by name
   */
  async findPolicyByName(name: string): Promise<PolicyEntity | null> {
    return this.policyRepo.findOne({
      where: { name },
    });
  }

  /**
   * List policies with filtering and pagination
   */
  async listPolicies(params: PolicyQueryParams = {}): Promise<{
    data: PolicyEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      isActive,
      search,
      action,
      resource,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortOrder = 'ASC',
    } = params;

    const queryBuilder = this.policyRepo.createQueryBuilder('policy');

    // Apply filters
    if (isActive !== undefined) {
      queryBuilder.andWhere('policy.isActive = :isActive', { isActive });
    }

    if (search) {
      queryBuilder.andWhere(
        '(policy.name ILIKE :search OR policy.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (action) {
      queryBuilder.andWhere(':action = ANY(policy.actions)', { action });
    }

    if (resource) {
      queryBuilder.andWhere(':resource = ANY(policy.resources)', { resource });
    }

    // Apply sorting
    queryBuilder.orderBy(`policy.${sortBy}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Attach policy directly to user
   */
  async attachPolicyToUser(
    input: AttachPolicyInput,
    attachedBy?: string,
  ): Promise<UserPolicyEntity> {
    this.logger.log(
      `Attaching policy ${input.policyId} to user ${input.userId}`,
    );

    // Validate policy exists and is active
    const policy = await this.findPolicyById(input.policyId);
    if (!policy.isActive) {
      throw new HttpErrorException(AUTHORIZATION_ERROR_CODES.POLICY_NOT_FOUND);
    }

    // Check if attachment already exists
    const existingAttachment = await this.userPolicyRepo.findOne({
      where: { userId: input.userId, policyId: input.policyId },
    });

    if (existingAttachment && existingAttachment.isCurrentlyActive()) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_ATTACHMENT_ALREADY_EXISTS,
      );
    }

    // Create or reactivate attachment
    let attachment: UserPolicyEntity;

    if (existingAttachment) {
      // Reactivate existing attachment
      existingAttachment.isActive = true;
      existingAttachment.attachedAt = new Date();
      existingAttachment.attachedBy = attachedBy;
      existingAttachment.expiresAt = input.expiresAt;
      existingAttachment.priorityOverride = input.priorityOverride;
      existingAttachment.reason = input.reason;
      attachment = await this.userPolicyRepo.save(existingAttachment);
    } else {
      // Create new attachment
      attachment = this.userPolicyRepo.create({
        userId: input.userId,
        policyId: input.policyId,
        attachedBy,
        expiresAt: input.expiresAt,
        priorityOverride: input.priorityOverride,
        reason: input.reason,
        isActive: true,
      });
      attachment = await this.userPolicyRepo.save(attachment);
    }

    this.logger.log(
      `Policy attached successfully: ${input.policyId} to ${input.userId}`,
    );
    return attachment;
  }

  /**
   * Detach policy from user
   */
  async detachPolicyFromUser(userId: string, policyId: string): Promise<void> {
    this.logger.log(`Detaching policy ${policyId} from user ${userId}`);

    const attachment = await this.userPolicyRepo.findOne({
      where: { userId, policyId, isActive: true },
    });

    if (!attachment) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_ATTACHMENT_NOT_FOUND,
      );
    }

    attachment.isActive = false;
    await this.userPolicyRepo.save(attachment);

    this.logger.log(`Policy detached successfully: ${policyId} from ${userId}`);
  }

  /**
   * Main policy evaluation method
   */
  async evaluateAccess(
    context: PolicyEvaluationContext,
  ): Promise<PolicyEvaluationResult> {
    this.logger.debug(
      `Evaluating access for user ${context.user.id}, action: ${context.action}, resource: ${context.resource}`,
    );

    const startTime = Date.now();

    try {
      // Get all applicable policies for the user
      const policies = await this.getApplicablePolicies(
        context.user.id,
        context.action,
        context.resource,
      );

      if (policies.length === 0) {
        this.logger.debug(
          `No applicable policies found for user ${context.user.id}`,
        );
        return {
          decision: 'DENY' as any,
          allowed: false,
          evaluatedPolicies: [],
          reason: 'No applicable policies found',
          metadata: {
            evaluationTime: Date.now() - startTime,
            policiesConsidered: 0,
            statementsEvaluated: 0,
          },
        };
      }

      // Evaluate policies using the policy engine
      const result = await this.policyEngine.evaluatePolicies(
        policies,
        context,
      );

      this.logger.debug(
        `Policy evaluation completed for user ${context.user.id}: ${result.decision} in ${result.metadata?.evaluationTime}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error during policy evaluation: ${error.message}`,
        error.stack,
      );
      throw new HttpErrorException(AUTHORIZATION_ERROR_CODES.EVALUATION_FAILED);
    }
  }

  /**
   * Get all applicable policies for a user, action, and resource
   */
  private async getApplicablePolicies(
    userId: string,
    action: string,
    resource: string,
  ): Promise<
    Array<{
      id: string;
      name: string;
      document: PolicyDocument;
      priority: number;
    }>
  > {
    const policies: Array<{
      id: string;
      name: string;
      document: PolicyDocument;
      priority: number;
    }> = [];

    // Get direct user policies
    const userPolicies = await this.userPolicyRepo
      .createQueryBuilder('userPolicy')
      .leftJoinAndSelect('userPolicy.policy', 'policy')
      .where('userPolicy.userId = :userId', { userId })
      .andWhere('userPolicy.isActive = true')
      .andWhere('policy.isActive = true')
      .andWhere(
        '(userPolicy.expiresAt IS NULL OR userPolicy.expiresAt > :now)',
        { now: new Date() },
      )
      .getMany();

    // Add user policies
    for (const userPolicy of userPolicies) {
      if (
        this.policyMatchesActionAndResource(userPolicy.policy, action, resource)
      ) {
        policies.push({
          id: userPolicy.policy.id,
          name: userPolicy.policy.name,
          document: userPolicy.policy.document,
          priority: userPolicy.getEffectivePriority(),
        });
      }
    }

    // Get policies from user roles
    const userRoles = await this.userRoleRepo
      .createQueryBuilder('userRole')
      .leftJoinAndSelect('userRole.role', 'role')
      .leftJoinAndSelect('role.rolePolicies', 'rolePolicy')
      .leftJoinAndSelect('rolePolicy.policy', 'policy')
      .where('userRole.userId = :userId', { userId })
      .andWhere('userRole.isActive = true')
      .andWhere('role.isActive = true')
      .andWhere('rolePolicy.isActive = true')
      .andWhere('policy.isActive = true')
      .andWhere('(userRole.expiresAt IS NULL OR userRole.expiresAt > :now)', {
        now: new Date(),
      })
      .getMany();

    // Add role policies
    for (const userRole of userRoles) {
      for (const rolePolicy of userRole.role.rolePolicies || []) {
        if (
          this.policyMatchesActionAndResource(
            rolePolicy.policy,
            action,
            resource,
          )
        ) {
          policies.push({
            id: rolePolicy.policy.id,
            name: rolePolicy.policy.name,
            document: rolePolicy.policy.document,
            priority: rolePolicy.getEffectivePriority(),
          });
        }
      }
    }

    // Remove duplicates by policy ID
    const uniquePolicies = policies.filter(
      (policy, index, self) =>
        index === self.findIndex((p) => p.id === policy.id),
    );

    this.logger.debug(
      `Found ${uniquePolicies.length} applicable policies for user ${userId}`,
    );
    return uniquePolicies;
  }

  /**
   * Quick check if policy matches action and resource patterns
   */
  private policyMatchesActionAndResource(
    policy: PolicyEntity,
    action: string,
    resource: string,
  ): boolean {
    // Check cached actions
    const actionMatches = policy.actions.some((policyAction) => {
      if (policyAction === '*') return true;
      if (policyAction === action) return true;
      if (policyAction.includes('*')) {
        const pattern = policyAction.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(action);
      }
      return false;
    });

    if (!actionMatches) return false;

    // Check cached resources
    const resourceMatches = policy.resources.some((policyResource) => {
      if (policyResource === '*') return true;
      if (policyResource === resource) return true;
      if (policyResource.includes('*')) {
        const pattern = policyResource.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(resource);
      }
      return false;
    });

    return resourceMatches;
  }

  /**
   * Validate policy document structure
   */
  private validatePolicyDocument(document: PolicyDocument): void {
    if (!document.version || document.version !== '2023-11-01') {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_VERSION_UNSUPPORTED,
      );
    }

    if (
      !document.statement ||
      !Array.isArray(document.statement) ||
      document.statement.length === 0
    ) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_STATEMENT_INVALID,
      );
    }

    if (document.statement.length > 20) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_STATEMENT_LIMIT_EXCEEDED,
      );
    }

    // Validate each statement
    for (const statement of document.statement) {
      this.validatePolicyStatement(statement);
    }
  }

  /**
   * Validate individual policy statement
   */
  private validatePolicyStatement(statement: PolicyStatement): void {
    // Validate effect
    if (
      !statement.effect ||
      ![PolicyEffect.ALLOW, PolicyEffect.DENY].includes(statement.effect)
    ) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_EFFECT_INVALID,
      );
    }

    // Validate action
    if (!statement.action) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_ACTION_INVALID,
      );
    }

    // Validate resource
    if (!statement.resource) {
      throw new HttpErrorException(
        AUTHORIZATION_ERROR_CODES.POLICY_RESOURCE_INVALID,
      );
    }

    // Additional validation can be added here
  }

  /**
   * Extract actions, resources, and principals from policy document for caching
   */
  private extractPolicyElements(document: PolicyDocument): {
    actions: string[];
    resources: string[];
    principals: string[];
  } {
    const actions = new Set<string>();
    const resources = new Set<string>();
    const principals = new Set<string>();

    for (const statement of document.statement) {
      // Extract actions
      const statementActions = Array.isArray(statement.action)
        ? statement.action
        : [statement.action];
      statementActions.forEach((action) => actions.add(action));

      // Extract resources
      const statementResources = Array.isArray(statement.resource)
        ? statement.resource
        : [statement.resource];
      statementResources.forEach((resource) => resources.add(resource));

      // Extract principals
      if (statement.principal) {
        if (typeof statement.principal === 'string') {
          principals.add(statement.principal);
        } else if (Array.isArray(statement.principal)) {
          statement.principal.forEach((principal) => principals.add(principal));
        } else if (typeof statement.principal === 'object') {
          Object.values(statement.principal)
            .flat()
            .forEach((principal) => principals.add(String(principal)));
        }
      }
    }

    return {
      actions: Array.from(actions),
      resources: Array.from(resources),
      principals: Array.from(principals),
    };
  }

  /**
   * Determine the primary effect of a policy (for caching)
   */
  private determinePolicyEffect(document: PolicyDocument): PolicyEffect {
    // If any statement has Deny effect, mark the policy as Deny
    const hasDeny = document.statement.some(
      (statement) => statement.effect === PolicyEffect.DENY,
    );
    return hasDeny ? PolicyEffect.DENY : PolicyEffect.ALLOW;
  }

  /**
   * Get user's effective policies (direct + role-based)
   */
  async getUserPolicies(userId: string): Promise<{
    directPolicies: PolicyEntity[];
    rolePolicies: { role: string; policies: PolicyEntity[] }[];
  }> {
    // Get direct policies
    const userPolicies = await this.userPolicyRepo
      .createQueryBuilder('userPolicy')
      .leftJoinAndSelect('userPolicy.policy', 'policy')
      .where('userPolicy.userId = :userId', { userId })
      .andWhere('userPolicy.isActive = true')
      .andWhere('policy.isActive = true')
      .andWhere(
        '(userPolicy.expiresAt IS NULL OR userPolicy.expiresAt > :now)',
        { now: new Date() },
      )
      .getMany();

    const directPolicies = userPolicies.map((up) => up.policy);

    // Get role-based policies
    const userRoles = await this.userRoleRepo
      .createQueryBuilder('userRole')
      .leftJoinAndSelect('userRole.role', 'role')
      .leftJoinAndSelect('role.rolePolicies', 'rolePolicy')
      .leftJoinAndSelect('rolePolicy.policy', 'policy')
      .where('userRole.userId = :userId', { userId })
      .andWhere('userRole.isActive = true')
      .andWhere('role.isActive = true')
      .andWhere('rolePolicy.isActive = true')
      .andWhere('policy.isActive = true')
      .andWhere('(userRole.expiresAt IS NULL OR userRole.expiresAt > :now)', {
        now: new Date(),
      })
      .getMany();

    const rolePolicies = userRoles.map((ur) => ({
      role: ur.role.name,
      policies: ur.role.rolePolicies?.map((rp) => rp.policy) || [],
    }));

    return {
      directPolicies,
      rolePolicies,
    };
  }
}
