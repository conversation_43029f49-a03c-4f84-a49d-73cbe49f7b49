import { Injectable, Logger } from '@nestjs/common';
import { AccessDecision, PolicyEffect } from '../enums';
import {
  PolicyDocument,
  PolicyEvaluationContext,
  PolicyEvaluationDetail,
  PolicyEvaluationResult,
  PolicyStatement,
} from '../interfaces/policy.interface';
import { ConditionEvaluatorService } from './condition-evaluator.service';
import { ResourceHandlerRegistry } from './resource-handler.service';

/**
 * Core policy evaluation engine
 * Implements AWS IAM-style policy evaluation logic
 */
@Injectable()
export class PolicyEngineService {
  private readonly logger = new Logger(PolicyEngineService.name);

  constructor(
    private readonly conditionEvaluator: ConditionEvaluatorService,
    private readonly resourceHandlerRegistry: ResourceHandlerRegistry,
  ) {}

  /**
   * Evaluate a single policy document against the given context
   */
  async evaluatePolicy(
    policyDocument: PolicyDocument,
    context: PolicyEvaluationContext,
    policyId: string,
    policyName: string,
  ): Promise<PolicyEvaluationDetail> {
    const startTime = Date.now();

    try {
      // Check each statement in the policy
      for (const statement of policyDocument.statement) {
        const statementResult = await this.evaluateStatement(
          statement,
          context,
        );

        if (statementResult.matched) {
          const evaluationTime = Date.now() - startTime;
          this.logger.debug(
            `Policy ${policyName} (${policyId}) matched with effect ${statement.effect} in ${evaluationTime}ms`,
          );

          return {
            policyId,
            policyName,
            matched: true,
            effect: statement.effect,
            statementId: statement.sid,
            conditionResults: statementResult.conditionResults,
            reason: statementResult.reason,
          };
        }
      }

      // No statements matched
      return {
        policyId,
        policyName,
        matched: false,
        reason: 'No statements matched the request',
      };
    } catch (error) {
      this.logger.error(
        `Error evaluating policy ${policyName}: ${error.message}`,
        error.stack,
      );
      return {
        policyId,
        policyName,
        matched: false,
        reason: `Evaluation error: ${error.message}`,
      };
    }
  }

  /**
   * Evaluate multiple policies and combine the results
   */
  async evaluatePolicies(
    policies: Array<{
      id: string;
      name: string;
      document: PolicyDocument;
      priority: number;
    }>,
    context: PolicyEvaluationContext,
  ): Promise<PolicyEvaluationResult> {
    const startTime = Date.now();
    const evaluatedPolicies: PolicyEvaluationDetail[] = [];
    let finalDecision = AccessDecision.NOT_APPLICABLE;
    let decisionReason = 'No applicable policies found';
    const warnings: string[] = [];

    try {
      // Sort policies by priority (higher priority first)
      const sortedPolicies = policies.sort(
        (a, b) => (b.priority || 0) - (a.priority || 0),
      );

      // Evaluate each policy
      for (const policy of sortedPolicies) {
        const policyResult = await this.evaluatePolicy(
          policy.document,
          context,
          policy.id,
          policy.name,
        );

        evaluatedPolicies.push(policyResult);

        // Apply decision logic based on AWS IAM rules:
        // 1. Explicit deny always wins
        // 2. Explicit allow grants access if no deny
        // 3. Default deny if no explicit allow
        if (policyResult.matched) {
          if (policyResult.effect === PolicyEffect.DENY) {
            finalDecision = AccessDecision.DENY;
            decisionReason = `Explicitly denied by policy ${policy.name}`;
            break; // Stop evaluation on first deny
          } else if (
            policyResult.effect === PolicyEffect.ALLOW &&
            finalDecision === AccessDecision.NOT_APPLICABLE
          ) {
            finalDecision = AccessDecision.ALLOW;
            decisionReason = `Allowed by policy ${policy.name}`;
          }
        }
      }

      // If no explicit allow and no deny, default to deny
      if (finalDecision === AccessDecision.NOT_APPLICABLE) {
        finalDecision = AccessDecision.DENY;
        decisionReason = 'Default deny - no matching allow policies found';
      }

      const evaluationTime = Date.now() - startTime;
      this.logger.debug(
        `Policy evaluation completed: ${finalDecision} for action=${context.action} resource=${context.resource} in ${evaluationTime}ms`,
      );

      return {
        decision: finalDecision,
        allowed: finalDecision === AccessDecision.ALLOW,
        evaluatedPolicies,
        reason: decisionReason,
        warnings,
        metadata: {
          evaluationTime,
          policiesConsidered: policies.length,
          statementsEvaluated: evaluatedPolicies.reduce(
            (count, policy) => count + (policy.matched ? 1 : 0),
            0,
          ),
        },
      };
    } catch (error) {
      this.logger.error(
        `Error during policy evaluation: ${error.message}`,
        error.stack,
      );

      return {
        decision: AccessDecision.INDETERMINATE,
        allowed: false,
        evaluatedPolicies,
        reason: `Evaluation error: ${error.message}`,
        warnings: [`Policy evaluation failed: ${error.message}`],
        metadata: {
          evaluationTime: Date.now() - startTime,
          policiesConsidered: policies.length,
          statementsEvaluated: 0,
        },
      };
    }
  }

  /**
   * Evaluate a single policy statement
   */
  private async evaluateStatement(
    statement: PolicyStatement,
    context: PolicyEvaluationContext,
  ): Promise<{
    matched: boolean;
    conditionResults?: any[];
    reason?: string;
  }> {
    // Check if action matches
    if (!this.matchesAction(statement.action, context.action)) {
      return {
        matched: false,
        reason: `Action ${context.action} does not match statement actions`,
      };
    }

    // Check if resource matches
    if (!this.matchesResource(statement.resource, context.resource)) {
      return {
        matched: false,
        reason: `Resource ${context.resource} does not match statement resources`,
      };
    }

    // Check if principal matches (if specified)
    if (
      statement.principal &&
      !this.matchesPrincipal(statement.principal, context)
    ) {
      return {
        matched: false,
        reason: 'Principal does not match statement principals',
      };
    }

    // Check conditions (if specified)
    if (statement.condition) {
      const conditionResults = this.conditionEvaluator.evaluateConditions(
        statement.condition,
        {
          user: context.user,
          request: context.request,
          ...context.attributes,
        },
      );

      const allConditionsSatisfied = conditionResults.every(
        (result) => result.satisfied,
      );

      if (!allConditionsSatisfied) {
        return {
          matched: false,
          conditionResults,
          reason: 'One or more conditions were not satisfied',
        };
      }

      return {
        matched: true,
        conditionResults,
        reason: 'Statement matched with all conditions satisfied',
      };
    }

    // Statement matches without conditions
    return {
      matched: true,
      reason: 'Statement matched (no conditions specified)',
    };
  }

  /**
   * Check if the requested action matches the statement actions
   */
  private matchesAction(
    statementActions: string | string[],
    requestedAction: string,
  ): boolean {
    const actions = Array.isArray(statementActions)
      ? statementActions
      : [statementActions];

    return actions.some((action) => {
      if (action === '*') return true;
      if (action === requestedAction) return true;

      // Support wildcard patterns
      if (action.includes('*')) {
        const pattern = action.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(requestedAction);
      }

      return false;
    });
  }

  /**
   * Check if the requested resource matches the statement resources
   */
  private matchesResource(
    statementResources: string | string[],
    requestedResource: string,
  ): boolean {
    const resources = Array.isArray(statementResources)
      ? statementResources
      : [statementResources];

    return resources.some((resource) => {
      if (resource === '*') return true;
      if (resource === requestedResource) return true;

      // Support wildcard patterns in resources
      if (resource.includes('*')) {
        const pattern = resource.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(requestedResource);
      }

      return false;
    });
  }

  /**
   * Check if the principal matches the statement principals
   */
  private matchesPrincipal(
    statementPrincipal: string | string[] | Record<string, string | string[]>,
    context: PolicyEvaluationContext,
  ): boolean {
    // If principal is a string or array
    if (
      typeof statementPrincipal === 'string' ||
      Array.isArray(statementPrincipal)
    ) {
      const principals = Array.isArray(statementPrincipal)
        ? statementPrincipal
        : [statementPrincipal];

      return principals.some((principal) => {
        if (principal === '*') return true;
        if (principal === context.user.id) return true;
        if (principal === context.user.email) return true;

        // Check role-based principals
        if (principal.startsWith('role:')) {
          const roleName = principal.substring(5);
          return context.user.roles?.includes(roleName);
        }

        return false;
      });
    }

    // If principal is an object with type mapping
    if (typeof statementPrincipal === 'object') {
      for (const [principalType, principalValues] of Object.entries(
        statementPrincipal,
      )) {
        const values = Array.isArray(principalValues)
          ? principalValues
          : [principalValues];

        switch (principalType) {
          case 'AWS':
          case 'User':
            if (
              values.includes(context.user.id) ||
              values.includes(context.user.email)
            ) {
              return true;
            }
            break;
          case 'Role':
            if (context.user.roles?.some((role) => values.includes(role))) {
              return true;
            }
            break;
        }
      }
    }

    return false;
  }

  /**
   * Build evaluation context from request
   */
  buildEvaluationContext(
    user: any,
    action: string,
    resourcePattern: string,
    request: any,
  ): PolicyEvaluationContext {
    // Build resource ARN using resource handlers
    const resource = this.resourceHandlerRegistry.buildResourceArn(
      resourcePattern,
      request,
    );

    // Extract additional attributes
    const attributes = this.resourceHandlerRegistry.extractResourceAttributes(
      resourcePattern,
      request,
    );

    return {
      user: {
        id: user.id,
        email: user.email,
        roles: user.roles || [],
        ...user,
      },
      action,
      resource,
      request: {
        ipAddress: request.ip || request.connection?.remoteAddress,
        userAgent: request.headers?.['user-agent'],
        timestamp: new Date(),
        method: request.method,
        path: request.path || request.url,
        headers: request.headers,
        ...request,
      },
      attributes,
    };
  }
}
