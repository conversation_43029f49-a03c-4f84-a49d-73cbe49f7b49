import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const AUTHORIZATION_NAMESPACE = 'AUTHORIZATION';

type AuthorizationErrorCodes =
  // Role-related errors
  | 'ROLE_NOT_FOUND'
  | 'ROLE_ALREADY_EXISTS'
  | 'ROLE_CANNOT_DELETE_BUILTIN'
  | 'ROLE_CANNOT_MODIFY_BUILTIN'
  | 'ROLE_HAS_ACTIVE_USERS'
  | 'ROLE_ASSIGNMENT_NOT_FOUND'
  | 'ROLE_ASSIGNMENT_ALREADY_EXISTS'
  | 'ROLE_PERMISSION_INVALID'
  // Policy-related errors
  | 'POLICY_NOT_FOUND'
  | 'POLICY_ALREADY_EXISTS'
  | 'POLICY_CANNOT_DELETE_BUILTIN'
  | 'POLICY_CANNOT_MODIFY_BUILTIN'
  | 'POLICY_HAS_ACTIVE_ATTACHMENTS'
  | 'POLICY_ATTACHMENT_NOT_FOUND'
  | 'POLICY_ATTACHMENT_ALREADY_EXISTS'
  | 'POLICY_VERSION_UNSUPPORTED'
  | 'POLICY_STATEMENT_INVALID'
  | 'POLICY_ACTION_INVALID'
  | 'POLICY_RESOURCE_INVALID'
  | 'POLICY_EFFECT_INVALID'
  | 'POLICY_STATEMENT_LIMIT_EXCEEDED'
  // Evaluation-related errors
  | 'EVALUATION_FAILED';

export const AUTHORIZATION_ERROR_CODES: Record<
  AuthorizationErrorCodes,
  ErrorCode
> = {
  // Role-related errors
  ROLE_NOT_FOUND: {
    code: `${AUTHORIZATION_NAMESPACE}:10000`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Role not found',
  },
  ROLE_ALREADY_EXISTS: {
    code: `${AUTHORIZATION_NAMESPACE}:10001`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Role with this name already exists',
  },
  ROLE_CANNOT_DELETE_BUILTIN: {
    code: `${AUTHORIZATION_NAMESPACE}:10002`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Cannot delete built-in system role',
  },
  ROLE_CANNOT_MODIFY_BUILTIN: {
    code: `${AUTHORIZATION_NAMESPACE}:10003`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Cannot modify built-in system role',
  },
  ROLE_HAS_ACTIVE_USERS: {
    code: `${AUTHORIZATION_NAMESPACE}:10004`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Cannot delete role with active user assignments',
  },
  ROLE_ASSIGNMENT_NOT_FOUND: {
    code: `${AUTHORIZATION_NAMESPACE}:10005`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Role assignment not found',
  },
  ROLE_ASSIGNMENT_ALREADY_EXISTS: {
    code: `${AUTHORIZATION_NAMESPACE}:10006`,
    statusCode: HttpStatus.CONFLICT,
    message: 'User already has this role assigned',
  },
  ROLE_PERMISSION_INVALID: {
    code: `${AUTHORIZATION_NAMESPACE}:10007`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid permission specified for role',
  },

  // Policy-related errors
  POLICY_NOT_FOUND: {
    code: `${AUTHORIZATION_NAMESPACE}:10008`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Policy not found',
  },
  POLICY_ALREADY_EXISTS: {
    code: `${AUTHORIZATION_NAMESPACE}:10009`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Policy with this name already exists',
  },
  POLICY_CANNOT_DELETE_BUILTIN: {
    code: `${AUTHORIZATION_NAMESPACE}:10010`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Cannot delete built-in system policy',
  },
  POLICY_CANNOT_MODIFY_BUILTIN: {
    code: `${AUTHORIZATION_NAMESPACE}:10011`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Cannot modify built-in system policy',
  },
  POLICY_HAS_ACTIVE_ATTACHMENTS: {
    code: `${AUTHORIZATION_NAMESPACE}:10012`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Cannot delete policy with active attachments',
  },
  POLICY_ATTACHMENT_NOT_FOUND: {
    code: `${AUTHORIZATION_NAMESPACE}:10013`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Policy attachment not found',
  },
  POLICY_ATTACHMENT_ALREADY_EXISTS: {
    code: `${AUTHORIZATION_NAMESPACE}:10014`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Policy is already attached to this entity',
  },
  POLICY_VERSION_UNSUPPORTED: {
    code: `${AUTHORIZATION_NAMESPACE}:10015`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Policy document version is not supported',
  },
  POLICY_STATEMENT_INVALID: {
    code: `${AUTHORIZATION_NAMESPACE}:10016`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Policy statement is invalid',
  },
  POLICY_ACTION_INVALID: {
    code: `${AUTHORIZATION_NAMESPACE}:10017`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Policy action is invalid',
  },
  POLICY_RESOURCE_INVALID: {
    code: `${AUTHORIZATION_NAMESPACE}:10018`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Policy resource is invalid',
  },
  POLICY_EFFECT_INVALID: {
    code: `${AUTHORIZATION_NAMESPACE}:10019`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Policy effect must be Allow or Deny',
  },
  POLICY_STATEMENT_LIMIT_EXCEEDED: {
    code: `${AUTHORIZATION_NAMESPACE}:10020`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Policy contains too many statements',
  },

  // Evaluation-related errors
  EVALUATION_FAILED: {
    code: `${AUTHORIZATION_NAMESPACE}:10021`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Policy evaluation failed',
  },
};
