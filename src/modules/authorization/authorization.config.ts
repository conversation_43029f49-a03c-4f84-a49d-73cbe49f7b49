import validateConfig from '@common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { Transform } from 'class-transformer';
import { IsBoolean, IsInt, IsOptional, Max, <PERSON> } from 'class-validator';

export type AuthorizationConfigType = {
  enabled: boolean;
  stopOnFirstDeny: boolean;
  maxPoliciesPerEvaluation: number;
  enableCache: boolean;
  cacheTtl: number;
  logEvaluations: boolean;
  enablePerformanceMetrics: boolean;
  enableDebugMode: boolean;
  contextPrefix: string;
};

class EnvironmentVariablesValidator {
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value !== 'false')
  AUTHORIZATION_ENABLED: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value !== 'false')
  AUTHORIZATION_STOP_ON_FIRST_DENY: boolean;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  @Transform(({ value }) => Number(value))
  AUTHORIZATION_MAX_POLICIES_PER_EVALUATION: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value !== 'false')
  AUTHORIZATION_ENABLE_CACHE: boolean;

  @IsOptional()
  @IsInt()
  @Min(60)
  @Max(86400)
  @Transform(({ value }) => Number(value))
  AUTHORIZATION_CACHE_TTL: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => (value as string)?.toLowerCase() === 'true')
  AUTHORIZATION_LOG_EVALUATIONS: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => (value as string)?.toLowerCase() === 'true')
  AUTHORIZATION_ENABLE_PERFORMANCE_METRICS: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => (value as string)?.toLowerCase() === 'true')
  AUTHORIZATION_DEBUG_MODE: boolean;

  @IsOptional()
  AUTHORIZATION_CONTEXT_PREFIX: string;
}

export const authorizationConfig = registerAs<AuthorizationConfigType>(
  'authorization',
  () => {
    validateConfig(process.env, EnvironmentVariablesValidator);

    return {
      enabled: process.env.AUTHORIZATION_ENABLED !== 'false',
      stopOnFirstDeny: process.env.AUTHORIZATION_STOP_ON_FIRST_DENY !== 'false',
      maxPoliciesPerEvaluation: process.env
        .AUTHORIZATION_MAX_POLICIES_PER_EVALUATION
        ? parseInt(process.env.AUTHORIZATION_MAX_POLICIES_PER_EVALUATION, 10)
        : 100,
      enableCache: process.env.AUTHORIZATION_ENABLE_CACHE !== 'false',
      cacheTtl: process.env.AUTHORIZATION_CACHE_TTL
        ? parseInt(process.env.AUTHORIZATION_CACHE_TTL, 10)
        : 300, // 5 minutes
      logEvaluations: process.env.AUTHORIZATION_LOG_EVALUATIONS === 'true',
      enablePerformanceMetrics:
        process.env.AUTHORIZATION_ENABLE_PERFORMANCE_METRICS === 'true',
      enableDebugMode: process.env.AUTHORIZATION_DEBUG_MODE === 'true',
      contextPrefix: process.env.AUTHORIZATION_CONTEXT_PREFIX || 'myapp',
    };
  },
);
