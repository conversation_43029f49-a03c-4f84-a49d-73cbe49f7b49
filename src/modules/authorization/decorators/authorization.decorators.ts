import { applyDecorators, SetMetadata } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

// Metadata keys for Authorization decorators
export const AUTHORIZATION_ACTION_KEY = 'authorization:action';
export const AUTHORIZATION_RESOURCE_KEY = 'authorization:resource';
export const AUTHORIZATION_ROLE_KEY = 'authorization:role';
export const AUTHORIZATION_PERMISSION_KEY = 'authorization:permission';
export const AUTHORIZATION_POLICY_KEY = 'authorization:policy';
export const AUTHORIZATION_CONDITIONS_KEY = 'authorization:conditions';
export const AUTHORIZATION_SKIP_KEY = 'authorization:skip';

/**
 * Decorator to specify required action for Authorization evaluation
 */
export const AuthorizationAction = (action: string) =>
  SetMetadata(AUTHORIZATION_ACTION_KEY, action);

/**
 * Decorator to specify resource pattern for Authorization evaluation
 */
export const AuthorizationResource = (pattern: string) =>
  SetMetadata(AUTHORIZATION_RESOURCE_KEY, pattern);

/**
 * Decorator to require specific role
 */
export const RequireRole = (role: string) =>
  SetMetadata(AUTHORIZATION_ROLE_KEY, role);

/**
 * Decorator to require specific permission
 */
export const RequirePermission = (permission: string) =>
  SetMetadata(AUTHORIZATION_PERMISSION_KEY, permission);

/**
 * Decorator to require specific policy
 */
export const RequirePolicy = (policyName: string) =>
  SetMetadata(AUTHORIZATION_POLICY_KEY, policyName);

/**
 * Decorator to add additional conditions for policy evaluation
 */
export const AuthorizationConditions = (conditions: Record<string, any>) =>
  SetMetadata(AUTHORIZATION_CONDITIONS_KEY, conditions);

/**
 * Decorator to skip Authorization evaluation (useful for public routes or testing)
 */
export const SkipAuthorization = () =>
  SetMetadata(AUTHORIZATION_SKIP_KEY, true);

/**
 * Combined decorator for policy-based access control
 * @param action - The action to check (e.g., 'user:read', 'document:update')
 * @param resource - The resource pattern (e.g., 'arn:app:user:*:user/${params.id}')
 */
export const RequirePolicyAccess = (action: string, resource: string) =>
  applyDecorators(
    AuthorizationAction(action),
    AuthorizationResource(resource),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Insufficient permissions for this action',
    }),
  );

/**
 * Decorator for role-based access control
 * @param role - The required role name
 */
export const RequireRoleAccess = (role: string) =>
  applyDecorators(
    RequireRole(role),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Required role not assigned',
    }),
  );

/**
 * Decorator for permission-based access control
 * @param permission - The required permission string
 */
export const RequirePermissionAccess = (permission: string) =>
  applyDecorators(
    RequirePermission(permission),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Required permission not granted',
    }),
  );

/**
 * Combined decorator for role and permission requirements
 * @param role - The required role name
 * @param permission - The required permission string
 */
export const RequireRoleAndPermission = (role: string, permission: string) =>
  applyDecorators(
    RequireRole(role),
    RequirePermission(permission),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Required role and permission not satisfied',
    }),
  );

/**
 * Decorator for multiple role options (user needs at least one)
 * @param roles - Array of role names (user needs at least one)
 */
export const RequireAnyRole = (...roles: string[]) =>
  applyDecorators(
    SetMetadata(AUTHORIZATION_ROLE_KEY, roles),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - None of the required roles assigned',
    }),
  );

/**
 * Decorator for multiple permission options (user needs at least one)
 * @param permissions - Array of permission strings (user needs at least one)
 */
export const RequireAnyPermission = (...permissions: string[]) =>
  applyDecorators(
    SetMetadata(AUTHORIZATION_PERMISSION_KEY, permissions),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - None of the required permissions granted',
    }),
  );

/**
 * Decorator for admin-only access (shorthand for admin role requirement)
 */
export const RequireAdmin = () => RequireRoleAccess('admin');

/**
 * Decorator for self-access or admin (common pattern for user profile endpoints)
 * @param userIdParam - The parameter name that contains the user ID (default: 'id')
 */
export const RequireSelfOrAdmin = (userIdParam = 'id') =>
  applyDecorators(
    AuthorizationAction('user:access'),
    AuthorizationResource(`arn:app:user:*:user/\${params.${userIdParam}}`),
    AuthorizationConditions({
      selfAccess: true,
      adminOverride: true,
    }),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description:
        'Forbidden - Can only access own resources or requires admin role',
    }),
  );

/**
 * Decorator for resource owner access (user can access resources they own)
 * @param action - The action to check
 * @param resourcePattern - The resource pattern with owner field
 * @param ownerField - The field name that contains the owner ID (default: 'ownerId')
 */
export const RequireResourceOwner = (
  action: string,
  resourcePattern: string,
  ownerField = 'ownerId',
) =>
  applyDecorators(
    AuthorizationAction(action),
    AuthorizationResource(resourcePattern),
    AuthorizationConditions({
      ownershipCheck: true,
      ownerField,
    }),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Can only access resources you own',
    }),
  );

/**
 * Decorator for time-based access control
 * @param action - The action to check
 * @param resource - The resource pattern
 * @param timeWindow - Time window for access (e.g., '09:00-17:00')
 * @param timezone - Timezone for time window (default: 'UTC')
 */
export const RequireTimeBasedAccess = (
  action: string,
  resource: string,
  timeWindow: string,
  timezone = 'UTC',
) =>
  applyDecorators(
    AuthorizationAction(action),
    AuthorizationResource(resource),
    AuthorizationConditions({
      timeWindow,
      timezone,
    }),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Access not allowed during this time window',
    }),
  );

/**
 * Decorator for IP-based access control
 * @param action - The action to check
 * @param resource - The resource pattern
 * @param allowedIpRanges - Array of allowed IP ranges (CIDR notation)
 */
export const RequireIpBasedAccess = (
  action: string,
  resource: string,
  allowedIpRanges: string[],
) =>
  applyDecorators(
    AuthorizationAction(action),
    AuthorizationResource(resource),
    AuthorizationConditions({
      allowedIpRanges,
    }),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Access not allowed from this IP address',
    }),
  );

/**
 * Decorator for conditional access based on custom conditions
 * @param action - The action to check
 * @param resource - The resource pattern
 * @param conditions - Custom conditions for access
 */
export const RequireConditionalAccess = (
  action: string,
  resource: string,
  conditions: Record<string, any>,
) =>
  applyDecorators(
    AuthorizationAction(action),
    AuthorizationResource(resource),
    AuthorizationConditions(conditions),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing authentication token',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - Access conditions not met',
    }),
  );
