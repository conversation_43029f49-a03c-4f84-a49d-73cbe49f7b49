import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
  Max,
  Min,
  ValidateNested
} from 'class-validator';
import { PolicyEffect } from '../enums/policy-effect.enum';
import {
  PolicyDocument,
  PolicyStatement,
} from '../interfaces/policy.interface';

/**
 * DTO for policy statement validation
 */
export class PolicyStatementDto implements PolicyStatement {
  @ApiPropertyOptional({
    description: 'Statement ID',
    example: 'AllowUserReadOwnData',
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  sid?: string;

  @ApiProperty({
    description: 'Statement effect',
    enum: PolicyEffect,
  })
  @IsEnum(PolicyEffect)
  effect: PolicyEffect;

  @ApiPropertyOptional({
    description: 'Principal(s) affected by this statement',
    example: ['user:${user.id}', 'role:admin'],
    oneOf: [
      { type: 'string' },
      { type: 'array', items: { type: 'string' } },
      { type: 'object' },
    ],
  })
  @IsOptional()
  principal?: string | string[] | Record<string, string | string[]>;

  @ApiProperty({
    description: 'Action(s) covered by this statement',
    example: ['user:read', 'user:update'],
    oneOf: [{ type: 'string' }, { type: 'array', items: { type: 'string' } }],
  })
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  action: string | string[];

  @ApiProperty({
    description: 'Resource(s) covered by this statement',
    example: ['arn:app:user:*:user/${user.id}'],
    oneOf: [{ type: 'string' }, { type: 'array', items: { type: 'string' } }],
  })
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  resource: string | string[];

  @ApiPropertyOptional({
    description: 'Conditions for when this statement applies',
    example: {
      StringEquals: {
        'user:id': '${myapp:userid}',
      },
      IpAddress: {
        'myapp:sourceIp': '***********/24',
      },
    },
  })
  @IsOptional()
  @IsObject()
  condition?: Record<string, Record<string, any>>;
}

/**
 * DTO for policy document validation
 */
export class PolicyDocumentDto implements PolicyDocument {
  @ApiProperty({
    description: 'Policy document version',
    example: '2023-11-01',
  })
  @IsString()
  @Matches(/^2023-11-01$/, {
    message: 'Policy version must be 2023-11-01',
  })
  version: '2023-11-01';

  @ApiPropertyOptional({
    description: 'Policy document ID',
    example: 'UserSelfManagement',
  })
  @IsOptional()
  @IsString()
  @Length(1, 128)
  id?: string;

  @ApiProperty({
    description: 'Array of policy statements',
    type: [PolicyStatementDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PolicyStatementDto)
  @ArrayMaxSize(20)
  statement: PolicyStatementDto[];
}

/**
 * DTO for creating a new policy
 */
export class CreatePolicyInput {
  @ApiProperty({
    description: 'Unique name for the policy',
    example: 'UserSelfManagementPolicy',
    minLength: 2,
    maxLength: 128,
  })
  @IsString()
  @IsNotEmpty()
  @Length(2, 128)
  @Matches(/^[a-zA-Z][a-zA-Z0-9_-]*$/, {
    message:
      'Policy name must start with a letter and contain only alphanumeric characters, hyphens, and underscores',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the policy and its purpose',
    example: 'Allows users to read and update their own profile data',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @ApiProperty({
    description: 'Policy document containing statements',
    type: PolicyDocumentDto,
  })
  @ValidateNested()
  @Type(() => PolicyDocumentDto)
  document: PolicyDocumentDto;

  @ApiPropertyOptional({
    description: 'Whether the policy is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Priority for policy evaluation (higher = more important)',
    example: 10,
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(1000)
  priority?: number;

  @ApiPropertyOptional({
    description: 'Additional metadata tags',
    example: { department: 'engineering', purpose: 'self-service' },
  })
  @IsOptional()
  @IsObject()
  tags?: Record<string, string>;
}

/**
 * DTO for updating an existing policy
 */
export class UpdatePolicyDto {
  @ApiPropertyOptional({
    description: 'Updated policy name',
    minLength: 2,
    maxLength: 128,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Length(2, 128)
  @Matches(/^[a-zA-Z][a-zA-Z0-9_-]*$/)
  name?: string;

  @ApiPropertyOptional({
    description: 'Updated description',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Updated policy document',
    type: PolicyDocumentDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PolicyDocumentDto)
  document?: PolicyDocumentDto;

  @ApiPropertyOptional({
    description: 'Updated active status',
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Updated priority',
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(1000)
  priority?: number;

  @ApiPropertyOptional({
    description: 'Updated metadata tags',
  })
  @IsOptional()
  @IsObject()
  tags?: Record<string, string>;
}

/**
 * DTO for attaching a policy to a user
 */
export class AttachPolicyDto {
  @ApiProperty({
    description: 'User ID to attach the policy to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Policy ID to attach',
    example: '456e7890-e89b-12d3-a456-************',
  })
  @IsUUID()
  policyId: string;

  @ApiPropertyOptional({
    description: 'Optional expiration date for the policy attachment',
    type: Date,
  })
  @IsOptional()
  @Type(() => Date)
  expiresAt?: Date;

  @ApiPropertyOptional({
    description: 'Priority override for this attachment',
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(1000)
  priorityOverride?: number;

  @ApiPropertyOptional({
    description: 'Reason for the policy attachment',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @Length(0, 255)
  reason?: string;
}

/**
 * DTO for policy evaluation request
 */
export class EvaluatePolicyDto {
  @ApiProperty({
    description: 'Action to evaluate',
    example: 'user:read',
  })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({
    description: 'Resource to evaluate (ARN format)',
    example: 'arn:app:user:*:user/123',
  })
  @IsString()
  @IsNotEmpty()
  resource: string;

  @ApiPropertyOptional({
    description: 'Additional context for evaluation',
    example: {
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
      time: '2023-11-01T10:00:00Z',
    },
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

/**
 * DTO for policy query parameters
 */
export class ListPoliciesDto {
  @ApiPropertyOptional({
    description: 'Filter by active status',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Search by policy name',
    example: 'UserManagement',
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by action pattern',
    example: 'user:*',
  })
  @IsOptional()
  @IsString()
  action?: string;

  @ApiPropertyOptional({
    description: 'Filter by resource pattern',
    example: 'arn:app:user:*',
  })
  @IsOptional()
  @IsString()
  resource?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Sort field',
    enum: ['name', 'createdAt', 'priority'],
  })
  @IsOptional()
  @IsString()
  sortBy?: 'name' | 'createdAt' | 'priority' = 'name';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
}

/**
 * DTO for policy response data
 */
export class PolicyResponseDto {
  @ApiProperty({
    description: 'Policy ID',
  })
  id: string;

  @ApiProperty({
    description: 'Policy name',
  })
  name: string;

  @ApiProperty({
    description: 'Policy description',
  })
  description?: string;

  @ApiProperty({
    description: 'Policy version',
  })
  version: string;

  @ApiProperty({
    description: 'Policy document',
  })
  document: PolicyDocument;

  @ApiProperty({
    description: 'Whether the policy is active',
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Whether this is a built-in system policy',
  })
  isBuiltIn: boolean;

  @ApiProperty({
    description: 'Policy priority',
  })
  priority: number;

  @ApiProperty({
    description: 'Cached actions from policy',
    isArray: true,
    type: String,
  })
  actions: string[];

  @ApiProperty({
    description: 'Cached resources from policy',
    isArray: true,
    type: String,
  })
  resources: string[];

  @ApiProperty({
    description: 'Policy metadata tags',
  })
  tags?: Record<string, string>;

  @ApiProperty({
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}

/**
 * DTO for paginated policy responses
 */
export class PaginatedPoliciesResponseDto {
  @ApiProperty({
    description: 'Array of policies',
    type: [PolicyResponseDto],
  })
  @ValidateNested({ each: true })
  @Type(() => PolicyResponseDto)
  data: PolicyResponseDto[];

  @ApiProperty({
    description: 'Total number of policies',
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
  })
  totalPages: number;
}
