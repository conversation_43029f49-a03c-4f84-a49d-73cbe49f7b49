import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { RoleType } from '../enums/role-type.enum';

/**
 * DTO for creating a new role
 */
export class CreateRoleDto {
  @ApiProperty({
    description: 'Unique name for the role',
    example: 'content-moderator',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @Length(2, 100)
  @Transform(({ value }) => value?.trim()?.toLowerCase())
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the role and its purpose',
    example: 'Role for content moderation team members',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  description?: string;

  @ApiProperty({
    description: 'Type of role',
    enum: RoleType,
    example: RoleType.CUSTOM,
  })
  @IsEnum(RoleType)
  type: RoleType;

  @ApiPropertyOptional({
    description: 'Array of permission strings',
    example: ['user:read', 'content:moderate', 'report:create'],
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(100)
  permissions?: string[];

  @ApiPropertyOptional({
    description: 'Whether the role is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Priority for role evaluation (higher = more important)',
    example: 10,
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(1000)
  priority?: number;
}

/**
 * DTO for updating an existing role
 */
export class UpdateRoleDto {
  @ApiPropertyOptional({
    description: 'Updated name for the role',
    example: 'senior-content-moderator',
    minLength: 2,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Length(2, 100)
  @Transform(({ value }) => value?.trim()?.toLowerCase())
  name?: string;

  @ApiPropertyOptional({
    description: 'Updated description',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  description?: string;

  @ApiPropertyOptional({
    description: 'Updated permissions array',
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(100)
  permissions?: string[];

  @ApiPropertyOptional({
    description: 'Updated active status',
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Updated priority',
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(1000)
  priority?: number;
}

/**
 * DTO for assigning a role to a user
 */
export class AssignRoleDto {
  @ApiProperty({
    description: 'User ID to assign the role to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Role ID to assign',
    example: '456e7890-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiPropertyOptional({
    description: 'Optional expiration date for the role assignment',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({
    description: 'Optional conditions for when this role is active',
    example: { ipRanges: ['***********/24'], timeWindow: '09:00-17:00' },
  })
  @IsOptional()
  @IsObject()
  conditions?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Reason for the role assignment',
    example: 'Temporary access for project X',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @Length(0, 255)
  reason?: string;
}

/**
 * DTO for bulk role assignments
 */
export class BulkAssignRoleDto {
  @ApiProperty({
    description: 'Array of user IDs to assign the role to',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '789e0123-e89b-12d3-a456-************',
    ],
    isArray: true,
    type: String,
  })
  @IsArray()
  @IsUUID(undefined, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  userIds: string[];

  @ApiProperty({
    description: 'Role ID to assign',
    example: '456e7890-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiPropertyOptional({
    description: 'Optional expiration date for all assignments',
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({
    description: 'Reason for bulk assignment',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @Length(0, 255)
  reason?: string;
}

/**
 * DTO for role query parameters
 */
export class ListRolesDto {
  @ApiPropertyOptional({
    description: 'Filter by role type',
    enum: RoleType,
  })
  @IsOptional()
  @IsEnum(RoleType)
  type?: RoleType;

  @ApiPropertyOptional({
    description: 'Filter by active status',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Search by role name',
    example: 'moderator',
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  search?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Sort field',
    enum: ['name', 'createdAt', 'priority'],
  })
  @IsOptional()
  @IsString()
  sortBy?: 'name' | 'createdAt' | 'priority' = 'name';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
}

/**
 * DTO for role response data
 */
export class RoleResponseDto {
  @ApiProperty({
    description: 'Role ID',
    example: '456e7890-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Role name',
    example: 'content-moderator',
  })
  name: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Role for content moderation team members',
  })
  description?: string;

  @ApiProperty({
    description: 'Role type',
    enum: RoleType,
  })
  type: RoleType;

  @ApiProperty({
    description: 'Whether this is a built-in system role',
  })
  isBuiltIn: boolean;

  @ApiProperty({
    description: 'Whether the role is active',
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Role permissions',
    isArray: true,
    type: String,
  })
  permissions: string[];

  @ApiProperty({
    description: 'Role priority',
  })
  priority: number;

  @ApiProperty({
    description: 'Number of users assigned to this role',
  })
  userCount?: number;

  @ApiProperty({
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}

/**
 * DTO for paginated role responses
 */
export class PaginatedRolesResponseDto {
  @ApiProperty({
    description: 'Array of roles',
    type: [RoleResponseDto],
  })
  @ValidateNested({ each: true })
  @Type(() => RoleResponseDto)
  data: RoleResponseDto[];

  @ApiProperty({
    description: 'Total number of roles',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 20,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
