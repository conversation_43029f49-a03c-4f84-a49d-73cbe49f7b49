import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

/**
 * DTO for user role information
 */
export class UserRoleDto {
  @ApiProperty({
    description: 'Role ID',
    example: '456e7890-e89b-12d3-a456-426614174001',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Role name',
    example: 'content-moderator',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Role for content moderation team members',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Role type',
    example: 'custom',
  })
  @Expose()
  type: string;

  @ApiProperty({
    description: 'Role permissions',
    example: ['user:read', 'content:moderate'],
    isArray: true,
    type: String,
  })
  @Expose()
  permissions: string[];

  @ApiProperty({
    description: 'Whether the role is active',
    example: true,
  })
  @Expose()
  isActive: boolean;

  @ApiProperty({
    description: 'Role priority',
    example: 10,
  })
  @Expose()
  priority: number;
}

/**
 * DTO for user roles response
 */
export class UserRolesResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    description: 'Array of user roles',
    type: [UserRoleDto],
  })
  @Type(() => UserRoleDto)
  @ValidateNested({ each: true })
  @Expose()
  roles: UserRoleDto[];

  @ApiProperty({
    description: 'Total number of roles',
    example: 3,
  })
  @Expose()
  totalRoles: number;
}

/**
 * DTO for role permissions information
 */
export class RolePermissionsDto {
  @ApiProperty({
    description: 'Role name',
    example: 'content-moderator',
  })
  @Expose()
  roleName: string;

  @ApiProperty({
    description: 'Permissions from this role',
    example: ['user:read', 'content:moderate'],
    isArray: true,
    type: String,
  })
  @Expose()
  permissions: string[];
}

/**
 * DTO for policy permissions information
 */
export class PolicyPermissionsDto {
  @ApiProperty({
    description: 'Policy name',
    example: 'UserSelfManagementPolicy',
  })
  @Expose()
  policyName: string;

  @ApiProperty({
    description: 'Actions from this policy',
    example: ['user:read', 'user:update'],
    isArray: true,
    type: String,
  })
  @Expose()
  actions: string[];

  @ApiProperty({
    description: 'Resources from this policy',
    example: ['arn:app:user:*:user/${user.id}'],
    isArray: true,
    type: String,
  })
  @Expose()
  resources: string[];

  @ApiProperty({
    description: 'Policy effect',
    example: 'Allow',
  })
  @Expose()
  effect: string;

  @ApiPropertyOptional({
    description: 'Source of the policy (direct or role-based)',
    example: 'direct',
  })
  @Expose()
  source?: string;
}

/**
 * DTO for user permissions response
 */
export class UserPermissionsResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    description: 'Permissions structure',
    type: 'object',
    properties: {
      fromRoles: {
        type: 'array',
        items: { $ref: '#/components/schemas/RolePermissionsDto' },
      },
      fromPolicies: {
        type: 'array',
        items: { $ref: '#/components/schemas/PolicyPermissionsDto' },
      },
      effectivePermissions: {
        type: 'array',
        items: { type: 'string' },
      },
    },
  })
  @Expose()
  permissions: {
    fromRoles: RolePermissionsDto[];
    fromPolicies: PolicyPermissionsDto[];
    effectivePermissions: string[];
  };

  @ApiProperty({
    description: 'Total number of effective permissions',
    example: 15,
  })
  @Expose()
  totalPermissions: number;
}

/**
 * DTO for direct policy information
 */
export class DirectPolicyDto {
  @ApiProperty({
    description: 'Policy ID',
    example: '789e0123-e89b-12d3-a456-426614174003',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Policy name',
    example: 'UserSelfManagementPolicy',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Policy description',
    example: 'Allows users to manage their own profile',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Policy effect',
    example: 'Allow',
  })
  @Expose()
  effect: string;

  @ApiProperty({
    description: 'Policy actions',
    example: ['user:read', 'user:update'],
    isArray: true,
    type: String,
  })
  @Expose()
  actions: string[];

  @ApiProperty({
    description: 'Policy resources',
    example: ['arn:app:user:*:user/${user.id}'],
    isArray: true,
    type: String,
  })
  @Expose()
  resources: string[];

  @ApiProperty({
    description: 'Whether the policy is active',
    example: true,
  })
  @Expose()
  isActive: boolean;

  @ApiProperty({
    description: 'Policy priority',
    example: 10,
  })
  @Expose()
  priority: number;
}

/**
 * DTO for role-based policies
 */
export class RolePolicyDto {
  @ApiProperty({
    description: 'Role name',
    example: 'content-moderator',
  })
  @Expose()
  roleName: string;

  @ApiProperty({
    description: 'Policies attached to this role',
    type: [DirectPolicyDto],
  })
  @Type(() => DirectPolicyDto)
  @ValidateNested({ each: true })
  @Expose()
  policies: DirectPolicyDto[];
}

/**
 * DTO for user policies response
 */
export class UserPoliciesResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    description: 'Direct policy attachments',
    type: [DirectPolicyDto],
  })
  @Type(() => DirectPolicyDto)
  @ValidateNested({ each: true })
  @Expose()
  directPolicies: DirectPolicyDto[];

  @ApiProperty({
    description: 'Role-based policies',
    type: [RolePolicyDto],
  })
  @Type(() => RolePolicyDto)
  @ValidateNested({ each: true })
  @Expose()
  rolePolicies: RolePolicyDto[];

  @ApiProperty({
    description: 'Total number of policies',
    example: 5,
  })
  @Expose()
  totalPolicies: number;
}

/**
 * DTO for user capabilities
 */
export class UserCapabilitiesDto {
  @ApiProperty({
    description: 'Can manage users',
    example: false,
  })
  @Expose()
  canManageUsers: boolean;

  @ApiProperty({
    description: 'Can manage roles',
    example: false,
  })
  @Expose()
  canManageRoles: boolean;

  @ApiProperty({
    description: 'Can manage policies',
    example: false,
  })
  @Expose()
  canManagePolicies: boolean;

  @ApiProperty({
    description: 'Is administrator',
    example: false,
  })
  @Expose()
  isAdministrator: boolean;

  @ApiProperty({
    description: 'Has elevated access',
    example: false,
  })
  @Expose()
  hasElevatedAccess: boolean;
}

/**
 * DTO for user access summary
 */
export class UserAccessSummaryDto {
  @ApiProperty({
    description: 'Number of roles',
    example: 2,
  })
  @Expose()
  rolesCount: number;

  @ApiProperty({
    description: 'Number of direct policies',
    example: 1,
  })
  @Expose()
  directPoliciesCount: number;

  @ApiProperty({
    description: 'Number of role-based policies',
    example: 3,
  })
  @Expose()
  rolePoliciesCount: number;

  @ApiProperty({
    description: 'Total number of effective permissions',
    example: 15,
  })
  @Expose()
  totalPermissions: number;

  @ApiProperty({
    description: 'User capabilities',
    type: UserCapabilitiesDto,
  })
  @Type(() => UserCapabilitiesDto)
  @ValidateNested()
  @Expose()
  capabilities: UserCapabilitiesDto;
}

/**
 * DTO for role summary information
 */
export class RoleSummaryDto {
  @ApiProperty({
    description: 'Role name',
    example: 'content-moderator',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Role type',
    example: 'custom',
  })
  @Expose()
  type: string;

  @ApiProperty({
    description: 'Number of permissions in this role',
    example: 5,
  })
  @Expose()
  permissionsCount: number;
}

/**
 * DTO for recent activity information
 */
export class RecentActivityDto {
  @ApiProperty({
    description: 'Last role assignment date',
    example: null,
    required: false,
  })
  @Expose()
  lastRoleAssignment?: Date | null;

  @ApiProperty({
    description: 'Last policy attachment date',
    example: null,
    required: false,
  })
  @Expose()
  lastPolicyAttachment?: Date | null;
}

/**
 * DTO for user access summary response
 */
export class UserAccessSummaryResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    description: 'Access summary',
    type: UserAccessSummaryDto,
  })
  @Type(() => UserAccessSummaryDto)
  @ValidateNested()
  @Expose()
  summary: UserAccessSummaryDto;

  @ApiProperty({
    description: 'Role summaries',
    type: [RoleSummaryDto],
  })
  @Type(() => RoleSummaryDto)
  @ValidateNested({ each: true })
  @Expose()
  roles: RoleSummaryDto[];

  @ApiProperty({
    description: 'Recent activity information',
    type: RecentActivityDto,
  })
  @Type(() => RecentActivityDto)
  @ValidateNested()
  @Expose()
  recentActivity: RecentActivityDto;
}
