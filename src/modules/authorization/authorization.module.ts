import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

// Configuration
import { authorizationConfig } from './authorization.config';

// Entities
import {
  PolicyEntity,
  RoleEntity,
  RolePolicyEntity,
  UserPolicyEntity,
  UserRoleEntity,
} from './entities';

// Services
import {
  AdminResourceHandler,
  ConditionEvaluatorService,
  DefaultResourceHandler,
  DocumentResourceHandler,
  PolicyEngineService,
  PolicyService,
  ResourceHandlerRegistry,
  RoleService,
  UserResourceHandler,
  UserRoleService,
} from './services';

// Guards
import { AuthorizationGuard } from './guards';

// Controllers
import {
  PolicyController,
  RoleController,
  UserRoleController,
} from './controllers';

// Import UserEntity from the existing user module
import { UserEntity } from '@app/modules/user/entities/user.entity';

@Global()
@Module({
  imports: [
    // Load Authorization configuration
    ConfigModule.forFeature(authorizationConfig),

    // Register entities with TypeORM
    TypeOrmModule.forFeature([
      // Authorization entities
      RoleEntity,
      PolicyEntity,
      UserRoleEntity,
      RolePolicyEntity,
      UserPolicyEntity,

      // External entities we depend on
      UserEntity,
    ]),
  ],
  providers: [
    // Core services
    ConditionEvaluatorService,
    ResourceHandlerRegistry,
    PolicyEngineService,
    RoleService,
    PolicyService,
    UserRoleService,

    // Resource handlers
    DefaultResourceHandler,
    UserResourceHandler,
    DocumentResourceHandler,
    AdminResourceHandler,

    // Guards
    AuthorizationGuard,

    // Resource handler registration
    {
      provide: 'AUTHORIZATION_RESOURCE_HANDLERS_INIT',
      useFactory: (
        registry: ResourceHandlerRegistry,
        userHandler: UserResourceHandler,
        docHandler: DocumentResourceHandler,
        adminHandler: AdminResourceHandler,
      ) => {
        // Register specialized resource handlers
        registry.registerHandler('user', userHandler);
        registry.registerHandler('document', docHandler);
        registry.registerHandler('file', docHandler); // Alias for document handler
        registry.registerHandler('admin', adminHandler);
        registry.registerHandler('iam', adminHandler); // Alias for admin handler

        return registry;
      },
      inject: [
        ResourceHandlerRegistry,
        UserResourceHandler,
        DocumentResourceHandler,
        AdminResourceHandler,
      ],
    },
  ],
  controllers: [RoleController, PolicyController, UserRoleController],
  exports: [
    // Export main services for use in other modules
    RoleService,
    PolicyService,
    PolicyEngineService,
    UserRoleService,
    AuthorizationGuard,

    // Export resource handler registry for custom handlers
    ResourceHandlerRegistry,

    // Export TypeORM repositories for direct access if needed
    TypeOrmModule,
  ],
})
export class AuthorizationModule {
  constructor(
    private readonly resourceHandlerRegistry: ResourceHandlerRegistry,
  ) {
    // Additional initialization if needed
    this.initializeModule();
  }

  private initializeModule(): void {
    // Any additional module initialization logic
    console.log('Authorization Module initialized successfully');
  }

  /**
   * For root module configuration with custom options
   */
  static forRoot(options?: {
    enableDebugMode?: boolean;
    customResourceHandlers?: Array<{
      name: string;
      handler: any;
    }>;
  }) {
    return {
      module: AuthorizationModule,
      providers: [
        ...(options?.customResourceHandlers?.map(({ name, handler }) => ({
          provide: `CUSTOM_RESOURCE_HANDLER_${name.toUpperCase()}`,
          useClass: handler,
        })) || []),

        {
          provide: 'AUTHORIZATION_MODULE_OPTIONS',
          useValue: options || {},
        },
      ],
    };
  }
}
