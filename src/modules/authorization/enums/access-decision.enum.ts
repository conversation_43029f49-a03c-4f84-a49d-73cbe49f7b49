/**
 * Represents the final decision result from policy evaluation
 */
export enum AccessDecision {
  /**
   * Access is explicitly allowed by one or more policies
   */
  ALLOW = 'ALLOW',

  /**
   * Access is explicitly denied by one or more policies
   */
  DENY = 'DENY',

  /**
   * No applicable policies found, default to deny (principle of least privilege)
   */
  NOT_APPLICABLE = 'NOT_APPLICABLE',

  /**
   * Error occurred during policy evaluation
   */
  INDETERMINATE = 'INDETERMINATE',
}
