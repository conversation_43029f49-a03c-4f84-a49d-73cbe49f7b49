/**
 * Defines the different types of roles in the PBAC system
 */
export enum RoleType {
  /**
   * System-defined roles that come with the application (e.g., Admin, User)
   * These roles cannot be deleted and have special handling
   */
  SYSTEM = 'SYSTEM',

  /**
   * Custom roles created by administrators for specific business needs
   * These can be modified and deleted by users with appropriate permissions
   */
  CUSTOM = 'CUSTOM',

  /**
   * Temporary roles with automatic expiration
   * Used for short-term access grants or emergency situations
   */
  TEMPORARY = 'TEMPORARY',

  /**
   * Service roles for automated systems and integrations
   * Used by applications, APIs, and service accounts
   */
  SERVICE = 'SERVICE',
}
