import { AccessDecision } from '../enums/access-decision.enum';
import { PolicyEffect } from '../enums/policy-effect.enum';

/**
 * AWS-style policy document structure
 */
export interface PolicyDocument {
  version: '2023-11-01';
  id?: string;
  statement: PolicyStatement[];
}

/**
 * Individual policy statement within a policy document
 */
export interface PolicyStatement {
  /**
   * Statement ID - optional unique identifier for the statement
   */
  sid?: string;

  /**
   * Effect of the statement - Allow or Deny
   */
  effect: PolicyEffect;

  /**
   * Principal(s) to which this statement applies
   * Can be user IDs, role names, or wildcard patterns
   */
  principal?: string | string[] | PrincipalMap;

  /**
   * Action(s) that this statement applies to
   * Supports wildcards and action patterns
   */
  action: string | string[];

  /**
   * Resource(s) that this statement applies to
   * Uses ARN-style resource identifiers
   */
  resource: string | string[];

  /**
   * Conditions that must be met for this statement to apply
   */
  condition?: PolicyConditions;

  /**
   * Additional context for the statement
   */
  notAction?: string | string[];
  notResource?: string | string[];
  notPrincipal?: string | string[] | PrincipalMap;
}

/**
 * Principal mapping for complex principal specifications
 */
export interface PrincipalMap {
  [key: string]: string | string[];
}

/**
 * Policy conditions using AWS-style condition operators
 */
export interface PolicyConditions {
  // String conditions
  StringEquals?: ConditionMap;
  StringNotEquals?: ConditionMap;
  StringLike?: ConditionMap;
  StringNotLike?: ConditionMap;

  // Numeric conditions
  NumericEquals?: ConditionMap;
  NumericNotEquals?: ConditionMap;
  NumericLessThan?: ConditionMap;
  NumericLessThanEquals?: ConditionMap;
  NumericGreaterThan?: ConditionMap;
  NumericGreaterThanEquals?: ConditionMap;

  // Date conditions
  DateEquals?: ConditionMap;
  DateNotEquals?: ConditionMap;
  DateLessThan?: ConditionMap;
  DateLessThanEquals?: ConditionMap;
  DateGreaterThan?: ConditionMap;
  DateGreaterThanEquals?: ConditionMap;

  // Boolean conditions
  Bool?: ConditionMap;

  // IP address conditions
  IpAddress?: ConditionMap;
  NotIpAddress?: ConditionMap;

  // Null conditions
  Null?: ConditionMap;

  // Array conditions
  ForAllValues?: ConditionOperatorMap;
  ForAnyValue?: ConditionOperatorMap;

  // Custom conditions
  [key: string]: ConditionMap | ConditionOperatorMap | undefined;
}

/**
 * Condition value mapping
 */
export interface ConditionMap {
  [key: string]: string | string[] | number | number[] | boolean;
}

/**
 * Nested condition operator mapping for complex conditions
 */
export interface ConditionOperatorMap {
  [operator: string]: ConditionMap;
}

/**
 * Context information available during policy evaluation
 */
export interface PolicyEvaluationContext {
  /**
   * The authenticated user making the request
   */
  user: {
    id: string;
    email: string;
    roles?: string[];
    [key: string]: any;
  };

  /**
   * The action being requested
   */
  action: string;

  /**
   * The resource being accessed (ARN format)
   */
  resource: string;

  /**
   * Request context information
   */
  request: {
    ipAddress?: string;
    userAgent?: string;
    timestamp: Date;
    method?: string;
    path?: string;
    headers?: Record<string, string>;
    [key: string]: any;
  };

  /**
   * Additional context attributes
   */
  attributes?: Record<string, any>;
}

/**
 * Result of policy evaluation
 */
export interface PolicyEvaluationResult {
  /**
   * Final access decision
   */
  decision: AccessDecision;

  /**
   * Whether access is allowed
   */
  allowed: boolean;

  /**
   * Policies that were evaluated
   */
  evaluatedPolicies: PolicyEvaluationDetail[];

  /**
   * Reason for the decision
   */
  reason?: string;

  /**
   * Any warnings or additional information
   */
  warnings?: string[];

  /**
   * Evaluation metadata
   */
  metadata?: {
    evaluationTime: number;
    policiesConsidered: number;
    statementsEvaluated: number;
    [key: string]: any;
  };
}

/**
 * Detailed result for individual policy evaluation
 */
export interface PolicyEvaluationDetail {
  /**
   * Policy ID
   */
  policyId: string;

  /**
   * Policy name
   */
  policyName: string;

  /**
   * Whether this policy matched the request
   */
  matched: boolean;

  /**
   * Effect of the matching statement
   */
  effect?: PolicyEffect;

  /**
   * Matching statement ID
   */
  statementId?: string;

  /**
   * Condition evaluation results
   */
  conditionResults?: ConditionEvaluationResult[];

  /**
   * Reason why this policy matched or didn't match
   */
  reason?: string;
}

/**
 * Result of condition evaluation
 */
export interface ConditionEvaluationResult {
  /**
   * Condition operator (e.g., StringEquals, NumericLessThan)
   */
  operator: string;

  /**
   * Condition key being tested
   */
  key: string;

  /**
   * Expected value(s)
   */
  expectedValue: any;

  /**
   * Actual value from context
   */
  actualValue: any;

  /**
   * Whether the condition was satisfied
   */
  satisfied: boolean;

  /**
   * Additional details about the evaluation
   */
  details?: string;
}

/**
 * Resource handler interface for custom resource resolution
 */
export interface ResourceHandler {
  /**
   * Build resource ARN from request parameters
   */
  getResourceArn(request: any, pattern: string): string;

  /**
   * Extract resource attributes for condition evaluation
   */
  extractAttributes(request: any): Record<string, any>;

  /**
   * Validate resource access permissions
   */
  validateAccess?(context: PolicyEvaluationContext): Promise<boolean>;
}

/**
 * Policy engine configuration
 */
export interface PolicyEngineConfig {
  /**
   * Default decision when no policies apply
   */
  defaultDecision: AccessDecision;

  /**
   * Whether to allow evaluation to continue after first deny
   */
  stopOnFirstDeny: boolean;

  /**
   * Maximum number of policies to evaluate
   */
  maxPoliciesPerEvaluation: number;

  /**
   * Whether to cache policy evaluation results
   */
  enableCache: boolean;

  /**
   * Cache TTL in seconds
   */
  cacheTtl: number;

  /**
   * Custom resource handlers
   */
  resourceHandlers?: Map<string, ResourceHandler>;
}
