import { RoleType } from '../enums/role-type.enum';

/**
 * Interface for role creation
 */
export interface CreateRoleInput {
  name: string;
  description?: string;
  type: RoleType;
  permissions?: string[];
  isActive?: boolean;
  priority?: number;
}

/**
 * Interface for role updates
 */
export interface UpdateRoleInput {
  name?: string;
  description?: string;
  permissions?: string[];
  isActive?: boolean;
  priority?: number;
}

/**
 * Interface for role assignment to users
 */
export interface AssignRoleInput {
  userId: string;
  roleId: string;
  expiresAt?: Date;
  conditions?: Record<string, any>;
  reason?: string;
}

/**
 * Interface for role query parameters
 */
export interface RoleQueryParams {
  type?: RoleType;
  isActive?: boolean;
  name?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'priority';
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Interface for role with user assignments
 */
export interface RoleWithUsers {
  id: string;
  name: string;
  description?: string;
  type: RoleType;
  permissions: string[];
  isActive: boolean;
  priority: number;
  userCount: number;
  users: UserRoleAssignment[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for user role assignment details
 */
export interface UserRoleAssignment {
  userId: string;
  userEmail: string;
  userFirstName: string;
  userLastName: string;
  assignedAt: Date;
  assignedBy?: string;
  expiresAt?: Date;
  isActive: boolean;
  isExpired: boolean;
}

/**
 * Interface for role policy attachment
 */
export interface AttachPolicyToRoleInput {
  roleId: string;
  policyId: string;
  priorityOverride?: number;
  conditions?: Record<string, any>;
}

/**
 * Interface for role permissions summary
 */
export interface RolePermissionsSummary {
  roleId: string;
  roleName: string;
  directPermissions: string[];
  policyPermissions: PolicyPermissionSummary[];
  effectivePermissions: string[];
  totalPermissions: number;
}

/**
 * Interface for policy permissions within a role
 */
export interface PolicyPermissionSummary {
  policyId: string;
  policyName: string;
  effect: string;
  actions: string[];
  resources: string[];
  conditions?: any;
}

/**
 * Interface for role hierarchy (if implementing hierarchical roles)
 */
export interface RoleHierarchy {
  roleId: string;
  roleName: string;
  parentRoleId?: string;
  parentRoleName?: string;
  children: RoleHierarchy[];
  level: number;
  inheritedPermissions: string[];
}

/**
 * Interface for bulk role operations
 */
export interface BulkRoleOperation {
  operation: 'assign' | 'revoke' | 'update';
  userIds: string[];
  roleIds?: string[];
  expiresAt?: Date;
  reason?: string;
}

/**
 * Interface for role assignment validation
 */
export interface RoleAssignmentValidation {
  isValid: boolean;
  warnings: string[];
  errors: string[];
  conflicts: RoleConflict[];
}

/**
 * Interface for role conflicts
 */
export interface RoleConflict {
  conflictType: 'permission_overlap' | 'role_hierarchy' | 'temporal_conflict';
  description: string;
  affectedRoles: string[];
  severity: 'low' | 'medium' | 'high';
  recommendation?: string;
}
