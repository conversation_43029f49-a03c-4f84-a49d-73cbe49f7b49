# PBAC API Documentation

## Overview

The PBAC (Policy-Based Access Control) module provides REST APIs for managing roles, policies, and access control evaluation. All APIs require authentication and appropriate authorization.

**Base URL**: `https://your-app.com/api/v1/pbac`
**API Version**: v1
**Content Type**: `application/json`

## Authentication

All PBAC APIs require JWT authentication:

```http
Authorization: Bearer <your-jwt-token>
```

## Role Management APIs

### List Roles
```http
GET /api/v1/pbac/roles?page=1&limit=10&search=admin&type=BUILT_IN
```

**Response:**
```json
{
  "data": [{
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "admin",
    "description": "Administrator role",
    "type": "BUILT_IN",
    "permissions": ["*:*"],
    "isActive": true,
    "priority": 100
  }],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### Create Role
```http
POST /api/v1/pbac/roles
```

**Request:**
```json
{
  "name": "content-moderator",
  "description": "Content moderation team role",
  "type": "CUSTOM",
  "permissions": ["content:read", "content:moderate", "user:read"],
  "isActive": true,
  "priority": 15
}
```

### Update Role
```http
PUT /api/v1/pbac/roles/{roleId}
```

### Delete Role
```http
DELETE /api/v1/pbac/roles/{roleId}
```

### Assign Role to User
```http
POST /api/v1/pbac/roles/{roleId}/users
```

**Request:**
```json
{
  "userId": "user-123",
  "expiresAt": "2024-12-31T23:59:59.000Z",
  "reason": "Promoted to content moderator"
}
```

### Bulk Operations
```http
POST /api/v1/pbac/roles/bulk-operations
```

**Request:**
```json
{
  "operation": "assign",
  "userIds": ["user-1", "user-2"],
  "roleIds": ["role-123"],
  "reason": "Bulk assignment"
}
```

## Policy Management APIs

### List Policies
```http
GET /api/v1/pbac/policies?page=1&limit=10&search=SelfAccess
```

### Create Policy
```http
POST /api/v1/pbac/policies
```

**Request:**
```json
{
  "name": "SelfAccessPolicy",
  "description": "Allow users to access their own data",
  "document": {
    "version": "2023-11-01",
    "statement": [{
      "effect": "Allow",
      "action": ["user:read", "user:update"],
      "resource": ["arn:app:user:*:*:user/*"],
      "condition": {
        "StringEquals": {
          "user:id": "${request.params.id}"
        }
      }
    }]
  },
  "isActive": true,
  "tags": {
    "category": "self-service"
  }
}
```

### Policy Conditions

Supported condition operators:

| Operator | Description | Example |
|----------|-------------|---------|
| `StringEquals` | Exact string match | `"user:department": "engineering"` |
| `StringLike` | Wildcard pattern | `"user:email": "*@company.com"` |
| `NumericEquals` | Numeric equality | `"user:age": 25` |
| `NumericLessThan` | Numeric less than | `"order:amount": 1000` |
| `DateGreaterThan` | Date after | `"myapp:CurrentTime": "2024-01-01T00:00:00Z"` |
| `IpAddress` | IP/CIDR match | `"myapp:SourceIp": ["***********/24"]` |
| `Bool` | Boolean value | `"user:isActive": "true"` |

### Attach Policy
```http
POST /api/v1/pbac/policies/{policyId}/users
POST /api/v1/pbac/policies/{policyId}/roles
```

## Evaluation APIs

### Evaluate Access
```http
POST /api/v1/pbac/policies/evaluate
```

**Request:**
```json
{
  "userId": "user-123",
  "action": "user:read",
  "resource": "arn:app:user:*:*:user/456",
  "context": {
    "ipAddress": "*************",
    "department": "engineering"
  }
}
```

**Response:**
```json
{
  "allowed": true,
  "decision": "Allow",
  "reason": "Access granted by SelfAccessPolicy",
  "evaluatedPolicies": [{
    "policyId": "policy-123",
    "policyName": "SelfAccessPolicy",
    "effect": "Allow",
    "matched": true
  }],
  "metadata": {
    "evaluationTime": 15,
    "policiesChecked": 3
  }
}
```

## Error Handling

Standard HTTP status codes with detailed error information:

```json
{
  "error": {
    "code": "ROLE_NOT_FOUND",
    "message": "Role with ID 'invalid-id' not found",
    "statusCode": 404,
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `UNAUTHORIZED` | 401 | Invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `ROLE_NOT_FOUND` | 404 | Role does not exist |
| `POLICY_NOT_FOUND` | 404 | Policy does not exist |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `ROLE_ALREADY_ASSIGNED` | 409 | Role already assigned |

## Examples

### Complete Workflow
```bash
# Create role
curl -X POST https://api.example.com/api/v1/pbac/roles \
  -H "Authorization: Bearer TOKEN" \
  -d '{"name": "analyst", "permissions": ["analytics:read"]}'

# Assign to user  
curl -X POST https://api.example.com/api/v1/pbac/roles/ROLE_ID/users \
  -H "Authorization: Bearer TOKEN" \
  -d '{"userId": "user-123"}'

# Create policy
curl -X POST https://api.example.com/api/v1/pbac/policies \
  -H "Authorization: Bearer TOKEN" \
  -d '{"name": "AnalystPolicy", "document": {...}}'

# Evaluate access
curl -X POST https://api.example.com/api/v1/pbac/policies/evaluate \
  -H "Authorization: Bearer TOKEN" \
  -d '{"userId": "user-123", "action": "analytics:read"}'
```

For detailed usage examples, see the [Usage Guide](./USAGE.md).