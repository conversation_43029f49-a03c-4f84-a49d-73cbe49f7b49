# Configurable Context Prefix

The authorization system now supports configurable context prefixes for policy condition variables, replacing the hardcoded "myapp:" prefix with a customizable option.

## Configuration

### Environment Variable

Set the context prefix using the environment variable:

```bash
# Default is 'aws'
AUTHORIZATION_CONTEXT_PREFIX=aws

# Custom prefix example
AUTHORIZATION_CONTEXT_PREFIX=myapp
```

### Configuration Type

The configuration is defined in `AuthorizationConfigType`:

```typescript
export type AuthorizationConfigType = {
  // ... other config options
  contextPrefix: string;
};
```

## Usage Examples

### Default AWS-style Context Variables

With the default `aws` prefix:

```typescript
const policy = {
  version: '2023-11-01',
  statement: [
    {
      effect: 'Allow',
      action: ['user:read'],
      resource: ['user:${myapp:userid}'],
      condition: {
        StringEquals: {
          'myapp:userid': 'user123',
        },
      },
    },
  ],
};
```

### Custom Context Prefix

With a custom prefix `myapp`:

```typescript
const policy = {
  version: '2023-11-01',
  statement: [
    {
      effect: 'Allow',
      action: ['user:read'], 
      resource: ['user:${myapp:userid}'],
      condition: {
        StringEquals: {
          'myapp:userid': 'user123',
        },
      },
    },
  ],
};
```

## Supported Context Variables

The following context variables are supported with any configured prefix:

| Variable | Description | Context Source |
|----------|-------------|----------------|
| `{prefix}:userid` | Current user ID | `context.user?.id` |
| `{prefix}:username` | Current username/email | `context.user?.email \|\| context.user?.username` |
| `{prefix}:sourceIp` | Request IP address | `context.request?.ipAddress` |
| `{prefix}:userAgent` | User agent string | `context.request?.userAgent` |
| `{prefix}:currentTime` | Current timestamp | `context.request?.timestamp \|\| new Date()` |
| `{prefix}:requestedRegion` | Requested region | `context.request?.region \|\| 'us-east-1'` |

## Implementation Details

### Service Configuration

The `ConditionEvaluatorService` now injects the authorization configuration:

```typescript
@Injectable()
export class ConditionEvaluatorService {
  constructor(
    @Inject(authorizationConfig.KEY)
    private readonly config: ConfigType<typeof authorizationConfig>,
  ) {}

  private getContextValue(key: string, context: Record<string, any>): any {
    const prefix = `${this.config.contextPrefix}:`;
    if (key.startsWith(prefix)) {
      return this.getContextVariableValue(key, context);
    }
    // ... rest of implementation
  }
}
```

### Context Variable Resolution

The service dynamically resolves context variables based on the configured prefix:

```typescript
private getContextVariableValue(key: string, context: Record<string, any>): any {
  const prefix = `${this.config.contextPrefix}:`;
  const variableName = key.substring(prefix.length);

  switch (variableName) {
    case 'userid':
      return context.user?.id;
    case 'username':
      return context.user?.email || context.user?.username;
    // ... other cases
  }
}
```

## Migration from Hardcoded AWS Prefix

### Before (Hardcoded)
```typescript
// Only supported myapp: prefix
if (key.startsWith('myapp:')) {
  return this.getAwsContextValue(key, context);
}
```

### After (Configurable)
```typescript
// Supports any configured prefix
const prefix = `${this.config.contextPrefix}:`;
if (key.startsWith(prefix)) {
  return this.getContextVariableValue(key, context);
}
```

## Benefits

1. **Flexibility**: Use any prefix that fits your application's naming conventions
2. **Backward Compatibility**: Default `aws` prefix maintains existing behavior
3. **Customization**: Align with your organization's policy naming standards
4. **Multi-tenant**: Different tenants can use different prefixes

## Environment Configuration Examples

### Development
```bash
AUTHORIZATION_CONTEXT_PREFIX=dev
```

### Production
```bash
AUTHORIZATION_CONTEXT_PREFIX=prod
```

### Multi-tenant
```bash
AUTHORIZATION_CONTEXT_PREFIX=tenant1
```

This feature provides greater flexibility while maintaining the familiar AWS IAM policy structure and evaluation logic.