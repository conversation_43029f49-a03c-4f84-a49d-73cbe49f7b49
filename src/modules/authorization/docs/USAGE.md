# PBAC (Policy-Based Access Control) Module Usage Guide

## Table of Contents

1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [Core Concepts](#core-concepts)
4. [Role-Based Access Control](#role-based-access-control)
5. [Policy-Based Access Control](#policy-based-access-control)
6. [Guard Usage](#guard-usage)
7. [API Management](#api-management)
8. [Advanced Features](#advanced-features)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Overview

The PBAC module provides enterprise-grade authorization capabilities with both Role-Based Access Control (RBAC) and Policy-Based Access Control (PBAC) features. It integrates seamlessly with the existing JWT authentication system and offers AWS IAM-style policy documents for fine-grained access control.

### Key Features

- 🔐 **Hybrid Authorization**: Combines RBAC and PBAC for maximum flexibility
- 🎯 **AWS-Style Policies**: JSON policy documents with sophisticated condition evaluation
- 🛡️ **Route Protection**: Decorators for easy endpoint protection
- 📊 **Comprehensive APIs**: Full CRUD operations for roles and policies
- 🧪 **Production Ready**: Extensive test coverage and error handling
- ⚡ **Performance Optimized**: Efficient policy evaluation and caching

## Quick Start

### 1. Module Integration

The PBAC module is already integrated into the application. Ensure it's imported in your `app.module.ts`:

```typescript
import { PbacModule } from './modules/pbac/pbac.module';

@Module({
  imports: [
    // ... other modules
    PbacModule,
  ],
})
export class AppModule {}
```

### 2. Basic Route Protection

Protect your routes using decorators:

```typescript
import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { RequireRole, RequirePermission, RequirePolicy } from '../pbac/decorators';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PbacGuard } from '../pbac/guards/pbac.guard';

@Controller('users')
@UseGuards(JwtAuthGuard, PbacGuard)
export class UsersController {
  @Get()
  @RequirePermission('user:read')
  async getAllUsers() {
    return await this.userService.findAll();
  }

  @Post()
  @RequireRole('admin')
  async createUser(@Body() userData: CreateUserDto) {
    return await this.userService.create(userData);
  }

  @Get(':id')
  @RequirePolicy('user:read', 'arn:app:user:*:user/${params.id}', {
    selfAccess: true,
    adminOverride: true,
  })
  async getUserById(@Param('id') id: string) {
    return await this.userService.findById(id);
  }
}
```

### 3. Seed Data Usage

The module includes default roles and policies:

- **Roles**: `admin`, `user`, `moderator`, `guest`
- **Policies**: Basic CRUD policies for common operations

## Core Concepts

### Roles

Roles represent a collection of permissions that can be assigned to users:

```typescript
interface Role {
  id: string;
  name: string;
  description?: string;
  type: RoleType; // BUILT_IN | CUSTOM
  permissions: string[]; // e.g., ['user:read', 'user:write']
  isActive: boolean;
  priority: number;
  isBuiltIn: boolean;
}
```

### Policies

Policies are AWS IAM-style JSON documents that define fine-grained access rules:

```typescript
interface PolicyDocument {
  version: '2023-11-01';
  statement: PolicyStatement[];
}

interface PolicyStatement {
  effect: 'Allow' | 'Deny';
  action: string | string[];
  resource: string | string[];
  principal?: string | string[] | Record<string, string | string[]>;
  condition?: Record<string, Record<string, any>>;
}
```

### Resource ARNs

Resources are identified using Amazon Resource Name (ARN) format:

```
arn:app:service:region:account:resourceType/resourceId
```

Examples:
- `arn:app:user:*:*:user/123` - Specific user
- `arn:app:user:*:*:user/*` - All users
- `arn:app:order:*:*:order/${params.orderId}` - Dynamic resource ID

## Role-Based Access Control

### Creating Roles

```typescript
import { RoleService } from '../pbac/services/role.service';

@Injectable()
export class MyService {
  constructor(private roleService: RoleService) {}

  async createCustomRole() {
    const role = await this.roleService.createRole({
      name: 'customer-support',
      description: 'Customer support team role',
      type: RoleType.CUSTOM,
      permissions: [
        'user:read',
        'order:read',
        'ticket:read',
        'ticket:write',
        'ticket:update',
      ],
      isActive: true,
      priority: 10,
    });
    
    return role;
  }
}
```

### Assigning Roles

```typescript
// Assign role to user
await this.roleService.assignRoleToUser({
  userId: 'user-123',
  roleId: 'role-456',
  expiresAt: new Date('2024-12-31'), // Optional expiration
  reason: 'Promoted to customer support',
}, 'admin-user-id');

// Check if user has role
const hasRole = await this.roleService.userHasRole('user-123', 'customer-support');

// Get user's roles
const userRoles = await this.roleService.getUserRoles('user-123');
```

### Using Role Decorators

```typescript
@Controller('admin')
export class AdminController {
  // Single role requirement
  @Get('dashboard')
  @RequireRole('admin')
  async getDashboard() {
    return { message: 'Admin dashboard' };
  }

  // Multiple roles (user needs at least one)
  @Get('reports')
  @RequireRole(['admin', 'manager'])
  async getReports() {
    return { message: 'Reports data' };
  }

  // Permission-based access
  @Get('users')
  @RequirePermission('user:read')
  async getUsers() {
    return await this.userService.findAll();
  }

  // Multiple permissions (user needs at least one)
  @Post('users')
  @RequirePermission(['user:create', 'admin:full'])
  async createUser(@Body() userData: any) {
    return await this.userService.create(userData);
  }
}
```

## Policy-Based Access Control

### Creating Policies

```typescript
import { PolicyService } from '../pbac/services/policy.service';

@Injectable()
export class MyService {
  constructor(private policyService: PolicyService) {}

  async createSelfAccessPolicy() {
    const policyDocument: PolicyDocument = {
      version: '2023-11-01',
      statement: [
        {
          effect: PolicyEffect.ALLOW,
          action: ['user:read', 'user:update'],
          resource: ['arn:app:user:*:*:user/*'],
          condition: {
            StringEquals: {
              'user:id': '${request.params.id}',
            },
          },
        },
      ],
    };

    const policy = await this.policyService.createPolicy({
      name: 'SelfAccessPolicy',
      description: 'Allow users to access their own data',
      document: policyDocument,
      isActive: true,
      priority: 10,
      tags: {
        category: 'self-service',
        environment: 'production',
      },
    });

    return policy;
  }

  async createTimeRestrictedPolicy() {
    const policyDocument: PolicyDocument = {
      version: '2023-11-01',
      statement: [
        {
          effect: PolicyEffect.ALLOW,
          action: ['order:read', 'order:update'],
          resource: ['arn:app:order:*:*:order/*'],
          condition: {
            DateGreaterThan: {
              'myapp:CurrentTime': '2024-01-01T00:00:00Z',
            },
            DateLessThan: {
              'myapp:CurrentTime': '2024-12-31T23:59:59Z',
            },
            IpAddress: {
              'myapp:SourceIp': ['***********/24', '10.0.0.0/8'],
            },
          },
        },
      ],
    };

    return await this.policyService.createPolicy({
      name: 'TimeRestrictedOrderAccess',
      description: 'Time and IP restricted order access',
      document: policyDocument,
      isActive: true,
    });
  }
}
```

### Condition Operators

The policy engine supports various condition operators:

| Operator | Description | Example |
|----------|-------------|---------|
| `StringEquals` | Exact string match | `"user:department": "engineering"` |
| `StringLike` | Wildcard pattern match | `"user:email": "*@company.com"` |
| `NumericEquals` | Numeric equality | `"user:age": 25` |
| `NumericLessThan` | Numeric comparison | `"order:amount": 1000` |
| `NumericGreaterThan` | Numeric comparison | `"user:experience": 2` |
| `DateGreaterThan` | Date comparison | `"myapp:CurrentTime": "2024-01-01T00:00:00Z"` |
| `DateLessThan` | Date comparison | `"myapp:CurrentTime": "2024-12-31T23:59:59Z"` |
| `IpAddress` | IP/CIDR match | `"myapp:SourceIp": ["***********/24"]` |
| `Bool` | Boolean value | `"user:isActive": "true"` |
| `Null` | Null/undefined check | `"user:department": "false"` |
| `ForAllValues` | All values match | For array conditions |
| `ForAnyValue` | Any value matches | For array conditions |

### Using Policy Decorators

```typescript
@Controller('orders')
export class OrdersController {
  // Simple policy protection
  @Get()
  @RequirePolicy('OrderReadPolicy')
  async getAllOrders() {
    return await this.orderService.findAll();
  }

  // Resource-specific policy with dynamic resource ID
  @Get(':id')
  @RequirePolicy('order:read', 'arn:app:order:*:*:order/${params.id}')
  async getOrder(@Param('id') id: string) {
    return await this.orderService.findById(id);
  }

  // Policy with additional conditions
  @Put(':id')
  @RequirePolicy('order:update', 'arn:app:order:*:*:order/${params.id}', {
    selfAccess: true,      // Allow if user owns the resource
    adminOverride: true,   // Allow admin access regardless
    ownershipCheck: true,  // Check ownership via ownerField
    ownerField: 'userId',  // Field to check for ownership
  })
  async updateOrder(@Param('id') id: string, @Body() data: any) {
    return await this.orderService.update(id, data);
  }
}
```

### Attaching Policies

```typescript
// Attach policy to user
await this.policyService.attachPolicyToUser({
  userId: 'user-123',
  policyId: 'policy-456',
  expiresAt: new Date('2024-12-31'),
  priorityOverride: 20,
  reason: 'Special project access',
}, 'admin-user-id');

// Attach policy to role
await this.roleService.attachPolicyToRole(
  'role-789',
  'policy-456',
  'admin-user-id',
  15 // priority override
);

// Evaluate policy access
const result = await this.policyService.evaluateAccess({
  user: { id: 'user-123', email: '<EMAIL>' },
  action: 'order:read',
  resource: 'arn:app:order:*:*:order/123',
  request: {
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0...',
    timestamp: new Date(),
  },
  attributes: {
    department: 'engineering',
    clearanceLevel: 3,
  },
});

console.log(result.allowed); // true/false
console.log(result.reason);  // explanation
console.log(result.evaluatedPolicies); // policies that were checked
```

## Guard Usage

### Basic Guard Setup

```typescript
import { Controller, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PbacGuard } from '../pbac/guards/pbac.guard';

@Controller('api/v1/protected')
@UseGuards(JwtAuthGuard, PbacGuard) // Order matters: Auth first, then PBAC
export class ProtectedController {
  // Controller methods with authorization decorators
}
```

### Skipping PBAC

```typescript
@Controller('public')
export class PublicController {
  // Skip PBAC for specific endpoints
  @Get('health')
  @SkipPbac()
  async healthCheck() {
    return { status: 'ok' };
  }

  // Skip PBAC but still require authentication
  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @SkipPbac()
  async getProfile(@Req() req) {
    return req.user;
  }
}
```

### Global PBAC Configuration

Configure PBAC globally in your app:

```typescript
import { APP_GUARD } from '@nestjs/core';
import { PbacGuard } from './modules/pbac/guards/pbac.guard';

@Module({
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PbacGuard,
    },
  ],
})
export class AppModule {}
```

## API Management

### Role Management APIs

```bash
# Get all roles
GET /api/v1/pbac/roles?page=1&limit=10&search=admin&type=BUILT_IN

# Get specific role
GET /api/v1/pbac/roles/{roleId}

# Create new role
POST /api/v1/pbac/roles
{
  "name": "customer-support",
  "description": "Customer support team",
  "type": "CUSTOM",
  "permissions": ["user:read", "ticket:write"],
  "isActive": true,
  "priority": 10
}

# Update role
PUT /api/v1/pbac/roles/{roleId}
{
  "description": "Updated description",
  "permissions": ["user:read", "ticket:write", "ticket:update"]
}

# Delete role
DELETE /api/v1/pbac/roles/{roleId}

# Assign role to user
POST /api/v1/pbac/roles/{roleId}/users
{
  "userId": "user-123",
  "expiresAt": "2024-12-31T23:59:59Z",
  "reason": "Promotion"
}

# Revoke role from user
DELETE /api/v1/pbac/roles/{roleId}/users/{userId}

# Get role assignments
GET /api/v1/pbac/roles/{roleId}/users

# Bulk role operations
POST /api/v1/pbac/roles/bulk-operations
{
  "operation": "assign",
  "userIds": ["user-1", "user-2"],
  "roleIds": ["role-123"],
  "reason": "Bulk assignment"
}
```

### Policy Management APIs

```bash
# Get all policies
GET /api/v1/pbac/policies?page=1&limit=10&search=SelfAccess&action=user:read

# Get specific policy
GET /api/v1/pbac/policies/{policyId}

# Create new policy
POST /api/v1/pbac/policies
{
  "name": "SelfAccessPolicy",
  "description": "Allow self-access",
  "document": {
    "version": "2023-11-01",
    "statement": [
      {
        "effect": "Allow",
        "action": ["user:read"],
        "resource": ["arn:app:user:*:*:user/*"],
        "condition": {
          "StringEquals": {
            "user:id": "${request.params.id}"
          }
        }
      }
    ]
  },
  "isActive": true,
  "tags": {
    "category": "self-service"
  }
}

# Update policy
PUT /api/v1/pbac/policies/{policyId}

# Delete policy
DELETE /api/v1/pbac/policies/{policyId}

# Attach policy to user
POST /api/v1/pbac/policies/{policyId}/users
{
  "userId": "user-123",
  "expiresAt": "2024-12-31T23:59:59Z",
  "reason": "Special access"
}

# Attach policy to role
POST /api/v1/pbac/policies/{policyId}/roles
{
  "roleId": "role-456",
  "priorityOverride": 15
}

# Evaluate policy
POST /api/v1/pbac/policies/evaluate
{
  "userId": "user-123",
  "action": "user:read",
  "resource": "arn:app:user:*:*:user/123",
  "context": {
    "ipAddress": "*************",
    "department": "engineering"
  }
}
```

## Advanced Features

### Custom Resource Handlers

Create custom resource handlers for domain-specific resources:

```typescript
import { Injectable } from '@nestjs/common';
import { ResourceHandler } from '../interfaces/resource-handler.interface';

@Injectable()
export class OrderResourceHandler implements ResourceHandler {
  canHandle(resource: string): boolean {
    return resource.startsWith('arn:app:order:');
  }

  async resolveResource(
    resource: string,
    context: any,
    request: any,
  ): Promise<string> {
    // Custom logic to resolve dynamic resource ARNs
    const orderId = request.params?.orderId || request.body?.orderId;
    return resource.replace('${params.orderId}', orderId);
  }

  async checkOwnership(
    resource: string,
    user: any,
    request: any,
  ): Promise<boolean> {
    // Custom ownership logic for orders
    const orderId = this.extractOrderId(resource);
    const order = await this.orderService.findById(orderId);
    return order && order.userId === user.id;
  }

  private extractOrderId(resource: string): string {
    const match = resource.match(/arn:app:order:.*:order\/(.+)$/);
    return match ? match[1] : null;
  }
}
```

Register the custom handler:

```typescript
@Module({
  providers: [
    OrderResourceHandler,
    {
      provide: 'RESOURCE_HANDLERS',
      useFactory: (orderHandler: OrderResourceHandler) => [orderHandler],
      inject: [OrderResourceHandler],
    },
  ],
})
export class OrderModule {}
```

### Custom Condition Evaluators

Extend the condition evaluation system:

```typescript
import { Injectable } from '@nestjs/common';
import { ConditionEvaluatorService } from '../services/condition-evaluator.service';

@Injectable()
export class CustomConditionEvaluator extends ConditionEvaluatorService {
  protected evaluateCustomCondition(
    operator: string,
    key: string,
    expectedValue: any,
    actualValue: any,
  ): { satisfied: boolean; reason?: string } {
    switch (operator) {
      case 'CustomEquals':
        return {
          satisfied: this.customEquals(actualValue, expectedValue),
          reason: `Custom condition ${key} evaluation`,
        };
      
      case 'InternalOnly':
        return {
          satisfied: this.isInternalUser(actualValue),
          reason: 'Internal user check',
        };
      
      default:
        return super.evaluateCondition(operator, key, expectedValue, actualValue);
    }
  }

  private customEquals(actual: any, expected: any): boolean {
    // Custom equality logic
    return String(actual).toLowerCase() === String(expected).toLowerCase();
  }

  private isInternalUser(user: any): boolean {
    // Check if user is internal based on email domain
    return user.email && user.email.endsWith('@company.com');
  }
}
```

### Caching and Performance

Implement caching for policy evaluation:

```typescript
import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CachedPolicyService extends PolicyService {
  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    // ... other dependencies
  ) {
    super(/* dependencies */);
  }

  async evaluateAccess(context: PolicyEvaluationContext): Promise<PolicyEvaluationResult> {
    const cacheKey = this.generateCacheKey(context);
    
    // Check cache first
    const cached = await this.cacheManager.get<PolicyEvaluationResult>(cacheKey);
    if (cached) {
      return cached;
    }

    // Evaluate and cache result
    const result = await super.evaluateAccess(context);
    await this.cacheManager.set(cacheKey, result, 300); // 5 minutes TTL
    
    return result;
  }

  private generateCacheKey(context: PolicyEvaluationContext): string {
    return `policy:${context.user.id}:${context.action}:${context.resource}`;
  }
}
```

## Best Practices

### 1. Principle of Least Privilege

Start with minimal permissions and add more as needed:

```typescript
// Good: Specific permissions
const customerSupportRole = {
  name: 'customer-support',
  permissions: [
    'user:read',
    'ticket:read',
    'ticket:write',
    'order:read',
  ],
};

// Avoid: Overly broad permissions
const badRole = {
  name: 'support',
  permissions: ['*:*'], // Too broad!
};
```

### 2. Use Descriptive Names

Make roles and policies self-documenting:

```typescript
// Good
const role = {
  name: 'financial-analyst-readonly',
  description: 'Read-only access to financial reports and analytics',
};

const policy = {
  name: 'QuarterlyReportAccess',
  description: 'Access to quarterly financial reports during business hours',
};

// Avoid
const badRole = {
  name: 'role1', // Not descriptive
};
```

### 3. Implement Time-Based Access

Use expiration dates for temporary access:

```typescript
await this.roleService.assignRoleToUser({
  userId: 'contractor-123',
  roleId: 'temporary-admin',
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
  reason: 'Temporary admin access for project',
}, 'manager-456');
```

### 4. Use Policy Conditions Effectively

Implement context-aware access control:

```typescript
const businessHoursPolicy = {
  version: '2023-11-01',
  statement: [
    {
      effect: 'Allow',
      action: ['financial:read'],
      resource: ['arn:app:finance:*:*:report/*'],
      condition: {
        DateGreaterThan: {
          'myapp:RequestTime': '08:00:00Z',
        },
        DateLessThan: {
          'myapp:RequestTime': '18:00:00Z',
        },
        StringEquals: {
          'user:department': 'finance',
        },
      },
    },
  ],
};
```

### 5. Monitor and Audit

Implement comprehensive logging:

```typescript
@Injectable()
export class AuditService {
  async logAccessAttempt(
    user: any,
    action: string,
    resource: string,
    allowed: boolean,
    reason: string,
  ) {
    await this.auditRepository.save({
      userId: user.id,
      action,
      resource,
      allowed,
      reason,
      timestamp: new Date(),
      ipAddress: user.ipAddress,
      userAgent: user.userAgent,
    });
  }
}
```

### 6. Test Authorization Logic

Create comprehensive tests for your authorization rules:

```typescript
describe('Authorization Tests', () => {
  it('should allow manager to access team reports', async () => {
    const result = await policyService.evaluateAccess({
      user: { id: 'manager-1', department: 'sales' },
      action: 'report:read',
      resource: 'arn:app:report:*:*:team-report/sales-team',
    });
    
    expect(result.allowed).toBe(true);
  });

  it('should deny access outside business hours', async () => {
    // Mock current time to be outside business hours
    jest.spyOn(Date, 'now').mockReturnValue(
      new Date('2024-01-01T22:00:00Z').getTime()
    );

    const result = await policyService.evaluateAccess({
      user: { id: 'user-1' },
      action: 'financial:read',
      resource: 'arn:app:finance:*:*:report/quarterly',
    });
    
    expect(result.allowed).toBe(false);
  });
});
```

## Troubleshooting

### Common Issues

#### 1. "Access denied - insufficient authorization metadata"

This error occurs when no PBAC decorators are found on a protected endpoint.

**Solution**: Add appropriate decorators:

```typescript
@Get('users')
@RequirePermission('user:read') // Add this
async getUsers() {
  return await this.userService.findAll();
}
```

#### 2. "Policy evaluation failed"

This indicates an error in policy evaluation logic.

**Debug steps**:
1. Check policy document syntax
2. Verify condition operators are supported
3. Check resource ARN format
4. Review evaluation logs

```typescript
// Enable debug logging
const result = await policyService.evaluateAccess(context);
console.log('Evaluation result:', result);
console.log('Evaluated policies:', result.evaluatedPolicies);
```

#### 3. "Role not found" or "Policy not found"

Ensure required roles/policies exist and are active.

**Solution**: Check seed data or create missing resources:

```typescript
// Check if role exists
const role = await roleService.findRoleByName('required-role');
if (!role) {
  // Create the role or fix the reference
}
```

#### 4. Permission denied for admin users

Admin users might not have the required roles assigned.

**Solution**: Verify admin role assignment:

```typescript
const hasAdminRole = await roleService.userHasRole(userId, 'admin');
if (!hasAdminRole) {
  await roleService.assignRoleToUser({
    userId,
    roleId: adminRole.id,
  }, 'system');
}
```

#### 5. Policies not evaluating correctly

Check policy conditions and context values.

**Debug example**:

```typescript
const context = {
  user: { id: 'user-123', department: 'engineering' },
  action: 'user:read',
  resource: 'arn:app:user:*:*:user/123',
  attributes: {
    department: 'engineering', // Make sure this matches policy conditions
  },
};

const result = await policyService.evaluateAccess(context);
console.log('Decision:', result.decision);
console.log('Reason:', result.reason);
console.log('Metadata:', result.metadata);
```

### Performance Issues

#### 1. Slow policy evaluation

- **Check**: Number of policies attached to user/roles
- **Solution**: Optimize policy structure, use caching
- **Monitor**: Policy evaluation times in logs

#### 2. Database performance

- **Check**: Database indexes on foreign keys
- **Solution**: Add indexes for user_roles, role_policies, user_policies tables
- **Monitor**: Query execution times

### Configuration Issues

#### 1. PBAC not working

Verify configuration in your environment:

```typescript
// Check PBAC configuration
const pbacConfig = configService.get('pbac');
console.log('PBAC enabled:', pbacConfig?.enabled);
```

#### 2. Guards not applied

Ensure guards are properly registered:

```typescript
// Global guard registration
@Module({
  providers: [
    {
      provide: APP_GUARD,
      useClass: PbacGuard,
    },
  ],
})
export class AppModule {}
```

For additional support or to report issues, please refer to the project documentation or create an issue in the repository.