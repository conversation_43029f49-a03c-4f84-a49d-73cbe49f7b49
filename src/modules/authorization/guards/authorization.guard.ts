import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import {
  AUTHORIZATION_ACTION_KEY,
  AUTHORIZATION_CONDITIONS_KEY,
  AUTHORIZATION_PERMISSION_KEY,
  AUTHORIZATION_POLICY_KEY,
  AUTHORIZATION_RESOURCE_KEY,
  AUTHORIZATION_ROLE_KEY,
  AUTHORIZATION_SKIP_KEY,
} from '../decorators/authorization.decorators';
import { PolicyEvaluationContext } from '../interfaces/policy.interface';
import { AuthorizationConfigType } from '../authorization.config';
import { PolicyEngineService } from '../services/policy-engine.service';
import { PolicyService } from '../services/policy.service';
import { RoleService } from '../services/role.service';

/**
 * Guard that implements Policy-Based Access Control (PBAC)
 * Integrates with existing JWT authentication and adds fine-grained authorization
 */
@Injectable()
export class AuthorizationGuard implements CanActivate {
  private readonly logger = new Logger(AuthorizationGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
    private readonly policyService: PolicyService,
    private readonly roleService: RoleService,
    private readonly policyEngine: PolicyEngineService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if Authorization is enabled
    const authorizationConfig =
      this.configService.get<AuthorizationConfigType>('authorization');
    if (!authorizationConfig?.enabled) {
      this.logger.debug('Authorization is disabled, allowing access');
      return true;
    }

    // Check if Authorization should be skipped for this route
    const skipAuthorization = this.reflector.getAllAndOverride<boolean>(
      AUTHORIZATION_SKIP_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (skipAuthorization) {
      this.logger.debug('Authorization evaluation skipped for this route');
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Ensure user is authenticated (should be handled by JwtAuthGuard first)
    if (!user) {
      this.logger.warn('Authorization guard called without authenticated user');
      throw new UnauthorizedException('User must be authenticated');
    }

    // Check if user account is active
    if (user.isActive === false) {
      this.logger.warn(`Access denied for inactive user: ${user.id}`);
      throw new ForbiddenException('User account is inactive');
    }

    try {
      // Get metadata from decorators
      const requiredAction = this.reflector.getAllAndOverride<string>(
        AUTHORIZATION_ACTION_KEY,
        [context.getHandler(), context.getClass()],
      );

      const resourcePattern = this.reflector.getAllAndOverride<string>(
        AUTHORIZATION_RESOURCE_KEY,
        [context.getHandler(), context.getClass()],
      );

      const requiredRole = this.reflector.getAllAndOverride<string | string[]>(
        AUTHORIZATION_ROLE_KEY,
        [context.getHandler(), context.getClass()],
      );

      const requiredPermission = this.reflector.getAllAndOverride<
        string | string[]
      >(AUTHORIZATION_PERMISSION_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      const requiredPolicy = this.reflector.getAllAndOverride<string>(
        AUTHORIZATION_POLICY_KEY,
        [context.getHandler(), context.getClass()],
      );

      const additionalConditions = this.reflector.getAllAndOverride<
        Record<string, any>
      >(AUTHORIZATION_CONDITIONS_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      // Policy-based evaluation (highest priority)
      if (requiredAction && resourcePattern) {
        return await this.evaluatePolicy(
          request,
          user,
          requiredAction,
          resourcePattern,
          additionalConditions,
        );
      }

      // Role-based evaluation
      if (requiredRole) {
        return await this.evaluateRole(user, requiredRole);
      }

      // Permission-based evaluation
      if (requiredPermission) {
        return await this.evaluatePermission(user, requiredPermission);
      }

      // Specific policy evaluation
      if (requiredPolicy) {
        return await this.evaluateSpecificPolicy(
          request,
          user,
          requiredPolicy,
          additionalConditions,
        );
      }

      // If no Authorization metadata is found, check if we should allow or deny by default
      // This follows the principle of least privilege - deny by default
      this.logger.debug(
        'No Authorization metadata found, applying default access policy',
      );

      // Allow access for admin users when no specific restrictions are defined
      const isAdmin = await this.roleService.userHasRole(user.id, 'admin');
      if (isAdmin) {
        this.logger.debug(`Admin user ${user.id} granted default access`);
        return true;
      }

      // For non-admin users, deny access when no specific authorization is defined
      this.logger.warn(
        `Access denied for user ${user.id} - no authorization metadata found`,
      );
      throw new ForbiddenException(
        'Access denied - insufficient authorization metadata',
      );
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      this.logger.error(
        `Authorization evaluation error for user ${user.id}: ${error.message}`,
        error.stack,
      );
      throw new ForbiddenException('Authorization evaluation failed');
    }
  }

  /**
   * Evaluate access using policy-based approach
   */
  private async evaluatePolicy(
    request: any,
    user: any,
    action: string,
    resourcePattern: string,
    additionalConditions?: Record<string, any>,
  ): Promise<boolean> {
    this.logger.debug(
      `Evaluating policy access for user ${user.id}, action: ${action}, resource: ${resourcePattern}`,
    );

    // Build evaluation context
    const context: PolicyEvaluationContext =
      this.policyEngine.buildEvaluationContext(
        user,
        action,
        resourcePattern,
        request,
      );

    // Add additional conditions if specified
    if (additionalConditions) {
      context.attributes = { ...context.attributes, ...additionalConditions };
    }

    // Handle special conditions
    if (additionalConditions?.selfAccess) {
      const isSelfAccess = this.checkSelfAccess(
        user,
        context.resource,
        request,
      );
      if (isSelfAccess) {
        this.logger.debug(`Self-access granted for user ${user.id}`);
        return true;
      }
    }

    if (additionalConditions?.adminOverride) {
      const isAdmin = await this.roleService.userHasRole(user.id, 'admin');
      if (isAdmin) {
        this.logger.debug(`Admin override granted for user ${user.id}`);
        return true;
      }
    }

    if (additionalConditions?.ownershipCheck) {
      const isOwner = this.checkResourceOwnership(
        user,
        request,
        additionalConditions.ownerField,
      );
      if (isOwner) {
        this.logger.debug(`Resource ownership granted for user ${user.id}`);
        return true;
      }
    }

    // Evaluate policies
    const result = await this.policyService.evaluateAccess(context);

    if (!result.allowed) {
      this.logger.warn(
        `Policy evaluation denied access for user ${user.id}: ${result.reason}`,
      );
      throw new ForbiddenException(
        result.reason || 'Access denied by policy evaluation',
      );
    }

    this.logger.debug(`Policy evaluation granted access for user ${user.id}`);
    return true;
  }

  /**
   * Evaluate access using role-based approach
   */
  private async evaluateRole(
    user: any,
    requiredRole: string | string[],
  ): Promise<boolean> {
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

    this.logger.debug(
      `Evaluating role access for user ${user.id}, required roles: ${roles.join(', ')}`,
    );

    for (const role of roles) {
      const hasRole = await this.roleService.userHasRole(user.id, role);
      if (hasRole) {
        this.logger.debug(
          `Role access granted for user ${user.id} with role: ${role}`,
        );
        return true;
      }
    }

    this.logger.warn(
      `Role access denied for user ${user.id} - missing required roles: ${roles.join(', ')}`,
    );
    throw new ForbiddenException(
      `Access denied - requires one of the following roles: ${roles.join(', ')}`,
    );
  }

  /**
   * Evaluate access using permission-based approach
   */
  private async evaluatePermission(
    user: any,
    requiredPermission: string | string[],
  ): Promise<boolean> {
    const permissions = Array.isArray(requiredPermission)
      ? requiredPermission
      : [requiredPermission];

    this.logger.debug(
      `Evaluating permission access for user ${user.id}, required permissions: ${permissions.join(', ')}`,
    );

    for (const permission of permissions) {
      const hasPermission = await this.roleService.userHasPermission(
        user.id,
        permission,
      );
      if (hasPermission) {
        this.logger.debug(
          `Permission access granted for user ${user.id} with permission: ${permission}`,
        );
        return true;
      }
    }

    this.logger.warn(
      `Permission access denied for user ${user.id} - missing required permissions: ${permissions.join(', ')}`,
    );
    throw new ForbiddenException(
      `Access denied - requires one of the following permissions: ${permissions.join(', ')}`,
    );
  }

  /**
   * Evaluate access for a specific policy
   */
  private async evaluateSpecificPolicy(
    request: any,
    user: any,
    policyName: string,
    additionalConditions?: Record<string, any>,
  ): Promise<boolean> {
    this.logger.debug(
      `Evaluating specific policy ${policyName} for user ${user.id}`,
    );

    const policy = await this.policyService.findPolicyByName(policyName);
    if (!policy || !policy.isActive) {
      this.logger.warn(`Policy ${policyName} not found or inactive`);
      throw new ForbiddenException(
        `Required policy ${policyName} is not available`,
      );
    }

    // For specific policy evaluation, we need to extract action and resource from the policy
    // This is a simplified approach - in practice, you might want more sophisticated logic
    const firstStatement = policy.document.statement[0];
    if (!firstStatement) {
      throw new ForbiddenException(`Policy ${policyName} has no statements`);
    }

    const action = Array.isArray(firstStatement.action)
      ? firstStatement.action[0]
      : firstStatement.action;
    const resource = Array.isArray(firstStatement.resource)
      ? firstStatement.resource[0]
      : firstStatement.resource;

    return this.evaluatePolicy(
      request,
      user,
      action,
      resource,
      additionalConditions,
    );
  }

  /**
   * Check if the user is accessing their own resource
   */
  private checkSelfAccess(user: any, resource: string, request: any): boolean {
    // Extract user ID from resource ARN or path
    const userIdFromResource = this.extractUserIdFromResource(
      resource,
      request,
    );
    return userIdFromResource === user.id;
  }

  /**
   * Check if the user owns the resource
   */
  private checkResourceOwnership(
    user: any,
    request: any,
    ownerField = 'ownerId',
  ): boolean {
    // Check if the resource's owner field matches the user ID
    const ownerId =
      request.params?.[ownerField] ||
      request.body?.[ownerField] ||
      request.query?.[ownerField];
    return ownerId === user.id;
  }

  /**
   * Extract user ID from resource ARN or path
   */
  private extractUserIdFromResource(
    resource: string,
    request: any,
  ): string | null {
    // Handle ARN format: arn:app:user:*:user/123
    const arnMatch = resource.match(/arn:app:user:\*:user\/(.+)$/);
    if (arnMatch) {
      const resourceId = arnMatch[1];
      // If it's a wildcard or template, try to get from request params
      if (resourceId === '*' || resourceId.includes('${')) {
        return request.params?.id || request.params?.userId;
      }
      return resourceId;
    }

    // Handle path parameters
    if (request.params?.id) {
      return request.params.id;
    }

    if (request.params?.userId) {
      return request.params.userId;
    }

    return null;
  }
}
