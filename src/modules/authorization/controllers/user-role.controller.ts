import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import {
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';

import {
  RequirePolicyAccess,
  RequireSelfOrAdmin,
} from '../decorators/authorization.decorators';
import { AuthorizationGuard } from '../guards/authorization.guard';
import {
  UserRolesResponseDto,
  UserPermissionsResponseDto,
  UserPoliciesResponseDto,
  UserAccessSummaryResponseDto,
} from '../dto/user-role.dto';
import { UserRoleService } from '../services/user-role.service';

@ApiTags('User Role')
@ApiBearerAuth()
@Controller({
  path: 'user-roles',
  version: '1',
})
@UseGuards(JwtAuthGuard, AuthorizationGuard)
export class UserRoleController {
  constructor(private readonly userRoleService: UserRoleService) {}

  @ApiOperation({
    summary: 'Get user roles',
    description:
      'Retrieve all roles assigned to a specific user. Users can view their own roles, admins can view any user.',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'User roles retrieved successfully',
    type: UserRolesResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  @ApiForbiddenResponse({
    description: 'Can only access own roles or requires admin role',
  })
  @RequireSelfOrAdmin()
  @Get(':id/roles')
  async getUserRoles(
    @Param('id', ParseUUIDPipe) userId: string,
  ): Promise<UserRolesResponseDto> {
    return await this.userRoleService.getUserRoles(userId);
  }

  @ApiOperation({
    summary: 'Get user effective permissions',
    description:
      'Retrieve all effective permissions for a user from roles and direct policy assignments.',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'User permissions retrieved successfully',
    type: UserPermissionsResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  @ApiForbiddenResponse({
    description: 'Can only access own permissions or requires admin role',
  })
  @RequireSelfOrAdmin()
  @Get(':id/permissions')
  async getUserPermissions(
    @Param('id', ParseUUIDPipe) userId: string,
  ): Promise<UserPermissionsResponseDto> {
    return await this.userRoleService.getUserPermissions(userId);
  }

  @ApiOperation({
    summary: 'Get user policies',
    description:
      'Retrieve all policies affecting a user (direct attachments and role-based policies).',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'User policies retrieved successfully',
    type: UserPoliciesResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  @ApiForbiddenResponse({
    description: 'Can only access own policies or requires admin role',
  })
  @RequireSelfOrAdmin()
  @Get(':id/policies')
  async getUserPolicies(
    @Param('id', ParseUUIDPipe) userId: string,
  ): Promise<UserPoliciesResponseDto> {
    return await this.userRoleService.getUserPolicies(userId);
  }

  @ApiOperation({
    summary: 'Get user access summary',
    description:
      'Get a comprehensive summary of user access including roles, policies, and permissions.',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'User access summary retrieved successfully',
    type: UserAccessSummaryResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  @ApiForbiddenResponse({
    description: 'Can only access own access summary or requires admin role',
  })
  @RequireSelfOrAdmin()
  @Get(':id/access-summary')
  async getUserAccessSummary(
    @Param('id', ParseUUIDPipe) userId: string,
  ): Promise<UserAccessSummaryResponseDto> {
    return await this.userRoleService.getUserAccessSummary(userId);
  }

  @ApiOperation({
    summary: 'Get current user access information',
    description: 'Get access information for the currently authenticated user.',
  })
  @ApiOkResponse({
    description: 'Current user access information retrieved successfully',
    type: UserAccessSummaryResponseDto,
  })
  @RequirePolicyAccess('user:read-own', 'arn:app:user:*:user/${user.id}')
  @Get('me/access')
  async getCurrentUserAccess(
    @Request() req: any,
  ): Promise<UserAccessSummaryResponseDto> {
    return await this.userRoleService.getUserAccessSummary(req.user.id);
  }
}
