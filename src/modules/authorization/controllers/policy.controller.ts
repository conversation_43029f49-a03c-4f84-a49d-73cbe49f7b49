import { CountDto } from '@app/common/dto/count.dto';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import {
  RequireAdmin,
  RequirePolicyAccess,
} from '../decorators/authorization.decorators';
import {
  AttachPolicyDto,
  CreatePolicyInput,
  EvaluatePolicyDto,
  ListPoliciesDto,
  PaginatedPoliciesResponseDto,
  PolicyResponseDto,
  UpdatePolicyDto,
} from '../dto/policy.dto';
import { PolicyEntity } from '../entities/policy.entity';
import { AuthorizationGuard } from '../guards/authorization.guard';
import { PolicyService } from '../services/policy.service';
import { User } from '@app/modules/auth/decorators/user.decorator';
import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';

@ApiTags('Policy Management')
@Controller('api/v1/policies')
@UseGuards(JwtAuthGuard, AuthorizationGuard)
@ApiBearerAuth()
export class PolicyController {
  constructor(private readonly policyService: PolicyService) {}

  @Post()
  @RequireAdmin()
  @ApiOperation({
    summary: 'Create a new policy',
    description:
      'Creates a new AWS-style policy document. Only administrators can create policies.',
  })
  @ApiCreatedResponse({
    description: 'Policy created successfully',
    type: PolicyResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid policy document or policy name already exists',
  })
  @ApiForbiddenResponse({
    description: 'Admin role required',
  })
  async createPolicy(
    @Body() input: CreatePolicyInput,
    @User() user: JwtPayloadType,
  ): Promise<PolicyResponseDto> {
    const policy = await this.policyService.createPolicy(input, user.sub);

    return this.mapPolicyToResponseDto(policy);
  }

  @Get()
  @RequirePolicyAccess('policy:list', 'arn:app:iam:*:policy/*')
  @ApiOperation({
    summary: 'List policies',
    description:
      'Retrieve a paginated list of policies with optional filtering.',
  })
  @ApiOkResponse({
    description: 'Policies retrieved successfully',
    type: PaginatedPoliciesResponseDto,
  })
  async listPolicies(
    @Query() query: ListPoliciesDto,
  ): Promise<PaginatedPoliciesResponseDto> {
    const result = await this.policyService.listPolicies({
      isActive: query.isActive,
      search: query.search,
      action: query.action,
      resource: query.resource,
      page: query.page,
      limit: query.limit,
      sortBy: query.sortBy,
      sortOrder: query.sortOrder,
    });

    return {
      data: result.data.map((policy) => this.mapPolicyToResponseDto(policy)),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  @Get('count')
  @RequirePolicyAccess('policy:list', 'arn:app:iam:*:policy/*')
  @ApiOperation({
    summary: 'Get policies count',
    description: 'Get the total number of policies with optional filtering.',
  })
  @ApiOkResponse({
    description: 'Policies count retrieved successfully',
    type: CountDto,
  })
  async getPoliciesCount(@Query() query: ListPoliciesDto): Promise<CountDto> {
    const result = await this.policyService.listPolicies({
      isActive: query.isActive,
      search: query.search,
      action: query.action,
      resource: query.resource,
      page: 1,
      limit: 1,
    });

    return { count: result.total };
  }

  @Get(':id')
  @RequirePolicyAccess('policy:read', 'arn:app:iam:*:policy/${params.id}')
  @ApiOperation({
    summary: 'Get policy by ID',
    description:
      'Retrieve detailed information about a specific policy including its document.',
  })
  @ApiParam({
    name: 'id',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Policy retrieved successfully',
    type: PolicyResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Policy not found',
  })
  async getPolicyById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PolicyResponseDto> {
    const policy = await this.policyService.findPolicyById(id);
    return this.mapPolicyToResponseDto(policy);
  }

  @Put(':id')
  @RequirePolicyAccess('policy:update', 'arn:app:iam:*:policy/${params.id}')
  @ApiOperation({
    summary: 'Update policy',
    description:
      'Update an existing policy. Built-in system policies cannot be modified.',
  })
  @ApiParam({
    name: 'id',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Policy updated successfully',
    type: PolicyResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid policy document or cannot modify built-in policy',
  })
  @ApiNotFoundResponse({
    description: 'Policy not found',
  })
  async updatePolicy(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePolicyDto: UpdatePolicyDto,
    @Request() req: any,
  ): Promise<PolicyResponseDto> {
    const policy = await this.policyService.updatePolicy(
      id,
      {
        name: updatePolicyDto.name,
        description: updatePolicyDto.description,
        document: updatePolicyDto.document,
        isActive: updatePolicyDto.isActive,
        priority: updatePolicyDto.priority,
        tags: updatePolicyDto.tags,
      },
      req.user.id,
    );

    return this.mapPolicyToResponseDto(policy);
  }

  @Delete(':id')
  @RequirePolicyAccess('policy:delete', 'arn:app:iam:*:policy/${params.id}')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete policy',
    description:
      'Delete a policy. Built-in system policies and policies with active attachments cannot be deleted.',
  })
  @ApiParam({
    name: 'id',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Policy deleted successfully',
  })
  @ApiBadRequestResponse({
    description:
      'Cannot delete built-in policy or policy with active attachments',
  })
  @ApiNotFoundResponse({
    description: 'Policy not found',
  })
  async deletePolicy(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.policyService.deletePolicy(id, req.user.id);
  }

  @Post('evaluate')
  @RequirePolicyAccess('policy:evaluate', 'arn:app:iam:*:policy/*')
  @ApiOperation({
    summary: 'Evaluate policy access',
    description:
      'Evaluate whether the current user has access to perform a specific action on a resource.',
  })
  @ApiOkResponse({
    description: 'Policy evaluation completed',
    schema: {
      type: 'object',
      properties: {
        decision: {
          type: 'string',
          enum: ['ALLOW', 'DENY', 'NOT_APPLICABLE', 'INDETERMINATE'],
        },
        allowed: { type: 'boolean' },
        reason: { type: 'string' },
        evaluatedPolicies: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              policyId: { type: 'string' },
              policyName: { type: 'string' },
              matched: { type: 'boolean' },
              effect: { type: 'string' },
              reason: { type: 'string' },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            evaluationTime: { type: 'number' },
            policiesConsidered: { type: 'number' },
            statementsEvaluated: { type: 'number' },
          },
        },
      },
    },
  })
  async evaluatePolicy(
    @Body() evaluateDto: EvaluatePolicyDto,
    @Request() req: any,
  ) {
    const context = {
      user: {
        id: req.user.id,
        email: req.user.email,
        roles: [], // Will be populated by the policy service
      },
      action: evaluateDto.action,
      resource: evaluateDto.resource,
      request: {
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        timestamp: new Date(),
        method: req.method,
        path: req.path,
        headers: req.headers,
      },
      attributes: evaluateDto.context || {},
    };

    return await this.policyService.evaluateAccess(context);
  }

  @Post('simulate')
  @RequireAdmin()
  @ApiOperation({
    summary: 'Simulate policy evaluation',
    description: 'Simulate policy evaluation for testing purposes. Admin only.',
  })
  @ApiOkResponse({
    description: 'Policy simulation completed',
  })
  async simulatePolicy(
    @Body()
    body: {
      userId: string;
      action: string;
      resource: string;
      context?: Record<string, any>;
    },
    @Request() req: any,
  ) {
    const context = {
      user: {
        id: body.userId,
        email: '', // Will be populated by the policy service
        roles: [],
      },
      action: body.action,
      resource: body.resource,
      request: {
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        timestamp: new Date(),
        method: 'SIMULATE',
        path: '/simulate',
        headers: req.headers,
      },
      attributes: body.context || {},
    };

    return await this.policyService.evaluateAccess(context);
  }

  @Post(':id/attach')
  @RequirePolicyAccess('policy:attach', 'arn:app:iam:*:policy/${params.id}')
  @ApiOperation({
    summary: 'Attach policy to user',
    description:
      'Directly attach a policy to a user bypassing role-based assignment.',
  })
  @ApiParam({
    name: 'id',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiCreatedResponse({
    description: 'Policy attached to user successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input or policy attachment already exists',
  })
  @ApiNotFoundResponse({
    description: 'Policy not found',
  })
  async attachPolicyToUser(
    @Param('id', ParseUUIDPipe) policyId: string,
    @Body() attachDto: AttachPolicyDto,
    @Request() req: any,
  ): Promise<{ message: string; attachmentId: string }> {
    const attachment = await this.policyService.attachPolicyToUser(
      {
        userId: attachDto.userId,
        policyId,
        expiresAt: attachDto.expiresAt
          ? new Date(attachDto.expiresAt)
          : undefined,
        priorityOverride: attachDto.priorityOverride,
        reason: attachDto.reason,
      },
      req.user.id,
    );

    return {
      message: 'Policy attached to user successfully',
      attachmentId: attachment.id,
    };
  }

  @Delete(':id/detach/:userId')
  @RequirePolicyAccess('policy:detach', 'arn:app:iam:*:policy/${params.id}')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Detach policy from user',
    description: 'Remove a direct policy attachment from a user.',
  })
  @ApiParam({
    name: 'id',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Policy detached from user successfully',
  })
  @ApiNotFoundResponse({
    description: 'Policy attachment not found',
  })
  async detachPolicyFromUser(
    @Param('id', ParseUUIDPipe) policyId: string,
    @Param('userId', ParseUUIDPipe) userId: string,
  ): Promise<void> {
    await this.policyService.detachPolicyFromUser(userId, policyId);
  }

  @Get(':id/validate')
  @RequirePolicyAccess('policy:validate', 'arn:app:iam:*:policy/${params.id}')
  @ApiOperation({
    summary: 'Validate policy document',
    description:
      'Validate a policy document structure and syntax without saving it.',
  })
  @ApiParam({
    name: 'id',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Policy validation completed',
    schema: {
      type: 'object',
      properties: {
        valid: { type: 'boolean' },
        errors: {
          type: 'array',
          items: { type: 'string' },
        },
        warnings: {
          type: 'array',
          items: { type: 'string' },
        },
        analysis: {
          type: 'object',
          properties: {
            statementCount: { type: 'number' },
            actionCount: { type: 'number' },
            resourceCount: { type: 'number' },
            hasConditions: { type: 'boolean' },
            effects: {
              type: 'array',
              items: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async validatePolicy(@Param('id', ParseUUIDPipe) id: string) {
    const policy = await this.policyService.findPolicyById(id);

    // Basic validation analysis
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Validate document structure
      const doc = policy.document;

      if (!doc.statement || doc.statement.length === 0) {
        errors.push('Policy must contain at least one statement');
      }

      if (doc.statement.length > 20) {
        warnings.push(
          'Policy contains more than 20 statements, consider splitting into multiple policies',
        );
      }

      // Analyze statements
      const effects = new Set<string>();
      let hasConditions = false;

      for (const statement of doc.statement) {
        effects.add(statement.effect);
        if (statement.condition) {
          hasConditions = true;
        }

        // Check for overly broad permissions
        const actions = Array.isArray(statement.action)
          ? statement.action
          : [statement.action];
        const resources = Array.isArray(statement.resource)
          ? statement.resource
          : [statement.resource];

        if (actions.includes('*') && resources.includes('*')) {
          warnings.push(
            'Statement grants all actions on all resources - consider more specific permissions',
          );
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        analysis: {
          statementCount: doc.statement.length,
          actionCount: policy.actions.length,
          resourceCount: policy.resources.length,
          hasConditions,
          effects: Array.from(effects),
        },
      };
    } catch (error) {
      errors.push(`Policy validation error: ${error.message}`);

      return {
        valid: false,
        errors,
        warnings,
        analysis: {
          statementCount: 0,
          actionCount: 0,
          resourceCount: 0,
          hasConditions: false,
          effects: [],
        },
      };
    }
  }

  /**
   * Map PolicyEntity to PolicyResponseDto
   */
  private mapPolicyToResponseDto(policy: PolicyEntity): PolicyResponseDto {
    return {
      id: policy.id,
      name: policy.name,
      description: policy.description,
      version: policy.version,
      document: policy.document,
      isActive: policy.isActive,
      isBuiltIn: policy.isBuiltIn,
      priority: policy.priority,
      actions: policy.actions,
      resources: policy.resources,
      tags: policy.tags,
      createdAt: policy.createdAt,
      updatedAt: policy.updatedAt,
    };
  }
}
