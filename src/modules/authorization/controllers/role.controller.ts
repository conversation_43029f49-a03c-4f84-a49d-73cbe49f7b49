import { CountDto } from '@app/common/dto/count.dto';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import {
  RequireAdmin,
  RequirePolicyAccess,
} from '../decorators/authorization.decorators';
import {
  AssignRoleDto,
  BulkAssignRoleDto,
  CreateRoleDto,
  ListRolesDto,
  PaginatedRolesResponseDto,
  RoleResponseDto,
  UpdateRoleDto,
} from '../dto/role.dto';
import { RoleEntity } from '../entities/role.entity';
import { AuthorizationGuard } from '../guards/authorization.guard';
import { RoleService } from '../services/role.service';

@ApiTags('Role Management')
@Controller('api/v1/roles')
@UseGuards(JwtAuthGuard, AuthorizationGuard)
@ApiBearerAuth()
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @RequireAdmin()
  @ApiOperation({
    summary: 'Create a new role',
    description:
      'Creates a new role with specified permissions. Only administrators can create roles.',
  })
  @ApiCreatedResponse({
    description: 'Role created successfully',
    type: RoleResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or role name already exists',
  })
  @ApiForbiddenResponse({
    description: 'Admin role required',
  })
  async createRole(
    @Body() createRoleDto: CreateRoleDto,
    @Request() req: any,
  ): Promise<RoleResponseDto> {
    const role = await this.roleService.createRole(
      {
        name: createRoleDto.name,
        description: createRoleDto.description,
        type: createRoleDto.type,
        permissions: createRoleDto.permissions,
        isActive: createRoleDto.isActive,
        priority: createRoleDto.priority,
      },
      req.user.id,
    );

    return this.mapRoleToResponseDto(role);
  }

  @Get()
  @RequirePolicyAccess('role:list', 'arn:app:iam:*:role/*')
  @ApiOperation({
    summary: 'List roles',
    description: 'Retrieve a paginated list of roles with optional filtering.',
  })
  @ApiOkResponse({
    description: 'Roles retrieved successfully',
    type: PaginatedRolesResponseDto,
  })
  async listRoles(
    @Query() query: ListRolesDto,
  ): Promise<PaginatedRolesResponseDto> {
    const result = await this.roleService.listRoles({
      type: query.type,
      isActive: query.isActive,
      search: query.search,
      page: query.page,
      limit: query.limit,
      sortBy: query.sortBy,
      sortOrder: query.sortOrder,
    });

    return {
      data: result.data.map((role) => this.mapRoleToResponseDto(role)),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  @Get('count')
  @RequirePolicyAccess('role:list', 'arn:app:iam:*:role/*')
  @ApiOperation({
    summary: 'Get roles count',
    description: 'Get the total number of roles with optional filtering.',
  })
  @ApiOkResponse({
    description: 'Roles count retrieved successfully',
    type: CountDto,
  })
  async getRolesCount(@Query() query: ListRolesDto): Promise<CountDto> {
    const result = await this.roleService.listRoles({
      type: query.type,
      isActive: query.isActive,
      search: query.search,
      page: 1,
      limit: 1,
    });

    return { count: result.total };
  }

  @Get(':id')
  @RequirePolicyAccess('role:read', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Get role by ID',
    description: 'Retrieve detailed information about a specific role.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Role retrieved successfully',
    type: RoleResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Role not found',
  })
  async getRoleById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<RoleResponseDto> {
    const role = await this.roleService.findRoleById(id);
    return this.mapRoleToResponseDto(role);
  }

  @Put(':id')
  @RequirePolicyAccess('role:update', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Update role',
    description:
      'Update an existing role. Built-in system roles cannot be modified.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Role updated successfully',
    type: RoleResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or cannot modify built-in role',
  })
  @ApiNotFoundResponse({
    description: 'Role not found',
  })
  async updateRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Request() req: any,
  ): Promise<RoleResponseDto> {
    const role = await this.roleService.updateRole(
      id,
      {
        name: updateRoleDto.name,
        description: updateRoleDto.description,
        permissions: updateRoleDto.permissions,
        isActive: updateRoleDto.isActive,
        priority: updateRoleDto.priority,
      },
      req.user.id,
    );

    return this.mapRoleToResponseDto(role);
  }

  @Delete(':id')
  @RequirePolicyAccess('role:delete', 'arn:app:iam:*:role/${params.id}')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete role',
    description:
      'Delete a role. Built-in system roles and roles with active users cannot be deleted.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Role deleted successfully',
  })
  @ApiBadRequestResponse({
    description: 'Cannot delete built-in role or role with active users',
  })
  @ApiNotFoundResponse({
    description: 'Role not found',
  })
  async deleteRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.roleService.deleteRole(id, req.user.id);
  }

  @Post(':id/users')
  @RequirePolicyAccess('role:assign', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Assign role to user',
    description:
      'Assign a role to a user with optional expiration and conditions.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiCreatedResponse({
    description: 'Role assigned successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input or role assignment already exists',
  })
  @ApiNotFoundResponse({
    description: 'Role or user not found',
  })
  async assignRoleToUser(
    @Param('id', ParseUUIDPipe) roleId: string,
    @Body() assignRoleDto: AssignRoleDto,
    @Request() req: any,
  ): Promise<{ message: string; assignmentId: string }> {
    const assignment = await this.roleService.assignRoleToUser(
      {
        userId: assignRoleDto.userId,
        roleId,
        expiresAt: assignRoleDto.expiresAt
          ? new Date(assignRoleDto.expiresAt)
          : undefined,
        conditions: assignRoleDto.conditions,
      },
      req.user.id,
    );

    return {
      message: 'Role assigned successfully',
      assignmentId: assignment.id,
    };
  }

  @Delete(':id/users/:userId')
  @RequirePolicyAccess('role:revoke', 'arn:app:iam:*:role/${params.id}')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Revoke role from user',
    description: 'Remove a role assignment from a user.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Role revoked successfully',
  })
  @ApiNotFoundResponse({
    description: 'Role assignment not found',
  })
  async revokeRoleFromUser(
    @Param('id', ParseUUIDPipe) roleId: string,
    @Param('userId', ParseUUIDPipe) userId: string,
    @Request() req: any,
  ): Promise<void> {
    await this.roleService.revokeRoleFromUser(userId, roleId, req.user.id);
  }

  @Post(':id/users/bulk')
  @RequirePolicyAccess('role:assign', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Bulk assign role to users',
    description: 'Assign a role to multiple users at once.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiCreatedResponse({
    description: 'Bulk role assignment completed',
  })
  async bulkAssignRole(
    @Param('id', ParseUUIDPipe) roleId: string,
    @Body() bulkAssignDto: BulkAssignRoleDto,
    @Request() req: any,
  ): Promise<{
    message: string;
    successful: string[];
    failed: { userId: string; error: string }[];
  }> {
    const result = await this.roleService.performBulkRoleOperation(
      {
        operation: 'assign',
        userIds: bulkAssignDto.userIds,
        roleIds: [roleId],
        expiresAt: bulkAssignDto.expiresAt
          ? new Date(bulkAssignDto.expiresAt)
          : undefined,
        reason: bulkAssignDto.reason,
      },
      req.user.id,
    );

    return {
      message: 'Bulk role assignment completed',
      successful: result.successful,
      failed: result.failed,
    };
  }

  @Get(':id/users')
  @RequirePolicyAccess('role:read', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Get role users',
    description:
      'Get all users assigned to a specific role with their assignment details.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Role users retrieved successfully',
  })
  async getRoleUsers(@Param('id', ParseUUIDPipe) id: string) {
    const roleWithUsers = await this.roleService.getRoleWithUsers(id);
    return {
      role: {
        id: roleWithUsers.id,
        name: roleWithUsers.name,
        description: roleWithUsers.description,
      },
      userCount: roleWithUsers.userCount,
      users: roleWithUsers.users,
    };
  }

  @Get(':id/permissions')
  @RequirePolicyAccess('role:read', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Get role permissions',
    description:
      'Get comprehensive permissions summary for a role including direct permissions and policy-based permissions.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Role permissions retrieved successfully',
  })
  async getRolePermissions(@Param('id', ParseUUIDPipe) id: string) {
    return await this.roleService.getRolePermissionsSummary(id);
  }

  @Post(':id/policies/:policyId')
  @RequirePolicyAccess('role:attach-policy', 'arn:app:iam:*:role/${params.id}')
  @ApiOperation({
    summary: 'Attach policy to role',
    description: 'Attach a policy to a role for policy-based permissions.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'policyId',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiCreatedResponse({
    description: 'Policy attached to role successfully',
  })
  async attachPolicyToRole(
    @Param('id', ParseUUIDPipe) roleId: string,
    @Param('policyId', ParseUUIDPipe) policyId: string,
    @Request() req: any,
  ): Promise<{ message: string; attachmentId: string }> {
    const attachment = await this.roleService.attachPolicyToRole(
      roleId,
      policyId,
      req.user.id,
    );

    return {
      message: 'Policy attached to role successfully',
      attachmentId: attachment.id,
    };
  }

  @Delete(':id/policies/:policyId')
  @RequirePolicyAccess('role:detach-policy', 'arn:app:iam:*:role/${params.id}')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Detach policy from role',
    description: 'Remove a policy attachment from a role.',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'policyId',
    description: 'Policy ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Policy detached from role successfully',
  })
  async detachPolicyFromRole(
    @Param('id', ParseUUIDPipe) roleId: string,
    @Param('policyId', ParseUUIDPipe) policyId: string,
  ): Promise<void> {
    await this.roleService.detachPolicyFromRole(roleId, policyId);
  }

  /**
   * Map RoleEntity to RoleResponseDto
   */
  private mapRoleToResponseDto(role: RoleEntity): RoleResponseDto {
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      type: role.type,
      isBuiltIn: role.isBuiltIn,
      isActive: role.isActive,
      permissions: role.permissions,
      priority: role.priority,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  }
}
