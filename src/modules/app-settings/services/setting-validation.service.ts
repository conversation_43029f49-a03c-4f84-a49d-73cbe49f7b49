import { Injectable, Logger } from '@nestjs/common';
import Ajv, { JSONSchemaType, ValidateFunction } from 'ajv';
import addFormats from 'ajv-formats';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';
import {
  IValidationResult,
  IValidationSchema,
  IValidationService,
} from '../interfaces/validation.interface';

/**
 * Service for validating setting values against JSON schemas
 */
@Injectable()
export class SettingValidationService implements IValidationService {
  private readonly logger = new Logger(SettingValidationService.name);
  private readonly ajv: Ajv;
  private readonly schemaCache = new Map<string, ValidateFunction>();

  constructor() {
    // Initialize AJV with additional formats
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: false,
      useDefaults: true,
      coerceTypes: true,
    });

    // Add format validators (email, date, uri, etc.)
    addFormats(this.ajv);

    // Add custom formats
    this.addCustomFormats();
  }

  /**
   * Validate a value against a schema
   */
  validate(value: any, schema: IValidationSchema): IValidationResult {
    try {
      // Convert our schema format to JSON Schema
      const jsonSchema = this.convertToJsonSchema(schema);

      // Get or create validator function
      const schemaKey = JSON.stringify(jsonSchema);
      let validator = this.schemaCache.get(schemaKey);

      if (!validator) {
        validator = this.ajv.compile(jsonSchema);
        this.schemaCache.set(schemaKey, validator);
      }

      // Validate the value
      const isValid = validator(value);

      if (isValid) {
        return { isValid: true };
      }

      // Format validation errors
      const errors = this.formatValidationErrors(validator.errors || []);

      return {
        isValid: false,
        errors,
      };
    } catch (error) {
      this.logger.error('Validation failed', error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.VALIDATION_FAILED);
    }
  }

  /**
   * Validate setting value with optional schema
   */
  validateSetting(
    key: string,
    value: any,
    schema?: IValidationSchema,
  ): IValidationResult {
    // If no schema provided, consider it valid
    if (!schema) {
      return { isValid: true };
    }

    // Validate required field
    if (
      schema.required &&
      (value === null || value === undefined || value === '')
    ) {
      return {
        isValid: false,
        errors: [`Setting '${key}' is required but no value was provided`],
      };
    }

    // If value is not required and empty, skip validation
    if (
      !schema.required &&
      (value === null || value === undefined || value === '')
    ) {
      return { isValid: true };
    }

    try {
      return this.validate(value, schema);
    } catch (error) {
      this.logger.error(`Validation failed for setting '${key}'`, error);
      return {
        isValid: false,
        errors: [`Validation failed for setting '${key}': ${error.message}`],
      };
    }
  }

  /**
   * Validate a schema itself
   */
  validateSchema(schema: IValidationSchema): IValidationResult {
    try {
      const jsonSchema = this.convertToJsonSchema(schema);

      // Use AJV's built-in schema validation
      const isValid = this.ajv.validateSchema(jsonSchema);

      if (isValid) {
        return { isValid: true };
      }

      const errors = this.formatValidationErrors(this.ajv.errors || []);

      return {
        isValid: false,
        errors,
      };
    } catch (error) {
      this.logger.error('Schema validation failed', error);
      return {
        isValid: false,
        errors: [`Invalid schema: ${error.message}`],
      };
    }
  }

  /**
   * Convert our validation schema format to JSON Schema
   */
  private convertToJsonSchema(schema: IValidationSchema): JSONSchemaType<any> {
    const jsonSchema: any = {
      type: schema.type,
    };

    // Add string-specific properties
    if (schema.type === 'string') {
      if (schema.minLength !== undefined) {
        jsonSchema.minLength = schema.minLength;
      }
      if (schema.maxLength !== undefined) {
        jsonSchema.maxLength = schema.maxLength;
      }
      if (schema.pattern) {
        jsonSchema.pattern = schema.pattern;
      }
    }

    // Add number-specific properties
    if (schema.type === 'number') {
      if (schema.minLength !== undefined) {
        jsonSchema.minimum = schema.minLength;
      }
      if (schema.maxLength !== undefined) {
        jsonSchema.maximum = schema.maxLength;
      }
    }

    // Add array-specific properties
    if (schema.type === 'array') {
      if (schema.minLength !== undefined) {
        jsonSchema.minItems = schema.minLength;
      }
      if (schema.maxLength !== undefined) {
        jsonSchema.maxItems = schema.maxLength;
      }
    }

    // Add enum constraint
    if (schema.enum && schema.enum.length > 0) {
      jsonSchema.enum = schema.enum;
    }

    // Add object properties
    if (schema.type === 'object' && schema.properties) {
      jsonSchema.properties = {};
      jsonSchema.additionalProperties = false;
      jsonSchema.required = [];

      for (const [key, propSchema] of Object.entries(schema.properties)) {
        jsonSchema.properties[key] = this.convertToJsonSchema(propSchema);

        // Add to required array if property is required
        if (propSchema.required) {
          jsonSchema.required.push(key);
        }
      }

      // Remove required array if empty
      if (jsonSchema.required.length === 0) {
        delete jsonSchema.required;
      }
    }

    return jsonSchema;
  }

  /**
   * Format AJV validation errors into readable messages
   */
  private formatValidationErrors(errors: any[]): string[] {
    return errors.map((error) => {
      const path = error.instancePath || 'root';
      const message = error.message || 'validation failed';

      switch (error.keyword) {
        case 'required':
          return `Missing required property: ${error.params?.missingProperty}`;
        case 'type':
          return `Expected ${error.params?.type} but received ${typeof error.data} at ${path}`;
        case 'minLength':
          return `Value at ${path} must be at least ${error.params?.limit} characters long`;
        case 'maxLength':
          return `Value at ${path} must be no more than ${error.params?.limit} characters long`;
        case 'pattern':
          return `Value at ${path} does not match required pattern`;
        case 'enum':
          return `Value at ${path} must be one of: ${error.params?.allowedValues?.join(', ')}`;
        case 'minimum':
          return `Value at ${path} must be at least ${error.params?.limit}`;
        case 'maximum':
          return `Value at ${path} must be no more than ${error.params?.limit}`;
        case 'minItems':
          return `Array at ${path} must have at least ${error.params?.limit} items`;
        case 'maxItems':
          return `Array at ${path} must have no more than ${error.params?.limit} items`;
        default:
          return `Validation error at ${path}: ${message}`;
      }
    });
  }

  /**
   * Add custom format validators
   */
  private addCustomFormats(): void {
    // Add setting key format validator
    this.ajv.addFormat('setting-key', {
      type: 'string',
      validate: (value: string) => {
        // Setting keys should be alphanumeric with dots, underscores, and hyphens
        return /^[a-zA-Z0-9._-]+$/.test(value);
      },
    });

    // Add category format validator
    this.ajv.addFormat('category', {
      type: 'string',
      validate: (value: string) => {
        // Categories should be alphanumeric with dots, underscores, and hyphens
        return /^[a-zA-Z0-9._-]+$/.test(value);
      },
    });

    // Add JSON format validator
    this.ajv.addFormat('json', {
      type: 'string',
      validate: (value: string) => {
        try {
          JSON.parse(value);
          return true;
        } catch {
          return false;
        }
      },
    });
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.schemaCache.clear();
    this.logger.debug('Validation cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.schemaCache.size,
      keys: Array.from(this.schemaCache.keys()),
    };
  }
}
