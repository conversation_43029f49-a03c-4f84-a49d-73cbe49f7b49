import { Test, TestingModule } from '@nestjs/testing';
import { IValidationSchema } from '../interfaces/validation.interface';
import { SettingValidationService } from './setting-validation.service';

describe('SettingValidationService', () => {
  let service: SettingValidationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SettingValidationService],
    }).compile();

    service = module.get<SettingValidationService>(SettingValidationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validate', () => {
    it('should validate string values correctly', () => {
      const schema: IValidationSchema = {
        type: 'string',
        minLength: 3,
        maxLength: 10,
      };

      // Valid string
      const validResult = service.validate('hello', schema);
      expect(validResult.isValid).toBe(true);

      // Too short
      const shortResult = service.validate('hi', schema);
      expect(shortResult.isValid).toBe(false);
      expect(shortResult.errors).toContain(
        'Value at root must be at least 3 characters long',
      );

      // Too long
      const longResult = service.validate('this is too long', schema);
      expect(longResult.isValid).toBe(false);
      expect(longResult.errors).toContain(
        'Value at root must be no more than 10 characters long',
      );
    });

    it('should validate number values correctly', () => {
      const schema: IValidationSchema = {
        type: 'number',
        minLength: 1, // Using minLength as minimum for numbers
        maxLength: 100, // Using maxLength as maximum for numbers
      };

      // Valid number
      const validResult = service.validate(50, schema);
      expect(validResult.isValid).toBe(true);

      // Too small
      const smallResult = service.validate(0, schema);
      expect(smallResult.isValid).toBe(false);

      // Too large
      const largeResult = service.validate(150, schema);
      expect(largeResult.isValid).toBe(false);
    });

    it('should validate enum values correctly', () => {
      const schema: IValidationSchema = {
        type: 'string',
        enum: ['red', 'green', 'blue'],
      };

      // Valid enum value
      const validResult = service.validate('red', schema);
      expect(validResult.isValid).toBe(true);

      // Invalid enum value
      const invalidResult = service.validate('yellow', schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors?.[0]).toContain(
        'must be one of: red, green, blue',
      );
    });

    it('should validate object values correctly', () => {
      const schema: IValidationSchema = {
        type: 'object',
        properties: {
          name: { type: 'string', required: true },
          age: { type: 'number' },
        },
      };

      // Valid object
      const validResult = service.validate({ name: 'John', age: 30 }, schema);
      expect(validResult.isValid).toBe(true);

      // Missing required property
      const invalidResult = service.validate({ age: 30 }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors?.[0]).toContain(
        'Missing required property: name',
      );
    });
  });

  describe('validateSetting', () => {
    it('should return valid for settings without schema', () => {
      const result = service.validateSetting('test.key', 'any value');
      expect(result.isValid).toBe(true);
    });

    it('should validate required settings', () => {
      const schema: IValidationSchema = {
        type: 'string',
        required: true,
      };

      // Missing required value
      const invalidResult = service.validateSetting('test.key', '', schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors?.[0]).toContain(
        'is required but no value was provided',
      );

      // Valid required value
      const validResult = service.validateSetting('test.key', 'value', schema);
      expect(validResult.isValid).toBe(true);
    });

    it('should skip validation for optional empty values', () => {
      const schema: IValidationSchema = {
        type: 'string',
        required: false,
        minLength: 5,
      };

      // Empty optional value should be valid
      const result = service.validateSetting('test.key', '', schema);
      expect(result.isValid).toBe(true);
    });
  });

  describe('validateSchema', () => {
    it('should validate correct schemas', () => {
      const validSchema: IValidationSchema = {
        type: 'string',
        minLength: 1,
        maxLength: 100,
      };

      const result = service.validateSchema(validSchema);
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid schemas', () => {
      const invalidSchema = {
        type: 'invalid-type',
      } as any;

      const result = service.validateSchema(invalidSchema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });

  describe('custom formats', () => {
    it('should validate setting-key format', () => {
      const schema: IValidationSchema = {
        type: 'string',
        pattern: '^[a-zA-Z0-9._-]+$', // Equivalent to setting-key format
      };

      // Valid setting key
      const validResult = service.validate('app.database.host', schema);
      expect(validResult.isValid).toBe(true);

      // Invalid setting key
      const invalidResult = service.validate('app database host', schema);
      expect(invalidResult.isValid).toBe(false);
    });
  });

  describe('cache management', () => {
    it('should manage validation cache', () => {
      const schema: IValidationSchema = {
        type: 'string',
        minLength: 1,
      };

      // Validate to populate cache
      service.validate('test', schema);

      const stats = service.getCacheStats();
      expect(stats.size).toBeGreaterThan(0);

      // Clear cache
      service.clearCache();
      const clearedStats = service.getCacheStats();
      expect(clearedStats.size).toBe(0);
    });
  });
});
