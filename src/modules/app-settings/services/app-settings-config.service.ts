import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';
import {
  IAppSettingsConfig,
  IAuditConfig,
  IValidationConfig,
} from '../interfaces/config.interface';
import { IEncryptionConfig } from '../interfaces/encryption.interface';

/**
 * Service for managing app settings module configuration
 * Loads and validates configuration from environment variables
 */
@Injectable()
export class AppSettingsConfigService {
  private readonly config: IAppSettingsConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = this.loadAndValidateConfig();
  }

  /**
   * Get the complete app settings configuration
   * @returns IAppSettingsConfig - The validated configuration
   */
  getConfig(): IAppSettingsConfig {
    return this.config;
  }

  /**
   * Get encryption configuration
   * @returns IEncryptionConfig - The encryption configuration
   */
  getEncryptionConfig(): IEncryptionConfig {
    return this.config.encryption;
  }

  /**
   * Get audit configuration
   * @returns IAuditConfig - The audit configuration
   */
  getAuditConfig(): IAuditConfig {
    return this.config.audit;
  }

  /**
   * Get validation configuration
   * @returns IValidationConfig - The validation configuration
   */
  getValidationConfig(): IValidationConfig {
    return this.config.validation;
  }

  /**
   * Load and validate configuration from environment variables
   * @returns IAppSettingsConfig - The validated configuration
   * @private
   */
  private loadAndValidateConfig(): IAppSettingsConfig {
    try {
      const config: IAppSettingsConfig = {
        encryption: this.loadEncryptionConfig(),
        audit: this.loadAuditConfig(),
        validation: this.loadValidationConfig(),
      };

      this.validateConfig(config);
      return config;
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }
  }

  /**
   * Load encryption configuration from environment variables
   * @returns IEncryptionConfig - The encryption configuration
   * @private
   */
  private loadEncryptionConfig(): IEncryptionConfig {
    const key = this.configService.get<string>('APP_SETTINGS_ENCRYPTION_KEY');

    if (!key) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.MISSING_REQUIRED_CONFIG,
      );
    }

    if (key.length < 32) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    return {
      key,
      algorithm: 'aes-256-gcm',
    };
  }

  /**
   * Load audit configuration from environment variables
   * @returns IAuditConfig - The audit configuration
   * @private
   */
  private loadAuditConfig(): IAuditConfig {
    const enabled = this.configService.get<string>(
      'APP_SETTINGS_AUDIT_ENABLED',
      'true',
    );
    const retentionDays = this.configService.get<string>(
      'APP_SETTINGS_AUDIT_RETENTION_DAYS',
      '90',
    );

    const retentionDaysNumber = parseInt(retentionDays, 10);

    if (isNaN(retentionDaysNumber) || retentionDaysNumber < 1) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    return {
      enabled: enabled.toLowerCase() === 'true',
      retentionDays: retentionDaysNumber,
    };
  }

  /**
   * Load validation configuration from environment variables
   * @returns IValidationConfig - The validation configuration
   * @private
   */
  private loadValidationConfig(): IValidationConfig {
    const enabled = this.configService.get<string>(
      'APP_SETTINGS_VALIDATION_ENABLED',
      'true',
    );
    const strictMode = this.configService.get<string>(
      'APP_SETTINGS_VALIDATION_STRICT_MODE',
      'false',
    );

    return {
      enabled: enabled.toLowerCase() === 'true',
      strictMode: strictMode.toLowerCase() === 'true',
    };
  }

  /**
   * Validate the complete configuration
   * @param config - The configuration to validate
   * @private
   */
  private validateConfig(config: IAppSettingsConfig): void {
    // Validate encryption config
    if (!config.encryption) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.MISSING_REQUIRED_CONFIG,
      );
    }

    if (!config.encryption.key) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.MISSING_REQUIRED_CONFIG,
      );
    }

    if (config.encryption.algorithm !== 'aes-256-gcm') {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    // Validate audit config
    if (!config.audit) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.MISSING_REQUIRED_CONFIG,
      );
    }

    if (typeof config.audit.enabled !== 'boolean') {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    if (config.audit.retentionDays < 1) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    // Validate validation config
    if (!config.validation) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.MISSING_REQUIRED_CONFIG,
      );
    }

    if (typeof config.validation.enabled !== 'boolean') {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    if (typeof config.validation.strictMode !== 'boolean') {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }
  }

  /**
   * Check if encryption is enabled and properly configured
   * @returns boolean - True if encryption is available
   */
  isEncryptionEnabled(): boolean {
    try {
      return !!this.config.encryption.key;
    } catch {
      return false;
    }
  }

  /**
   * Check if audit is enabled
   * @returns boolean - True if audit is enabled
   */
  isAuditEnabled(): boolean {
    return this.config.audit.enabled;
  }

  /**
   * Check if validation is enabled
   * @returns boolean - True if validation is enabled
   */
  isValidationEnabled(): boolean {
    return this.config.validation.enabled;
  }

  /**
   * Check if strict validation mode is enabled
   * @returns boolean - True if strict mode is enabled
   */
  isStrictValidationEnabled(): boolean {
    return this.config.validation.enabled && this.config.validation.strictMode;
  }
}
