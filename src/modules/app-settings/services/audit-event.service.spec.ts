import { Test, TestingModule } from '@nestjs/testing';
import { AuditEventService } from './audit-event.service';

describe('AuditEventService', () => {
  let service: AuditEventService;
  let mockListener: jest.Mock;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AuditEventService],
    }).compile();

    service = module.get<AuditEventService>(AuditEventService);
    mockListener = jest.fn();
    service.registerAuditListener(mockListener);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('emitCreateEvent', () => {
    it('should emit create audit event with correct structure', async () => {
      const settingKey = 'test.setting';
      const newValue = 'test-value';
      const userId = 'user123';
      const metadata = { category: 'test' };

      await service.emitCreateEvent(settingKey, newValue, userId, metadata);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          settingKey,
          action: 'CREATE',
          newValue,
          userId,
          metadata: expect.objectContaining({
            ...metadata,
            source: 'app-settings-module',
            operation: 'create',
          }),
          timestamp: expect.any(Date),
        }),
      );
    });
  });

  describe('emitUpdateEvent', () => {
    it('should emit update audit event with old and new values', async () => {
      const settingKey = 'test.setting';
      const oldValue = 'old-value';
      const newValue = 'new-value';
      const userId = 'user123';

      await service.emitUpdateEvent(settingKey, oldValue, newValue, userId);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          settingKey,
          action: 'UPDATE',
          oldValue,
          newValue,
          userId,
          metadata: expect.objectContaining({
            source: 'app-settings-module',
            operation: 'update',
          }),
          timestamp: expect.any(Date),
        }),
      );
    });
  });

  describe('emitDeleteEvent', () => {
    it('should emit delete audit event with old value', async () => {
      const settingKey = 'test.setting';
      const oldValue = 'deleted-value';
      const userId = 'user123';

      await service.emitDeleteEvent(settingKey, oldValue, userId);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          settingKey,
          action: 'DELETE',
          oldValue,
          userId,
          metadata: expect.objectContaining({
            source: 'app-settings-module',
            operation: 'delete',
          }),
          timestamp: expect.any(Date),
        }),
      );
    });
  });

  describe('sanitizeValueForAudit', () => {
    it('should return [ENCRYPTED_VALUE] for encrypted values', () => {
      const result = service.sanitizeValueForAudit('some-value', true);
      expect(result).toBe('[ENCRYPTED_VALUE]');
    });

    it('should return [SENSITIVE_VALUE] for sensitive patterns', () => {
      const sensitiveValues = ['password123', 'secret-key', 'auth-token'];

      sensitiveValues.forEach((value) => {
        const result = service.sanitizeValueForAudit(value, false);
        expect(result).toBe('[SENSITIVE_VALUE]');
      });
    });

    it('should return original value for non-sensitive values', () => {
      const value = 'normal-setting-value';
      const result = service.sanitizeValueForAudit(value, false);
      expect(result).toBe(value);
    });
  });

  describe('createAuditMetadata', () => {
    it('should create metadata with default values', () => {
      const metadata = service.createAuditMetadata();

      expect(metadata).toEqual(
        expect.objectContaining({
          timestamp: expect.any(String),
          module: 'app-settings',
          version: '1.0.0',
        }),
      );
    });

    it('should merge additional context', () => {
      const context = { category: 'test', isSystem: true };
      const metadata = service.createAuditMetadata(context);

      expect(metadata).toEqual(
        expect.objectContaining({
          timestamp: expect.any(String),
          module: 'app-settings',
          version: '1.0.0',
          ...context,
        }),
      );
    });
  });

  describe('error handling', () => {
    it('should not throw when listener fails', async () => {
      const failingListener = jest.fn().mockImplementation(() => {
        throw new Error('Listener failed');
      });

      service.registerAuditListener(failingListener);

      await expect(
        service.emitCreateEvent('test.key', 'value', 'user123'),
      ).resolves.not.toThrow();

      expect(failingListener).toHaveBeenCalled();
    });
  });
});
