import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';
import { AppSettingEntity } from '../entities/app-setting.entity';
import { IEncryptedValue } from '../interfaces/encryption.interface';
import {
  ICreateSetting,
  ISetting,
  ISettingFilter,
  ISettingMetadata,
  IUpdateSetting,
} from '../interfaces/setting.interface';
import { AuditEventService } from './audit-event.service';
import { EncryptionService } from './encryption.service';
import { SettingValidationService } from './setting-validation.service';

/**
 * Service for managing application settings with CRUD operations
 * Handles system vs custom setting differentiation, encryption, and validation
 */
@Injectable()
export class AppSettingsService {
  private readonly logger = new Logger(AppSettingsService.name);

  constructor(
    @InjectRepository(AppSettingEntity)
    private readonly settingRepository: Repository<AppSettingEntity>,
    private readonly encryptionService: EncryptionService,
    private readonly validationService: SettingValidationService,
    private readonly auditEventService: AuditEventService,
  ) {}

  /**
   * Create a new setting
   * @param createData - Setting creation data
   * @returns Promise<ISetting> - The created setting
   */
  async create(createData: ICreateSetting): Promise<ISetting> {
    try {
      // Validate required fields
      this.validateCreateData(createData);

      // Check if key already exists
      const existingSetting = await this.settingRepository.findOne({
        where: { key: createData.key },
      });

      if (existingSetting) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.SETTING_KEY_ALREADY_EXISTS,
          {
            description: `Setting with key '${createData.key}' already exists`,
          },
        );
      }

      // Validate setting value against schema if provided
      if (createData.validationSchema) {
        const validationResult = this.validationService.validateSetting(
          createData.key,
          createData.value,
          createData.validationSchema as any,
        );

        if (!validationResult.isValid) {
          throw new HttpErrorException(
            APP_SETTINGS_ERROR_CODES.VALIDATION_FAILED,
            {
              description: `Setting value validation failed for key '${createData.key}': ${validationResult.errors?.join(', ')}`,
            },
          );
        }
      }

      // Prepare setting entity
      const settingEntity = new AppSettingEntity();
      settingEntity.key = createData.key;
      settingEntity.description = createData.description;
      settingEntity.category = createData.category;
      settingEntity.isSystem = createData.isSystem || false;
      settingEntity.isEncrypted = createData.isEncrypted || false;
      settingEntity.validationSchema = createData.validationSchema;
      settingEntity.createdBy = createData.createdBy;

      // Handle encryption if needed
      if (createData.isEncrypted) {
        const encryptedValue = await this.encryptionService.encrypt(
          JSON.stringify(createData.value),
        );
        settingEntity.value = JSON.stringify(encryptedValue);
      } else {
        settingEntity.value = JSON.stringify(createData.value);
      }

      // Save to database
      const savedSetting = await this.settingRepository.save(settingEntity);

      // Emit audit event for creation
      try {
        const auditValue = this.auditEventService.sanitizeValueForAudit(
          createData.value,
          createData.isEncrypted || false,
        );

        await this.auditEventService.emitCreateEvent(
          savedSetting.key,
          auditValue,
          createData.createdBy || 'system',
          this.auditEventService.createAuditMetadata({
            category: savedSetting.category,
            isSystem: savedSetting.isSystem,
            isEncrypted: savedSetting.isEncrypted,
          }),
        );
      } catch (auditError) {
        this.logger.warn(
          `Failed to emit audit event for setting creation: ${savedSetting.key}`,
          auditError,
        );
        // Continue execution - audit failures should not break the main operation
      }

      this.logger.log(`Setting created: ${savedSetting.key}`);

      return this.entityToInterface(savedSetting);
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(`Failed to create setting: ${createData.key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: `Failed to create setting '${createData.key}'`,
      });
    }
  }

  /**
   * Find a setting by key
   * @param key - Setting key
   * @returns Promise<ISetting | null> - The setting or null if not found
   */
  async findByKey(key: string): Promise<ISetting | null> {
    try {
      const setting = await this.settingRepository.findOne({
        where: { key },
      });

      if (!setting) {
        return null;
      }

      return this.entityToInterface(setting);
    } catch (error) {
      this.logger.error(`Failed to find setting by key: ${key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: `Failed to retrieve setting '${key}'`,
      });
    }
  }

  /**
   * Find settings with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise<ISetting[]> - Array of matching settings
   */
  async findMany(filter?: ISettingFilter): Promise<ISetting[]> {
    try {
      const queryBuilder = this.settingRepository.createQueryBuilder('setting');

      // Apply filters
      if (filter?.category) {
        queryBuilder.andWhere('setting.category = :category', {
          category: filter.category,
        });
      }

      if (filter?.isSystem !== undefined) {
        queryBuilder.andWhere('setting.isSystem = :isSystem', {
          isSystem: filter.isSystem,
        });
      }

      if (filter?.isEncrypted !== undefined) {
        queryBuilder.andWhere('setting.isEncrypted = :isEncrypted', {
          isEncrypted: filter.isEncrypted,
        });
      }

      if (filter?.keys && filter.keys.length > 0) {
        queryBuilder.andWhere('setting.key IN (:...keys)', {
          keys: filter.keys,
        });
      }

      // Order by key for consistent results
      queryBuilder.orderBy('setting.key', 'ASC');

      const settings = await queryBuilder.getMany();

      return Promise.all(
        settings.map((setting) => this.entityToInterface(setting)),
      );
    } catch (error) {
      this.logger.error('Failed to find settings', error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: 'Failed to retrieve settings',
      });
    }
  }

  /**
   * Update an existing setting
   * @param key - Setting key
   * @param updateData - Update data
   * @returns Promise<ISetting> - The updated setting
   */
  async update(key: string, updateData: IUpdateSetting): Promise<ISetting> {
    try {
      // Find existing setting
      const existingSetting = await this.settingRepository.findOne({
        where: { key },
      });

      if (!existingSetting) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.SETTING_NOT_FOUND,
          {
            description: `Setting with key '${key}' not found`,
          },
        );
      }

      // Capture old value for audit
      const oldValue = await this.getDecryptedValue(existingSetting);

      // Validate value against schema if provided
      const validationSchema =
        updateData.validationSchema || existingSetting.validationSchema;
      if (validationSchema && updateData.value !== undefined) {
        const validationResult = this.validationService.validateSetting(
          key,
          updateData.value,
          validationSchema as any,
        );

        if (!validationResult.isValid) {
          throw new HttpErrorException(
            APP_SETTINGS_ERROR_CODES.VALIDATION_FAILED,
            {
              description: `Setting value validation failed for key '${key}': ${validationResult.errors?.join(', ')}`,
            },
          );
        }
      }

      // Update fields
      if (updateData.description !== undefined) {
        existingSetting.description = updateData.description;
      }

      if (updateData.category !== undefined) {
        existingSetting.category = updateData.category;
      }

      if (updateData.validationSchema !== undefined) {
        existingSetting.validationSchema = updateData.validationSchema;
      }

      if (updateData.updatedBy !== undefined) {
        existingSetting.updatedBy = updateData.updatedBy;
      }

      // Handle value and encryption updates
      if (
        updateData.value !== undefined ||
        updateData.isEncrypted !== undefined
      ) {
        const newIsEncrypted =
          updateData.isEncrypted !== undefined
            ? updateData.isEncrypted
            : existingSetting.isEncrypted;

        const newValue =
          updateData.value !== undefined
            ? updateData.value
            : await this.getDecryptedValue(existingSetting);

        existingSetting.isEncrypted = newIsEncrypted;

        // Handle encryption state transitions
        if (newIsEncrypted) {
          const encryptedValue = await this.encryptionService.encrypt(
            JSON.stringify(newValue),
          );
          existingSetting.value = JSON.stringify(encryptedValue);
        } else {
          existingSetting.value = JSON.stringify(newValue);
        }
      }

      // Save updated setting
      const updatedSetting = await this.settingRepository.save(existingSetting);

      // Emit audit event for update
      try {
        const newValue =
          updateData.value !== undefined
            ? updateData.value
            : await this.getDecryptedValue(updatedSetting);

        const auditOldValue = this.auditEventService.sanitizeValueForAudit(
          oldValue,
          existingSetting.isEncrypted,
        );

        const auditNewValue = this.auditEventService.sanitizeValueForAudit(
          newValue,
          updatedSetting.isEncrypted,
        );

        await this.auditEventService.emitUpdateEvent(
          key,
          auditOldValue,
          auditNewValue,
          updateData.updatedBy || 'system',
          this.auditEventService.createAuditMetadata({
            category: updatedSetting.category,
            isSystem: updatedSetting.isSystem,
            isEncrypted: updatedSetting.isEncrypted,
            fieldsUpdated: Object.keys(updateData),
          }),
        );
      } catch (auditError) {
        this.logger.warn(
          `Failed to emit audit event for setting update: ${key}`,
          auditError,
        );
        // Continue execution - audit failures should not break the main operation
      }

      this.logger.log(`Setting updated: ${key}`);

      return this.entityToInterface(updatedSetting);
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(`Failed to update setting: ${key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: `Failed to update setting '${key}'`,
      });
    }
  }

  /**
   * Delete a setting (only custom settings can be deleted)
   * @param key - Setting key
   * @param deletedBy - User who is deleting the setting
   * @returns Promise<void>
   */
  async delete(key: string, deletedBy?: string): Promise<void> {
    try {
      // Find existing setting
      const existingSetting = await this.settingRepository.findOne({
        where: { key },
      });

      if (!existingSetting) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.SETTING_NOT_FOUND,
          {
            description: `Setting with key '${key}' not found`,
          },
        );
      }

      // Check if it's a system setting
      if (existingSetting.isSystem) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.SYSTEM_SETTING_DELETE_FORBIDDEN,
          {
            description: `System setting '${key}' cannot be deleted`,
          },
        );
      }

      // Capture old value for audit before deletion
      const oldValue = await this.getDecryptedValue(existingSetting);

      // Delete the setting
      await this.settingRepository.remove(existingSetting);

      // Emit audit event for deletion
      try {
        const auditOldValue = this.auditEventService.sanitizeValueForAudit(
          oldValue,
          existingSetting.isEncrypted,
        );

        await this.auditEventService.emitDeleteEvent(
          key,
          auditOldValue,
          deletedBy || 'system',
          this.auditEventService.createAuditMetadata({
            category: existingSetting.category,
            isSystem: existingSetting.isSystem,
            isEncrypted: existingSetting.isEncrypted,
          }),
        );
      } catch (auditError) {
        this.logger.warn(
          `Failed to emit audit event for setting deletion: ${key}`,
          auditError,
        );
        // Continue execution - audit failures should not break the main operation
      }

      this.logger.log(`Setting deleted: ${key}`);
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(`Failed to delete setting: ${key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: `Failed to delete setting '${key}'`,
      });
    }
  }

  /**
   * Check if a setting exists
   * @param key - Setting key
   * @returns Promise<boolean> - True if setting exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const count = await this.settingRepository.count({
        where: { key },
      });
      return count > 0;
    } catch (error) {
      this.logger.error(`Failed to check setting existence: ${key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: `Failed to check setting existence for key '${key}'`,
      });
    }
  }

  /**
   * Convert entity to interface with proper decryption
   * @param entity - Setting entity
   * @returns Promise<ISetting> - Setting interface
   * @private
   */
  private async entityToInterface(entity: AppSettingEntity): Promise<ISetting> {
    const metadata: ISettingMetadata = {
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
    };

    // Handle decryption if needed
    let value: any;
    if (entity.isEncrypted) {
      value = await this.getDecryptedValue(entity);
    } else {
      try {
        value = JSON.parse(entity.value);
      } catch {
        value = entity.value;
      }
    }

    return {
      key: entity.key,
      value,
      description: entity.description,
      category: entity.category,
      isSystem: entity.isSystem,
      isEncrypted: entity.isEncrypted,
      validationSchema: entity.validationSchema,
      metadata,
    };
  }

  /**
   * Get decrypted value from entity
   * @param entity - Setting entity
   * @returns Promise<any> - Decrypted value
   * @private
   */
  private async getDecryptedValue(entity: AppSettingEntity): Promise<any> {
    if (!entity.isEncrypted) {
      try {
        return JSON.parse(entity.value);
      } catch {
        return entity.value;
      }
    }

    try {
      const encryptedValue: IEncryptedValue = JSON.parse(entity.value);
      const decryptedString =
        await this.encryptionService.decrypt(encryptedValue);
      return JSON.parse(decryptedString);
    } catch (error) {
      this.logger.error(`Failed to decrypt setting: ${entity.key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DECRYPTION_FAILED, {
        description: `Failed to decrypt setting '${entity.key}'`,
      });
    }
  }

  /**
   * Validate create setting data
   * @param createData - Create setting data
   * @private
   */
  private validateCreateData(createData: ICreateSetting): void {
    if (!createData.key || createData.key.trim() === '') {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.SETTING_KEY_REQUIRED,
        {
          description: 'Setting key is required',
        },
      );
    }

    if (createData.value === undefined || createData.value === null) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.SETTING_VALUE_REQUIRED,
        {
          description: 'Setting value is required',
        },
      );
    }

    // Validate key format
    if (!/^[a-zA-Z0-9._-]+$/.test(createData.key)) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_SETTING_KEY,
        {
          description: `Setting key '${createData.key}' must contain only alphanumeric characters, dots, underscores, and hyphens`,
        },
      );
    }
  }
}
