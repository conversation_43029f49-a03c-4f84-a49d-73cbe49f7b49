import { Injectable } from '@nestjs/common';
import {
  createCipher<PERSON>,
  createDecipher<PERSON>,
  createHash,
  randomBytes,
} from 'crypto';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';
import {
  IEncryptedValue,
  IEncryptionService,
} from '../interfaces/encryption.interface';
import { AppSettingsConfigService } from './app-settings-config.service';

/**
 * Service for handling encryption and decryption of setting values
 * Uses AES-256-GCM for secure encryption with authentication
 */
@Injectable()
export class EncryptionService implements IEncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16; // 128 bits
  private readonly tagLength = 16; // 128 bits

  constructor(private readonly configService: AppSettingsConfigService) {}

  /**
   * Encrypt a string value using AES-256-GCM
   * @param value - The plain text value to encrypt
   * @returns Promise<IEncryptedValue> - The encrypted value with IV and authentication tag
   */
  async encrypt(value: string): Promise<IEncryptedValue> {
    try {
      const encryptionKey = this.getEncryptionKey();

      // Generate a random IV for each encryption
      const iv = randomBytes(this.ivLength);

      // Create cipher
      const cipher = createCipheriv(this.algorithm, encryptionKey, iv);
      cipher.setAAD(Buffer.from('app-settings')); // Additional authenticated data

      // Encrypt the value
      let encrypted = cipher.update(value, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Get the authentication tag
      const tag = cipher.getAuthTag();

      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        algorithm: this.algorithm,
      };
    } catch (error) {
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.ENCRYPTION_FAILED);
    }
  }

  /**
   * Decrypt an encrypted value using AES-256-GCM
   * @param encryptedValue - The encrypted value object
   * @returns Promise<string> - The decrypted plain text value
   */
  async decrypt(encryptedValue: IEncryptedValue): Promise<string> {
    try {
      this.validateEncryptedValue(encryptedValue);

      const encryptionKey = this.getEncryptionKey();

      // Convert hex strings back to buffers
      const iv = Buffer.from(encryptedValue.iv, 'hex');
      const tag = Buffer.from(encryptedValue.tag, 'hex');

      // Create decipher
      const decipher = createDecipheriv(this.algorithm, encryptionKey, iv);
      decipher.setAuthTag(tag);
      decipher.setAAD(Buffer.from('app-settings')); // Same AAD used during encryption

      // Decrypt the value
      let decrypted = decipher.update(encryptedValue.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DECRYPTION_FAILED);
    }
  }

  /**
   * Check if a value is encrypted by examining its structure
   * @param value - The value to check
   * @returns boolean - True if the value appears to be encrypted
   */
  isEncrypted(value: any): boolean {
    if (!value || typeof value !== 'object') {
      return false;
    }

    return (
      typeof value.encrypted === 'string' &&
      typeof value.iv === 'string' &&
      typeof value.tag === 'string' &&
      value.algorithm === this.algorithm
    );
  }

  /**
   * Get the encryption key from configuration
   * @returns Buffer - The encryption key
   * @private
   */
  private getEncryptionKey(): Buffer {
    const encryptionConfig = this.configService.getEncryptionConfig();

    if (!encryptionConfig.key) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.ENCRYPTION_KEY_MISSING,
      );
    }

    // Derive a consistent key from the provided key using SHA-256
    return createHash('sha256').update(encryptionConfig.key).digest();
  }

  /**
   * Validate the structure of an encrypted value
   * @param encryptedValue - The encrypted value to validate
   * @private
   */
  private validateEncryptedValue(encryptedValue: IEncryptedValue): void {
    if (!encryptedValue) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_ENCRYPTED_VALUE,
      );
    }

    if (!this.isEncrypted(encryptedValue)) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_ENCRYPTED_VALUE,
      );
    }

    // Validate hex string lengths
    if (encryptedValue.iv.length !== this.ivLength * 2) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_ENCRYPTED_VALUE,
      );
    }

    if (encryptedValue.tag.length !== this.tagLength * 2) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_ENCRYPTED_VALUE,
      );
    }
  }
}
