import { Injectable, Logger } from '@nestjs/common';
import { IAuditEvent } from '../interfaces/audit.interface';

/**
 * Service for emitting audit events for settings changes
 * Handles event structure and metadata for all setting modifications
 */
@Injectable()
export class AuditEventService {
  private readonly logger = new Logger(AuditEventService.name);
  private readonly auditListeners: Array<(event: IAuditEvent) => void> = [];

  constructor() {}

  /**
   * Emit audit event for setting creation
   * @param settingKey - The setting key
   * @param newValue - The new setting value
   * @param userId - User who performed the action
   * @param metadata - Additional metadata
   */
  async emitCreateEvent(
    settingKey: string,
    newValue: any,
    userId: string,
    metadata?: object,
  ): Promise<void> {
    const event: IAuditEvent = {
      settingKey,
      action: 'CREATE',
      newValue,
      userId,
      metadata: {
        ...metadata,
        source: 'app-settings-module',
        operation: 'create',
      },
      timestamp: new Date(),
    };

    await this.emitAuditEvent(event);
  }

  /**
   * Emit audit event for setting update
   * @param settingKey - The setting key
   * @param oldValue - The previous setting value
   * @param newValue - The new setting value
   * @param userId - User who performed the action
   * @param metadata - Additional metadata
   */
  async emitUpdateEvent(
    settingKey: string,
    oldValue: any,
    newValue: any,
    userId: string,
    metadata?: object,
  ): Promise<void> {
    const event: IAuditEvent = {
      settingKey,
      action: 'UPDATE',
      oldValue,
      newValue,
      userId,
      metadata: {
        ...metadata,
        source: 'app-settings-module',
        operation: 'update',
      },
      timestamp: new Date(),
    };

    await this.emitAuditEvent(event);
  }

  /**
   * Emit audit event for setting deletion
   * @param settingKey - The setting key
   * @param oldValue - The deleted setting value
   * @param userId - User who performed the action
   * @param metadata - Additional metadata
   */
  async emitDeleteEvent(
    settingKey: string,
    oldValue: any,
    userId: string,
    metadata?: object,
  ): Promise<void> {
    const event: IAuditEvent = {
      settingKey,
      action: 'DELETE',
      oldValue,
      userId,
      metadata: {
        ...metadata,
        source: 'app-settings-module',
        operation: 'delete',
      },
      timestamp: new Date(),
    };

    await this.emitAuditEvent(event);
  }

  /**
   * Register an audit event listener
   * @param listener - Function to handle audit events
   */
  registerAuditListener(listener: (event: IAuditEvent) => void): void {
    this.auditListeners.push(listener);
  }

  /**
   * Emit a generic audit event
   * @param event - The audit event to emit
   * @private
   */
  private emitAuditEvent(event: IAuditEvent): void {
    try {
      // Log the audit event
      this.logger.log(
        `Audit event: ${event.action} for setting '${event.settingKey}' by user '${event.userId}'`,
      );

      // Notify all registered listeners
      this.auditListeners.forEach((listener) => {
        try {
          listener(event);
        } catch (listenerError) {
          this.logger.warn(
            `Audit listener failed for setting '${event.settingKey}'`,
            listenerError,
          );
        }
      });

      this.logger.debug(
        `Audit event processed: ${event.action} for setting '${event.settingKey}' by user '${event.userId}'`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to emit audit event for setting '${event.settingKey}'`,
        error,
      );
      // Don't throw error to avoid breaking the main operation
      // Audit failures should not prevent setting operations
    }
  }

  /**
   * Create audit metadata with additional context
   * @param context - Additional context information
   * @returns Audit metadata object
   */
  createAuditMetadata(context?: Record<string, any>): object {
    return {
      timestamp: new Date().toISOString(),
      module: 'app-settings',
      version: '1.0.0',
      ...context,
    };
  }

  /**
   * Sanitize sensitive values for audit logging
   * @param value - The value to sanitize
   * @param isEncrypted - Whether the value is encrypted
   * @returns Sanitized value for audit logging
   */
  sanitizeValueForAudit(value: any, isEncrypted: boolean): any {
    if (isEncrypted) {
      return '[ENCRYPTED_VALUE]';
    }

    // For sensitive keys, mask the value
    if (typeof value === 'string' && this.isSensitiveValue(value)) {
      return '[SENSITIVE_VALUE]';
    }

    return value;
  }

  /**
   * Check if a value should be considered sensitive
   * @param value - The value to check
   * @returns True if the value appears to be sensitive
   * @private
   */
  private isSensitiveValue(value: string): boolean {
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /token/i,
      /key/i,
      /credential/i,
      /auth/i,
    ];

    return sensitivePatterns.some((pattern) => pattern.test(value));
  }
}
