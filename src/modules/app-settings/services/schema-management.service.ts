import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Repository } from 'typeorm';
import { HttpErrorException } from '../../../common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';
import { AppSettingEntity } from '../entities/app-setting.entity';
import {
  IValidationResult,
  IValidationSchema,
} from '../interfaces/validation.interface';
import { SettingValidationService } from './setting-validation.service';

/**
 * Interface for schema update result
 */
export interface ISchemaUpdateResult {
  success: boolean;
  affectedSettings: string[];
  validationErrors?: Record<string, string[]>;
}

/**
 * Interface for schema compatibility check
 */
export interface ISchemaCompatibilityResult {
  isCompatible: boolean;
  breakingChanges: string[];
  warnings: string[];
}

/**
 * Service for managing validation schemas
 */
@Injectable()
export class SchemaManagementService {
  private readonly logger = new Logger(SchemaManagementService.name);

  constructor(
    @InjectRepository(AppSettingEntity)
    private readonly settingRepository: Repository<AppSettingEntity>,
    private readonly validationService: SettingValidationService,
  ) {}

  /**
   * Store or update validation schema for a setting
   */
  async storeSchema(key: string, schema: IValidationSchema): Promise<void> {
    try {
      // Validate the schema itself
      const schemaValidation = this.validationService.validateSchema(schema);
      if (!schemaValidation.isValid) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.INVALID_VALIDATION_SCHEMA,
        );
      }

      // Check if setting exists
      const setting = await this.settingRepository.findOne({ where: { key } });
      if (!setting) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.SETTING_NOT_FOUND,
        );
      }

      // If setting has existing schema, check compatibility
      if (setting.validationSchema) {
        const compatibilityResult = this.checkSchemaCompatibility(
          setting.validationSchema as IValidationSchema,
          schema,
        );

        if (!compatibilityResult.isCompatible) {
          this.logger.warn(`Schema update for '${key}' has breaking changes`, {
            breakingChanges: compatibilityResult.breakingChanges,
          });
        }
      }

      // Validate current setting value against new schema
      if (setting.value) {
        let valueToValidate = setting.value;

        // If setting is encrypted, we can't validate the encrypted value
        // This is a limitation - encrypted values can't be validated against schema changes
        if (!setting.isEncrypted) {
          try {
            // Try to parse as JSON first
            valueToValidate = JSON.parse(setting.value);
          } catch {
            // If not JSON, use as string
          }

          const validation = this.validationService.validate(
            valueToValidate,
            schema,
          );
          if (!validation.isValid) {
            throw new HttpErrorException(
              APP_SETTINGS_ERROR_CODES.VALIDATION_FAILED,
            );
          }
        }
      }

      // Update the schema
      await this.settingRepository.update(
        { key },
        {
          validationSchema: schema,
          updatedAt: new Date(),
        },
      );

      this.logger.log(`Schema updated for setting '${key}'`);
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(`Failed to store schema for setting '${key}'`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR);
    }
  }

  /**
   * Retrieve validation schema for a setting
   */
  async getSchema(key: string): Promise<IValidationSchema | null> {
    try {
      const setting = await this.settingRepository.findOne({
        where: { key },
        select: ['key', 'validationSchema'],
      });

      if (!setting) {
        return null;
      }

      return (setting.validationSchema as IValidationSchema) || null;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve schema for setting '${key}'`,
        error,
      );
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR);
    }
  }

  /**
   * Remove validation schema from a setting
   */
  async removeSchema(key: string): Promise<void> {
    try {
      const result = await this.settingRepository.update(
        { key },
        {
          validationSchema: null as any,
          updatedAt: new Date(),
        },
      );

      if (result.affected === 0) {
        throw new HttpErrorException(
          APP_SETTINGS_ERROR_CODES.SETTING_NOT_FOUND,
        );
      }

      this.logger.log(`Schema removed from setting '${key}'`);
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }

      this.logger.error(`Failed to remove schema from setting '${key}'`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR);
    }
  }

  /**
   * Validate all settings with schemas
   */
  async validateAllSettings(): Promise<Record<string, IValidationResult>> {
    try {
      const settingsWithSchemas = await this.settingRepository.find({
        where: {
          validationSchema: Not(IsNull()),
        },
      });

      const results: Record<string, IValidationResult> = {};

      for (const setting of settingsWithSchemas) {
        if (!setting.validationSchema) continue;

        // Skip encrypted settings as we can't validate encrypted values
        if (setting.isEncrypted) {
          results[setting.key] = {
            isValid: true,
            errors: ['Skipped validation for encrypted setting'],
          };
          continue;
        }

        let valueToValidate = setting.value;
        try {
          valueToValidate = JSON.parse(setting.value);
        } catch {
          // Use as string if not JSON
        }

        results[setting.key] = this.validationService.validate(
          valueToValidate,
          setting.validationSchema as IValidationSchema,
        );
      }

      return results;
    } catch (error) {
      this.logger.error('Failed to validate all settings', error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR);
    }
  }

  /**
   * Update schema and validate affected settings
   */
  async updateSchemaWithValidation(
    key: string,
    newSchema: IValidationSchema,
  ): Promise<ISchemaUpdateResult> {
    try {
      // Get current schema for compatibility check
      const currentSchema = await this.getSchema(key);

      // Check compatibility if there's an existing schema
      let compatibilityResult: ISchemaCompatibilityResult | null = null;
      if (currentSchema) {
        compatibilityResult = this.checkSchemaCompatibility(
          currentSchema,
          newSchema,
        );
      }

      // Store the new schema
      await this.storeSchema(key, newSchema);

      // Validate the specific setting
      const setting = await this.settingRepository.findOne({ where: { key } });
      const validationErrors: Record<string, string[]> = {};

      if (setting && setting.value && !setting.isEncrypted) {
        let valueToValidate = setting.value;
        try {
          valueToValidate = JSON.parse(setting.value);
        } catch {
          // Use as string if not JSON
        }

        const validation = this.validationService.validate(
          valueToValidate,
          newSchema,
        );
        if (!validation.isValid) {
          validationErrors[key] = validation.errors || [];
        }
      }

      return {
        success: Object.keys(validationErrors).length === 0,
        affectedSettings: [key],
        validationErrors:
          Object.keys(validationErrors).length > 0
            ? validationErrors
            : undefined,
      };
    } catch (error) {
      this.logger.error(`Failed to update schema for setting '${key}'`, error);
      return {
        success: false,
        affectedSettings: [key],
        validationErrors: { [key]: [error.message] },
      };
    }
  }

  /**
   * Check compatibility between old and new schemas
   */
  checkSchemaCompatibility(
    oldSchema: IValidationSchema,
    newSchema: IValidationSchema,
  ): ISchemaCompatibilityResult {
    const breakingChanges: string[] = [];
    const warnings: string[] = [];

    // Check type changes
    if (oldSchema.type !== newSchema.type) {
      breakingChanges.push(
        `Type changed from '${oldSchema.type}' to '${newSchema.type}'`,
      );
    }

    // Check required field changes
    if (!oldSchema.required && newSchema.required) {
      breakingChanges.push('Field is now required');
    }
    if (oldSchema.required && !newSchema.required) {
      warnings.push('Field is no longer required');
    }

    // Check string constraints
    if (oldSchema.type === 'string' && newSchema.type === 'string') {
      // Min length increased
      if ((newSchema.minLength || 0) > (oldSchema.minLength || 0)) {
        breakingChanges.push(
          `Minimum length increased from ${oldSchema.minLength || 0} to ${newSchema.minLength}`,
        );
      }

      // Max length decreased
      if (
        oldSchema.maxLength &&
        newSchema.maxLength &&
        newSchema.maxLength < oldSchema.maxLength
      ) {
        breakingChanges.push(
          `Maximum length decreased from ${oldSchema.maxLength} to ${newSchema.maxLength}`,
        );
      }

      // Pattern changed
      if (oldSchema.pattern !== newSchema.pattern) {
        if (newSchema.pattern && !oldSchema.pattern) {
          breakingChanges.push('Pattern validation added');
        } else if (oldSchema.pattern !== newSchema.pattern) {
          breakingChanges.push('Pattern validation changed');
        }
      }
    }

    // Check enum constraints
    if (oldSchema.enum && newSchema.enum) {
      const oldEnumSet = new Set(oldSchema.enum);
      const newEnumSet = new Set(newSchema.enum);

      // Check if any old values are no longer allowed
      const removedValues = oldSchema.enum.filter(
        (value) => !newEnumSet.has(value),
      );
      if (removedValues.length > 0) {
        breakingChanges.push(
          `Enum values removed: ${removedValues.join(', ')}`,
        );
      }

      // Check if new values were added
      const addedValues = newSchema.enum.filter(
        (value) => !oldEnumSet.has(value),
      );
      if (addedValues.length > 0) {
        warnings.push(`Enum values added: ${addedValues.join(', ')}`);
      }
    } else if (!oldSchema.enum && newSchema.enum) {
      breakingChanges.push('Enum constraint added');
    } else if (oldSchema.enum && !newSchema.enum) {
      warnings.push('Enum constraint removed');
    }

    // Check object properties
    if (oldSchema.type === 'object' && newSchema.type === 'object') {
      if (oldSchema.properties && newSchema.properties) {
        // Check for removed properties
        const oldProps = Object.keys(oldSchema.properties);
        const newProps = Object.keys(newSchema.properties);

        const removedProps = oldProps.filter(
          (prop) => !newProps.includes(prop),
        );
        if (removedProps.length > 0) {
          breakingChanges.push(
            `Object properties removed: ${removedProps.join(', ')}`,
          );
        }

        const addedProps = newProps.filter((prop) => !oldProps.includes(prop));
        if (addedProps.length > 0) {
          warnings.push(`Object properties added: ${addedProps.join(', ')}`);
        }
      }
    }

    return {
      isCompatible: breakingChanges.length === 0,
      breakingChanges,
      warnings,
    };
  }

  /**
   * Get all settings with their schemas
   */
  async getAllSchemasInfo(): Promise<
    Array<{
      key: string;
      hasSchema: boolean;
      schema?: IValidationSchema;
      category?: string;
      isSystem: boolean;
    }>
  > {
    try {
      const settings = await this.settingRepository.find({
        select: ['key', 'validationSchema', 'category', 'isSystem'],
      });

      return settings.map((setting) => ({
        key: setting.key,
        hasSchema: !!setting.validationSchema,
        schema: (setting.validationSchema as IValidationSchema) || undefined,
        category: setting.category,
        isSystem: setting.isSystem,
      }));
    } catch (error) {
      this.logger.error('Failed to retrieve schemas info', error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR);
    }
  }
}
