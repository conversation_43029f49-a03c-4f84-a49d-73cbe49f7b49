# App Settings Module Configuration

# Encryption Configuration (REQUIRED)
# Generate a secure 64-character key for production
APP_SETTINGS_ENCRYPTION_KEY=your-secure-64-character-encryption-key-here-change-this

# Audit Configuration (OPTIONAL)
# Enable/disable audit logging for settings changes
APP_SETTINGS_AUDIT_ENABLED=true

# Number of days to retain audit logs (1-3650)
APP_SETTINGS_AUDIT_RETENTION_DAYS=90

# Validation Configuration (OPTIONAL)
# Enable/disable setting value validation
APP_SETTINGS_VALIDATION_ENABLED=true

# Enable strict validation mode (more restrictive)
APP_SETTINGS_VALIDATION_STRICT_MODE=false