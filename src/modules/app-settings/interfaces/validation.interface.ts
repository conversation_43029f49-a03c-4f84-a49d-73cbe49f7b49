/**
 * Interface for validation schema
 */
export interface IValidationSchema {
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  enum?: any[];
  properties?: Record<string, IValidationSchema>;
}

/**
 * Interface for validation result
 */
export interface IValidationResult {
  isValid: boolean;
  errors?: string[];
}

/**
 * Interface for validation service
 */
export interface IValidationService {
  /**
   * Validate a value against a schema
   */
  validate(value: any, schema: IValidationSchema): IValidationResult;

  /**
   * Validate setting value
   */
  validateSetting(
    key: string,
    value: any,
    schema?: IValidationSchema,
  ): IValidationResult;
}
