/**
 * Audit action types
 */
export type AuditAction = 'CREATE' | 'UPDATE' | 'DELETE';

/**
 * Interface for audit event
 */
export interface IAuditEvent {
  settingKey: string;
  action: AuditAction;
  oldValue?: any;
  newValue?: any;
  userId: string;
  metadata?: object;
  timestamp: Date;
}

/**
 * Interface for audit service
 */
export interface IAuditService {
  /**
   * Log an audit event
   */
  logEvent(event: IAuditEvent): Promise<void>;

  /**
   * Get audit history for a setting
   */
  getHistory(settingKey: string): Promise<IAuditEvent[]>;
}
