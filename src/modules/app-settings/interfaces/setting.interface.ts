/**
 * Core interface for application settings
 */
export interface ISetting {
  key: string;
  value: any;
  description?: string;
  category?: string;
  isSystem: boolean;
  isEncrypted: boolean;
  validationSchema?: object;
  metadata: ISettingMetadata;
}

/**
 * Setting metadata interface
 */
export interface ISettingMetadata {
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

/**
 * Interface for setting creation
 */
export interface ICreateSetting {
  key: string;
  value: any;
  description?: string;
  category?: string;
  isSystem?: boolean;
  isEncrypted?: boolean;
  validationSchema?: object;
  createdBy?: string;
}

/**
 * Interface for setting updates
 */
export interface IUpdateSetting {
  value?: any;
  description?: string;
  category?: string;
  isEncrypted?: boolean;
  validationSchema?: object;
  updatedBy?: string;
}

/**
 * Interface for setting retrieval filters
 */
export interface ISettingFilter {
  category?: string;
  isSystem?: boolean;
  isEncrypted?: boolean;
  keys?: string[];
}
