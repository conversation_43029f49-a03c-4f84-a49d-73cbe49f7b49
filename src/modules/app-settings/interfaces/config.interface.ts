import { IEncryptionConfig } from './encryption.interface';

/**
 * Interface for app settings module configuration
 */
export interface IAppSettingsConfig {
  encryption: IEncryptionConfig;
  audit: IAuditConfig;
  validation: IValidationConfig;
}

/**
 * Interface for audit configuration
 */
export interface IAuditConfig {
  enabled: boolean;
  retentionDays: number;
}

/**
 * Interface for validation configuration
 */
export interface IValidationConfig {
  enabled: boolean;
  strictMode: boolean;
}
