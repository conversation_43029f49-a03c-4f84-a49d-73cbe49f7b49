/**
 * Interface for encrypted value structure
 */
export interface IEncryptedValue {
  encrypted: string;
  iv: string;
  tag: string;
  algorithm: 'aes-256-gcm';
}

/**
 * Interface for encryption service
 */
export interface IEncryptionService {
  /**
   * Encrypt a value
   */
  encrypt(value: string): Promise<IEncryptedValue>;

  /**
   * Decrypt an encrypted value
   */
  decrypt(encryptedValue: IEncryptedValue): Promise<string>;

  /**
   * Check if a value is encrypted
   */
  isEncrypted(value: any): boolean;
}

/**
 * Interface for encryption configuration
 */
export interface IEncryptionConfig {
  key: string;
  algorithm: 'aes-256-gcm';
}
