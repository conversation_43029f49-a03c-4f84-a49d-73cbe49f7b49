import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '../../common/types';

const APP_SETTINGS_NAMESPACE = 'APP_SETTINGS';

type AppSettingsErrorCodes =
  | 'SETTING_NOT_FOUND'
  | 'SYSTEM_SETTING_DELETE_FORBIDDEN'
  | 'SYSTEM_SETTING_MODIFICATION_RESTRICTED'
  | 'SETTING_KEY_ALREADY_EXISTS'
  | 'INVALID_SETTING_KEY'
  | 'SETTING_KEY_REQUIRED'
  | 'VALIDATION_FAILED'
  | 'INVALID_VALIDATION_SCHEMA'
  | 'SETTING_VALUE_REQUIRED'
  | 'ENCRYPTION_FAILED'
  | 'DECRYPTION_FAILED'
  | 'ENCRYPTION_KEY_MISSING'
  | 'INVALID_ENCRYPTED_VALUE'
  | 'INVALID_CONFIGURATION'
  | 'MISSING_REQUIRED_CONFIG'
  | 'INSUFFICIENT_PERMISSIONS'
  | 'UNAUTHORIZED_ACCESS'
  | 'DATABASE_ERROR'
  | 'CONSTRAINT_VIOLATION'
  | 'AUDIT_LOG_FAILED';

/**
 * Error codes for app settings module
 */
export const APP_SETTINGS_ERROR_CODES: Record<
  AppSettingsErrorCodes,
  ErrorCode
> = {
  // Setting not found errors
  SETTING_NOT_FOUND: {
    code: `${APP_SETTINGS_NAMESPACE}:10000`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Setting not found',
  },

  // System setting protection errors
  SYSTEM_SETTING_DELETE_FORBIDDEN: {
    code: `${APP_SETTINGS_NAMESPACE}:10001`,
    statusCode: HttpStatus.FORBIDDEN,
    message: 'System settings cannot be deleted',
  },
  SYSTEM_SETTING_MODIFICATION_RESTRICTED: {
    code: `${APP_SETTINGS_NAMESPACE}:10002`,
    statusCode: HttpStatus.FORBIDDEN,
    message: 'System setting modification is restricted',
  },

  // Setting key errors
  SETTING_KEY_ALREADY_EXISTS: {
    code: `${APP_SETTINGS_NAMESPACE}:10003`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Setting key already exists',
  },
  INVALID_SETTING_KEY: {
    code: `${APP_SETTINGS_NAMESPACE}:10004`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid setting key format',
  },
  SETTING_KEY_REQUIRED: {
    code: `${APP_SETTINGS_NAMESPACE}:10005`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Setting key is required',
  },

  // Validation errors
  VALIDATION_FAILED: {
    code: `${APP_SETTINGS_NAMESPACE}:10006`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Setting value validation failed',
  },
  INVALID_VALIDATION_SCHEMA: {
    code: `${APP_SETTINGS_NAMESPACE}:10007`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid validation schema',
  },
  SETTING_VALUE_REQUIRED: {
    code: `${APP_SETTINGS_NAMESPACE}:10008`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Setting value is required',
  },

  // Encryption errors
  ENCRYPTION_FAILED: {
    code: `${APP_SETTINGS_NAMESPACE}:10009`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Failed to encrypt setting value',
  },
  DECRYPTION_FAILED: {
    code: `${APP_SETTINGS_NAMESPACE}:10010`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Failed to decrypt setting value',
  },
  ENCRYPTION_KEY_MISSING: {
    code: `${APP_SETTINGS_NAMESPACE}:10011`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Encryption key not configured',
  },
  INVALID_ENCRYPTED_VALUE: {
    code: `${APP_SETTINGS_NAMESPACE}:10012`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid encrypted value format',
  },

  // Configuration errors
  INVALID_CONFIGURATION: {
    code: `${APP_SETTINGS_NAMESPACE}:10013`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Invalid module configuration',
  },
  MISSING_REQUIRED_CONFIG: {
    code: `${APP_SETTINGS_NAMESPACE}:10014`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Required configuration is missing',
  },

  // Permission errors
  INSUFFICIENT_PERMISSIONS: {
    code: `${APP_SETTINGS_NAMESPACE}:10015`,
    statusCode: HttpStatus.FORBIDDEN,
    message: 'Insufficient permissions for this operation',
  },
  UNAUTHORIZED_ACCESS: {
    code: `${APP_SETTINGS_NAMESPACE}:10016`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Unauthorized access to settings',
  },

  // Database errors
  DATABASE_ERROR: {
    code: `${APP_SETTINGS_NAMESPACE}:10017`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Database operation failed',
  },
  CONSTRAINT_VIOLATION: {
    code: `${APP_SETTINGS_NAMESPACE}:10018`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Database constraint violation',
  },

  // Audit errors
  AUDIT_LOG_FAILED: {
    code: `${APP_SETTINGS_NAMESPACE}:10019`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Failed to log audit event',
  },
};
