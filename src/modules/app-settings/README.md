# App Settings Module

A comprehensive configuration management system for NestJS applications that supports both system-protected settings and user-manageable custom settings, with built-in encryption capabilities for sensitive values.

## Features

- **System vs Custom Settings**: Differentiate between protected system settings and user-manageable custom settings
- **Encryption Support**: Built-in AES-256-GCM encryption for sensitive configuration values
- **Validation**: JSON schema-based validation for setting values
- **Audit Logging**: Complete audit trail for all setting modifications
- **RESTful API**: Full CRUD operations via REST endpoints
- **Type Safety**: Full TypeScript support with proper interfaces

## Installation

1. Import the module in your application:

```typescript
import { AppSettingsModule } from './modules/app-settings';

@Module({
  imports: [
    AppSettingsModule,
    // ... other modules
  ],
})
export class AppModule {}
```

2. Configure environment variables (see `.env.example`):

```bash
# Required
APP_SETTINGS_ENCRYPTION_KEY=your-secure-64-character-encryption-key

# Optional (with defaults)
APP_SETTINGS_AUDIT_ENABLED=true
APP_SETTINGS_AUDIT_RETENTION_DAYS=90
APP_SETTINGS_VALIDATION_ENABLED=true
APP_SETTINGS_VALIDATION_STRICT_MODE=false
```

## Configuration

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `APP_SETTINGS_ENCRYPTION_KEY` | Yes | - | 64-character encryption key for sensitive values |
| `APP_SETTINGS_AUDIT_ENABLED` | No | `true` | Enable audit logging |
| `APP_SETTINGS_AUDIT_RETENTION_DAYS` | No | `90` | Days to retain audit logs (1-3650) |
| `APP_SETTINGS_VALIDATION_ENABLED` | No | `true` | Enable setting validation |
| `APP_SETTINGS_VALIDATION_STRICT_MODE` | No | `false` | Enable strict validation mode |

### Generating Encryption Key

For production, generate a secure encryption key:

```typescript
import { ConfigValidationUtil } from './modules/app-settings';

const secureKey = ConfigValidationUtil.generateSecureKey(64);
console.log('Generated key:', secureKey);
```

## Usage

### Service Injection

```typescript
import { AppSettingsService } from './modules/app-settings';

@Injectable()
export class MyService {
  constructor(private readonly settingsService: AppSettingsService) {}

  async getApiUrl(): Promise<string> {
    const setting = await this.settingsService.getByKey('api.base_url');
    return setting.value;
  }
}
```

### REST API Endpoints

- `GET /settings` - List all settings (with filtering)
- `GET /settings/:key` - Get specific setting
- `POST /settings` - Create new custom setting
- `PUT /settings/:key` - Update existing setting
- `DELETE /settings/:key` - Delete custom setting

### Setting Types

#### System Settings
- Cannot be deleted
- Protected from unauthorized modifications
- Marked with `isSystem: true`

#### Custom Settings
- Full CRUD operations allowed
- User-manageable
- Marked with `isSystem: false`

### Encryption

Settings can be automatically encrypted by setting `isEncrypted: true`:

```typescript
await settingsService.create({
  key: 'database.password',
  value: 'secret-password',
  isEncrypted: true,
  description: 'Database connection password'
});
```

### Validation

Settings support JSON schema validation:

```typescript
await settingsService.create({
  key: 'api.timeout',
  value: '5000',
  validationSchema: {
    type: 'number',
    minimum: 1000,
    maximum: 30000
  }
});
```

## API Examples

### Create Setting

```bash
POST /settings
{
  "key": "feature.enabled",
  "value": "true",
  "description": "Enable new feature",
  "category": "features",
  "isEncrypted": false,
  "validationSchema": {
    "type": "boolean"
  }
}
```

### Update Setting

```bash
PUT /settings/feature.enabled
{
  "value": "false",
  "description": "Disable new feature"
}
```

### Get Settings by Category

```bash
GET /settings?category=features
```

## Error Handling

The module uses structured error codes for consistent error handling:

```typescript
import { APP_SETTINGS_ERROR_CODES } from './modules/app-settings';

// Example error codes:
// - SETTING_NOT_FOUND
// - SYSTEM_SETTING_DELETE_FORBIDDEN
// - VALIDATION_FAILED
// - ENCRYPTION_FAILED
```

## Security Considerations

1. **Encryption Key**: Use a strong, randomly generated 64-character key
2. **Key Rotation**: Implement regular encryption key rotation
3. **Access Control**: Ensure proper authentication and authorization
4. **Audit Logging**: Monitor all setting modifications
5. **Environment Variables**: Secure storage of configuration values

## Development

### Running Tests

```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e
```

### Database Migrations

The module automatically creates the required database tables:
- `app_settings` - Main settings storage
- `setting_audits` - Audit log storage

## License

This module is part of the main application and follows the same license terms.