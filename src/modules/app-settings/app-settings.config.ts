import { registerAs } from '@nestjs/config';
import { IAppSettingsConfig } from './interfaces/config.interface';
import { ConfigValidationUtil } from './utils/config-validation.util';

/**
 * Configuration factory for app settings module
 * Registers configuration with NestJS ConfigModule
 */
export const appSettingsConfig = registerAs(
  'appSettings',
  (): IAppSettingsConfig => {
    // Validate required environment variables
    ConfigValidationUtil.validateRequiredEnvVars([
      'APP_SETTINGS_ENCRYPTION_KEY',
    ]);

    // Load and validate encryption configuration
    const encryptionKey = process.env.APP_SETTINGS_ENCRYPTION_KEY!;
    ConfigValidationUtil.validateEncryptionKey(encryptionKey);

    // Load audit configuration with defaults
    const auditEnabled = ConfigValidationUtil.validateBooleanConfig(
      process.env.APP_SETTINGS_AUDIT_ENABLED || 'true',
      'APP_SETTINGS_AUDIT_ENABLED',
    );

    const auditRetentionDays = ConfigValidationUtil.validateNumericConfig(
      process.env.APP_SETTINGS_AUDIT_RETENTION_DAYS || '90',
      'APP_SETTINGS_AUDIT_RETENTION_DAYS',
      1,
      3650, // Max 10 years
    );

    // Load validation configuration with defaults
    const validationEnabled = ConfigValidationUtil.validateBooleanConfig(
      process.env.APP_SETTINGS_VALIDATION_ENABLED || 'true',
      'APP_SETTINGS_VALIDATION_ENABLED',
    );

    const validationStrictMode = ConfigValidationUtil.validateBooleanConfig(
      process.env.APP_SETTINGS_VALIDATION_STRICT_MODE || 'false',
      'APP_SETTINGS_VALIDATION_STRICT_MODE',
    );

    return {
      encryption: {
        key: encryptionKey,
        algorithm: 'aes-256-gcm',
      },
      audit: {
        enabled: auditEnabled,
        retentionDays: auditRetentionDays,
      },
      validation: {
        enabled: validationEnabled,
        strictMode: validationStrictMode,
      },
    };
  },
);

/**
 * Default configuration values for development/testing
 */
export const defaultAppSettingsConfig: IAppSettingsConfig = {
  encryption: {
    key: ConfigValidationUtil.generateSecureKey(64),
    algorithm: 'aes-256-gcm',
  },
  audit: {
    enabled: true,
    retentionDays: 90,
  },
  validation: {
    enabled: true,
    strictMode: false,
  },
};

/**
 * Environment variable names used by the app settings module
 */
export const APP_SETTINGS_ENV_VARS = {
  ENCRYPTION_KEY: 'APP_SETTINGS_ENCRYPTION_KEY',
  AUDIT_ENABLED: 'APP_SETTINGS_AUDIT_ENABLED',
  AUDIT_RETENTION_DAYS: 'APP_SETTINGS_AUDIT_RETENTION_DAYS',
  VALIDATION_ENABLED: 'APP_SETTINGS_VALIDATION_ENABLED',
  VALIDATION_STRICT_MODE: 'APP_SETTINGS_VALIDATION_STRICT_MODE',
} as const;
