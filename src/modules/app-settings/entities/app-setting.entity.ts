import { Column, Entity, Index } from 'typeorm';
import { CustomBaseEntity } from '../../../common/entities/base.entity';

@Entity('app_settings')
@Index(['category'])
@Index(['isSystem'])
@Index(['key', 'category'])
export class AppSettingEntity extends CustomBaseEntity {
  @Column({ unique: true, length: 255 })
  key: string;

  @Column({ type: 'text' })
  value: string;

  @Column({ nullable: true, length: 500 })
  description?: string;

  @Column({ nullable: true, length: 100 })
  category?: string;

  @Column({ default: false, name: 'is_system' })
  isSystem: boolean;

  @Column({ default: false, name: 'is_encrypted' })
  isEncrypted: boolean;

  @Column({ type: 'json', nullable: true, name: 'validation_schema' })
  validationSchema?: object;

  @Column({ nullable: true, length: 255, name: 'created_by' })
  createdBy?: string;

  @Column({ nullable: true, length: 255, name: 'updated_by' })
  updatedBy?: string;
}
