import { Column, Entity, Index } from 'typeorm';
import { CustomBaseEntity } from '../../../common/entities/base.entity';
import { AuditAction } from '../interfaces/audit.interface';

@Entity('setting_audits')
@Index(['settingKey'])
@Index(['userId'])
@Index(['action'])
@Index(['settingKey', 'createdAt'])
export class SettingAuditEntity extends CustomBaseEntity {
  @Column({ length: 255, name: 'setting_key' })
  settingKey: string;

  @Column({
    type: 'enum',
    enum: ['CREATE', 'UPDATE', 'DELETE'],
    enumName: 'audit_action_enum',
  })
  action: AuditAction;

  @Column({ type: 'text', nullable: true, name: 'old_value' })
  oldValue?: string;

  @Column({ type: 'text', nullable: true, name: 'new_value' })
  newValue?: string;

  @Column({ length: 255, name: 'user_id' })
  userId: string;

  @Column({ type: 'json', nullable: true })
  metadata?: object;
}
