import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsBoolean,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class UpdateSettingDto {
  @Expose()
  @ApiProperty({
    example: '#e74c3c',
    description: 'The new setting value',
    required: false,
  })
  @IsOptional()
  @IsString()
  value?: string;

  @Expose()
  @ApiProperty({
    example: 'Updated primary color for the application theme',
    description: 'Updated description of the setting',
    maxLength: 500,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @Expose()
  @ApiProperty({
    example: 'ui',
    description: 'Updated category to group related settings',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;

  @Expose()
  @ApiProperty({
    example: true,
    description: 'Whether the setting value should be encrypted',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isEncrypted?: boolean;

  @Expose()
  @ApiProperty({
    example: {
      type: 'string',
      pattern: '^#[0-9a-fA-F]{6}$',
      description: 'Hex color code',
    },
    description: 'Updated JSON schema for validating the setting value',
    required: false,
  })
  @IsOptional()
  @IsObject()
  validationSchema?: object;
}
