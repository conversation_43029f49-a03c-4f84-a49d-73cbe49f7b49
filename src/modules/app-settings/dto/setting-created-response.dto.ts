import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { SettingResponseDto } from './setting-response.dto';

export class SettingCreatedResponseDto {
  @Expose()
  @ApiProperty({
    type: SettingResponseDto,
    description: 'The created setting',
  })
  data: SettingResponseDto;

  @Expose()
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  success: boolean;

  @Expose()
  @ApiProperty({
    example: 'Setting created successfully',
    description: 'Success message',
  })
  message: string;

  constructor(
    data: SettingResponseDto,
    message: string = 'Setting created successfully',
  ) {
    this.data = data;
    this.success = true;
    this.message = message;
  }
}
