import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class SettingDeletedResponseDto {
  @Expose()
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  success: boolean;

  @Expose()
  @ApiProperty({
    example: 'Setting deleted successfully',
    description: 'Success message',
  })
  message: string;

  @Expose()
  @ApiProperty({
    example: 'app.theme.primary_color',
    description: 'Key of the deleted setting',
  })
  deletedKey: string;

  constructor(
    deletedKey: string,
    message: string = 'Setting deleted successfully',
  ) {
    this.success = true;
    this.message = message;
    this.deletedKey = deletedKey;
  }
}
