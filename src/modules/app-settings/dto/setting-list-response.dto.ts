import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Pagination } from '../../../common/dto/abstract.dto';
import { SettingResponseDto } from './setting-response.dto';

export class SettingListResponseDto {
  @Expose()
  @ApiProperty({
    type: [SettingResponseDto],
    description: 'Array of settings',
  })
  data: SettingResponseDto[];

  @Expose()
  @ApiProperty({
    type: Pagination,
    description: 'Pagination information',
  })
  pagination: Pagination;

  @Expose()
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  success: boolean;

  constructor(data: SettingResponseDto[], pagination: Pagination) {
    this.data = data;
    this.pagination = pagination;
    this.success = true;
  }
}
