import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { SettingResponseDto } from './setting-response.dto';

export class SettingUpdatedResponseDto {
  @Expose()
  @ApiProperty({
    type: SettingResponseDto,
    description: 'The updated setting',
  })
  data: SettingResponseDto;

  @Expose()
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  success: boolean;

  @Expose()
  @ApiProperty({
    example: 'Setting updated successfully',
    description: 'Success message',
  })
  message: string;

  constructor(
    data: SettingResponseDto,
    message: string = 'Setting updated successfully',
  ) {
    this.data = data;
    this.success = true;
    this.message = message;
  }
}
