import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import {
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class QuerySettingsDto {
  @Expose()
  @ApiProperty({
    example: 'theme',
    description: 'Filter settings by category',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;

  @Expose()
  @ApiProperty({
    example: 'color',
    description: 'Search settings by key (partial match)',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @Expose()
  @ApiProperty({
    example: false,
    description: 'Filter by system settings only',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isSystem?: boolean;

  @Expose()
  @ApiProperty({
    example: true,
    description: 'Filter by encrypted settings only',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isEncrypted?: boolean;

  @Expose()
  @ApiProperty({
    example: 1,
    description: 'Page number for pagination',
    minimum: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @Expose()
  @ApiProperty({
    example: 20,
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}
