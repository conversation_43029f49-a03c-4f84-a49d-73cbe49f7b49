import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';

export class CreateSettingDto {
  @Expose()
  @ApiProperty({
    example: 'app.theme.primary_color',
    description: 'Unique key for the setting',
    maxLength: 255,
    minLength: 1,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(255)
  @Matches(/^[a-zA-Z0-9._-]+$/, {
    message:
      'Key can only contain letters, numbers, dots, underscores, and hyphens',
  })
  key: string;

  @Expose()
  @ApiProperty({
    example: '#3498db',
    description: 'The setting value (will be stored as string)',
  })
  @IsString()
  @IsNotEmpty()
  value: string;

  @Expose()
  @ApiProperty({
    example: 'Primary color for the application theme',
    description: 'Optional description of the setting',
    maxLength: 500,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @Expose()
  @ApiProperty({
    example: 'theme',
    description: 'Category to group related settings',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;

  @Expose()
  @ApiProperty({
    example: false,
    description: 'Whether the setting value should be encrypted',
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isEncrypted?: boolean;

  @Expose()
  @ApiProperty({
    example: {
      type: 'string',
      pattern: '^#[0-9a-fA-F]{6}$',
    },
    description: 'JSON schema for validating the setting value',
    required: false,
  })
  @IsOptional()
  @IsObject()
  validationSchema?: object;
}
