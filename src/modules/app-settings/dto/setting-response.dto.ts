import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class SettingResponseDto {
  @Expose()
  @ApiProperty({
    example: 'app.theme.primary_color',
    description: 'Unique key for the setting',
  })
  key: string;

  @Expose()
  @ApiProperty({
    example: '#3498db',
    description: 'The setting value',
  })
  value: string;

  @Expose()
  @ApiProperty({
    example: 'Primary color for the application theme',
    description: 'Description of the setting',
    nullable: true,
  })
  description?: string;

  @Expose()
  @ApiProperty({
    example: 'theme',
    description: 'Category of the setting',
    nullable: true,
  })
  category?: string;

  @Expose()
  @ApiProperty({
    example: false,
    description: 'Whether this is a system-managed setting',
  })
  isSystem: boolean;

  @Expose()
  @ApiProperty({
    example: false,
    description: 'Whether the setting value is encrypted',
  })
  isEncrypted: boolean;

  @Expose()
  @ApiProperty({
    example: {
      type: 'string',
      pattern: '^#[0-9a-fA-F]{6}$',
    },
    description: 'JSON schema for validating the setting value',
    nullable: true,
  })
  validationSchema?: object;

  @Expose()
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User who created the setting',
    nullable: true,
  })
  createdBy?: string;

  @Expose()
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User who last updated the setting',
    nullable: true,
  })
  updatedBy?: string;

  @Expose()
  @ApiProperty({
    example: '2024-01-15T10:30:00Z',
    description: 'When the setting was created',
  })
  createdAt: Date;

  @Expose()
  @ApiProperty({
    example: '2024-01-16T14:20:00Z',
    description: 'When the setting was last updated',
  })
  updatedAt: Date;

  @Expose()
  @ApiProperty({
    example: 'uuid-v4-string',
    description: 'Unique identifier for the setting',
  })
  id: string;
}
