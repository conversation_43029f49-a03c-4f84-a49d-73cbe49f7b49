import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

// Configuration
import { appSettingsConfig } from './app-settings.config';

// Entities
import { AppSettingEntity } from './entities/app-setting.entity';
import { SettingAuditEntity } from './entities/setting-audit.entity';

// Controllers
import { AppSettingsController } from './controllers';

// Services
import {
  AppSettingsConfigService,
  AppSettingsService,
  AuditEventService,
  EncryptionService,
  SchemaManagementService,
  SettingValidationService,
} from './services';

@Module({
  imports: [
    TypeOrmModule.forFeature([AppSettingEntity, SettingAuditEntity]),
    ConfigModule.forFeature(appSettingsConfig),
  ],
  controllers: [AppSettingsController],
  providers: [
    AppSettingsService,
    EncryptionService,
    SettingValidationService,
    SchemaManagementService,
    AuditEventService,
    AppSettingsConfigService,
  ],
  exports: [
    AppSettingsService,
    EncryptionService,
    SettingValidationService,
    AppSettingsConfigService,
  ],
})
export class AppSettingsModule {}
