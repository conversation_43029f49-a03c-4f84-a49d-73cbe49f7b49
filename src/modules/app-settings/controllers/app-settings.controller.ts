import { toDto } from '@app/common/dto/to-dto';
import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { User } from '@app/modules/auth/decorators/user.decorator';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';
import { CreateSettingDto } from '../dto/create-setting.dto';
import { QuerySettingsDto } from '../dto/query-settings.dto';
import { SettingCreatedResponseDto } from '../dto/setting-created-response.dto';
import { SettingDeletedResponseDto } from '../dto/setting-deleted-response.dto';
import { SettingListResponseDto } from '../dto/setting-list-response.dto';
import { SettingResponseDto } from '../dto/setting-response.dto';
import { SettingUpdatedResponseDto } from '../dto/setting-updated-response.dto';
import { UpdateSettingDto } from '../dto/update-setting.dto';
import { AppSettingsService } from '../services/app-settings.service';

@ApiTags('App Settings')
@Controller({ path: 'settings', version: '1' })
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AppSettingsController {
  constructor(private readonly appSettingsService: AppSettingsService) {}

  /**
   * Get all settings with optional filtering
   */
  @Get()
  @ApiOperation({
    summary: 'Get all settings',
    description:
      'Retrieve all settings with optional filtering by category, type, and search parameters',
  })
  @ApiQuery({ type: QuerySettingsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Settings retrieved successfully',
    type: SettingListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
  })
  async getAllSettings(
    @Query() queryDto: QuerySettingsDto,
  ): Promise<SettingListResponseDto> {
    const {
      page = 1,
      limit = 20,
      category,
      search,
      isSystem,
      isEncrypted,
    } = queryDto;

    // Build filter object
    const filter: any = {};
    if (category) filter.category = category;
    if (isSystem !== undefined) filter.isSystem = isSystem;
    if (isEncrypted !== undefined) filter.isEncrypted = isEncrypted;

    // Get all settings with filter
    let settings = await this.appSettingsService.findMany(filter);

    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      settings = settings.filter(
        (setting) =>
          setting.key.toLowerCase().includes(searchLower) ||
          (setting.description &&
            setting.description.toLowerCase().includes(searchLower)),
      );
    }

    // Apply pagination
    const total = settings.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSettings = settings.slice(startIndex, endIndex);

    // Convert to DTOs
    const settingDtos = paginatedSettings.map((setting) =>
      toDto(SettingResponseDto, setting),
    );

    // Create pagination info
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: endIndex < total,
      hasPrev: page > 1,
    };

    return new SettingListResponseDto(settingDtos, pagination);
  }

  /**
   * Get a specific setting by key
   */
  @Get(':key')
  @ApiOperation({
    summary: 'Get setting by key',
    description: 'Retrieve a specific setting by its unique key',
  })
  @ApiParam({
    name: 'key',
    description: 'Unique setting key',
    example: 'app.theme.primary_color',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Setting retrieved successfully',
    type: SettingResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Setting not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async getSettingByKey(
    @Param('key') key: string,
  ): Promise<SettingResponseDto> {
    const setting = await this.appSettingsService.findByKey(key);

    if (!setting) {
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.SETTING_NOT_FOUND, {
        description: `Setting with key '${key}' not found`,
      });
    }

    return toDto(setting, SettingResponseDto);
  }

  /**
   * Get settings by category
   */
  @Get('category/:category')
  @ApiOperation({
    summary: 'Get settings by category',
    description: 'Retrieve all settings belonging to a specific category',
  })
  @ApiParam({
    name: 'category',
    description: 'Setting category',
    example: 'theme',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 20,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Settings retrieved successfully',
    type: SettingListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async getSettingsByCategory(
    @Param('category') category: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
  ): Promise<SettingListResponseDto> {
    const settings = await this.appSettingsService.findMany({ category });

    // Apply pagination
    const total = settings.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSettings = settings.slice(startIndex, endIndex);

    // Convert to DTOs
    const settingDtos = paginatedSettings.map((setting) =>
      toDto(setting, SettingResponseDto),
    );

    // Create pagination info
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: endIndex < total,
      hasPrev: page > 1,
    };

    return new SettingListResponseDto(settingDtos, pagination);
  }

  /**
   * Create a new custom setting
   */
  @Post()
  @ApiOperation({
    summary: 'Create new setting',
    description:
      'Create a new custom setting (system settings cannot be created via API)',
  })
  @ApiBody({ type: CreateSettingDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Setting created successfully',
    type: SettingCreatedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Setting key already exists',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  @HttpCode(HttpStatus.CREATED)
  async createSetting(
    @Body() createSettingDto: CreateSettingDto,
    @User() user: JwtPayloadType,
  ): Promise<SettingCreatedResponseDto> {
    // Ensure this is not a system setting (only custom settings can be created via API)
    const createData = {
      ...createSettingDto,
      isSystem: false, // Force to false for API-created settings
      createdBy: user.email,
    };

    const setting = await this.appSettingsService.create(createData);

    return toDto(setting, SettingCreatedResponseDto);
  }

  /**
   * Update an existing setting (PUT - full update)
   */
  @Put(':key')
  @ApiOperation({
    summary: 'Update setting (full update)',
    description:
      'Update an existing setting with full replacement of updatable fields',
  })
  @ApiParam({
    name: 'key',
    description: 'Unique setting key',
    example: 'app.theme.primary_color',
  })
  @ApiBody({ type: UpdateSettingDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Setting updated successfully',
    type: SettingUpdatedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation failed',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Setting not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'System setting modification restricted',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async updateSetting(
    @Param('key') key: string,
    @Body() updateSettingDto: UpdateSettingDto,
    @User() user: JwtPayloadType,
  ): Promise<SettingUpdatedResponseDto> {
    const updateData = {
      ...updateSettingDto,
      updatedBy: user.email,
    };

    const updatedSetting = await this.appSettingsService.update(
      key,
      updateData,
    );
    const settingDto = toDto(updatedSetting, SettingResponseDto);

    return new SettingUpdatedResponseDto(settingDto);
  }

  /**
   * Update an existing setting (PATCH - partial update)
   */
  @Patch(':key')
  @ApiOperation({
    summary: 'Update setting (partial update)',
    description:
      'Partially update an existing setting with only provided fields',
  })
  @ApiParam({
    name: 'key',
    description: 'Unique setting key',
    example: 'app.theme.primary_color',
  })
  @ApiBody({ type: UpdateSettingDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Setting updated successfully',
    type: SettingUpdatedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation failed',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Setting not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'System setting modification restricted',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async patchSetting(
    @Param('key') key: string,
    @Body() updateSettingDto: UpdateSettingDto,
    @User() user: JwtPayloadType,
  ): Promise<SettingUpdatedResponseDto> {
    const updateData = {
      ...updateSettingDto,
      updatedBy: user.email,
    };

    const updatedSetting = await this.appSettingsService.update(
      key,
      updateData,
    );
    const settingDto = toDto(updatedSetting, SettingResponseDto);

    return new SettingUpdatedResponseDto(settingDto);
  }

  /**
   * Delete a setting (only custom settings can be deleted)
   */
  @Delete(':key')
  @ApiOperation({
    summary: 'Delete setting',
    description: 'Delete a custom setting (system settings cannot be deleted)',
  })
  @ApiParam({
    name: 'key',
    description: 'Unique setting key',
    example: 'app.theme.primary_color',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Setting deleted successfully',
    type: SettingDeletedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Setting not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'System settings cannot be deleted',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async deleteSetting(
    @Param('key') key: string,
    @User() user: JwtPayloadType,
  ): Promise<SettingDeletedResponseDto> {
    await this.appSettingsService.delete(key, user.email);

    return new SettingDeletedResponseDto(key);
  }

  /**
   * Search settings by key pattern
   */
  @Get('search/:pattern')
  @ApiOperation({
    summary: 'Search settings by key pattern',
    description:
      'Search for settings where the key matches the provided pattern (case-insensitive)',
  })
  @ApiParam({
    name: 'pattern',
    description: 'Search pattern for setting keys',
    example: 'theme',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
    description: 'Filter by category',
    example: 'ui',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Settings found successfully',
    type: SettingListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async searchSettings(
    @Param('pattern') pattern: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('category') category?: string,
  ): Promise<SettingListResponseDto> {
    // Build filter object
    const filter: any = {};
    if (category) filter.category = category;

    // Get all settings with filter
    let settings = await this.appSettingsService.findMany(filter);

    // Apply search pattern
    const searchLower = pattern.toLowerCase();
    settings = settings.filter(
      (setting) =>
        setting.key.toLowerCase().includes(searchLower) ||
        (setting.description &&
          setting.description.toLowerCase().includes(searchLower)),
    );

    // Apply pagination
    const total = settings.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSettings = settings.slice(startIndex, endIndex);

    // Convert to DTOs
    const settingDtos = paginatedSettings.map((setting) =>
      toDto(setting, SettingResponseDto),
    );

    // Create pagination info
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: endIndex < total,
      hasPrev: page > 1,
    };

    return new SettingListResponseDto(settingDtos, pagination);
  }

  /**
   * Get system settings only
   */
  @Get('system/all')
  @ApiOperation({
    summary: 'Get all system settings',
    description: 'Retrieve all system-managed settings',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
    description: 'Filter by category',
    example: 'security',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'System settings retrieved successfully',
    type: SettingListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async getSystemSettings(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('category') category?: string,
  ): Promise<SettingListResponseDto> {
    // Build filter for system settings
    const filter: any = { isSystem: true };
    if (category) filter.category = category;

    const settings = await this.appSettingsService.findMany(filter);

    // Apply pagination
    const total = settings.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSettings = settings.slice(startIndex, endIndex);

    // Convert to DTOs
    const settingDtos = paginatedSettings.map((setting) =>
      toDto(setting, SettingResponseDto),
    );

    // Create pagination info
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: endIndex < total,
      hasPrev: page > 1,
    };

    return new SettingListResponseDto(settingDtos, pagination);
  }

  /**
   * Get custom settings only
   */
  @Get('custom/all')
  @ApiOperation({
    summary: 'Get all custom settings',
    description: 'Retrieve all user-created custom settings',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
    description: 'Filter by category',
    example: 'user_preferences',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Custom settings retrieved successfully',
    type: SettingListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async getCustomSettings(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('category') category?: string,
  ): Promise<SettingListResponseDto> {
    // Build filter for custom settings
    const filter: any = { isSystem: false };
    if (category) filter.category = category;

    const settings = await this.appSettingsService.findMany(filter);

    // Apply pagination
    const total = settings.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSettings = settings.slice(startIndex, endIndex);

    // Convert to DTOs
    const settingDtos = paginatedSettings.map((setting) =>
      toDto(setting, SettingResponseDto),
    );

    // Create pagination info
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: endIndex < total,
      hasPrev: page > 1,
    };

    return new SettingListResponseDto(settingDtos, pagination);
  }

  /**
   * Get encrypted settings only
   */
  @Get('encrypted/all')
  @ApiOperation({
    summary: 'Get all encrypted settings',
    description: 'Retrieve all settings that have encrypted values',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
    description: 'Filter by category',
    example: 'security',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Encrypted settings retrieved successfully',
    type: SettingListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access',
  })
  async getEncryptedSettings(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('category') category?: string,
  ): Promise<SettingListResponseDto> {
    // Build filter for encrypted settings
    const filter: any = { isEncrypted: true };
    if (category) filter.category = category;

    const settings = await this.appSettingsService.findMany(filter);

    // Apply pagination
    const total = settings.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSettings = settings.slice(startIndex, endIndex);

    // Convert to DTOs
    const settingDtos = paginatedSettings.map((setting) =>
      toDto(setting, SettingResponseDto),
    );

    // Create pagination info
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: endIndex < total,
      hasPrev: page > 1,
    };

    return new SettingListResponseDto(settingDtos, pagination);
  }
}
