/**
 * Enum for setting types
 */
export enum SettingType {
  SYSTEM = 'system',
  CUSTOM = 'custom',
}

/**
 * Enum for setting categories
 */
export enum SettingCategory {
  GENERAL = 'general',
  SECURITY = 'security',
  NOTIFICATION = 'notification',
  INTEGRATION = 'integration',
  PERFORMANCE = 'performance',
  UI = 'ui',
  API = 'api',
  DATABASE = 'database',
  CACHE = 'cache',
  LOGGING = 'logging',
}

/**
 * Enum for validation types
 */
export enum ValidationType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
}
