import { HttpErrorException } from '../../../common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '../app-settings.error-codes';

/**
 * Utility functions for validating app settings configuration
 */
export class ConfigValidationUtil {
  /**
   * Validate that required environment variables are present
   * @param requiredVars - Array of required environment variable names
   * @throws HttpErrorException if any required variables are missing
   */
  static validateRequiredEnvVars(requiredVars: string[]): void {
    const missingVars: string[] = [];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    }

    if (missingVars.length > 0) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.MISSING_REQUIRED_CONFIG,
      );
    }
  }

  /**
   * Validate encryption key strength
   * @param key - The encryption key to validate
   * @throws HttpErrorException if the key is not strong enough
   */
  static validateEncryptionKey(key: string): void {
    if (!key) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.ENCRYPTION_KEY_MISSING,
      );
    }

    if (key.length < 32) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    // Check for common weak patterns
    if (key === key.toLowerCase() || key === key.toUpperCase()) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    if (!/\d/.test(key)) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }
  }

  /**
   * Validate numeric configuration values
   * @param value - The value to validate
   * @param name - The name of the configuration for error messages
   * @param min - Minimum allowed value (optional)
   * @param max - Maximum allowed value (optional)
   * @returns The parsed number
   * @throws HttpErrorException if the value is invalid
   */
  static validateNumericConfig(
    value: string | number,
    name: string,
    min?: number,
    max?: number,
  ): number {
    const numValue = typeof value === 'string' ? parseInt(value, 10) : value;

    if (isNaN(numValue)) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    if (min !== undefined && numValue < min) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    if (max !== undefined && numValue > max) {
      throw new HttpErrorException(
        APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
      );
    }

    return numValue;
  }

  /**
   * Validate boolean configuration values
   * @param value - The value to validate
   * @param name - The name of the configuration for error messages
   * @returns The parsed boolean
   * @throws HttpErrorException if the value is invalid
   */
  static validateBooleanConfig(value: string | boolean, name: string): boolean {
    if (typeof value === 'boolean') {
      return value;
    }

    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes') {
        return true;
      }
      if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no') {
        return false;
      }
    }

    throw new HttpErrorException(
      APP_SETTINGS_ERROR_CODES.INVALID_CONFIGURATION,
    );
  }

  /**
   * Generate a secure random encryption key
   * @param length - The length of the key to generate (default: 64)
   * @returns A secure random key
   */
  static generateSecureKey(length: number = 64): string {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }
}
