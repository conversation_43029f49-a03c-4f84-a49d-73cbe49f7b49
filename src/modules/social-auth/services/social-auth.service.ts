import { toDto } from '@app/common/dto/to-dto';
import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { base64Decode, base64Encode } from '@app/common/utils/crypto';
import { AuthConfigType } from '@app/modules/auth/auth.config';
import { AUTH_ERROR_CODES } from '@app/modules/auth/auth.error-code';
import { LoginDto } from '@app/modules/auth/dto/login.dto';
import { SessionEntity } from '@app/modules/auth/entities/session.entity';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { USER_ERROR_CODES } from '@app/modules/user/user.error-code';
import { Injectable } from '@nestjs/common';
import { randomStringGenerator } from '@nestjs/common/utils/random-string-generator.util';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Repository } from 'typeorm';
import { SocialEntity } from '../entities/social.entity';
import { SocialProviderEnum } from '../enums/social-provider.enum';
import {
  SocialAuthException,
  SocialOAuthCallbackException,
  SocialProfileDataException,
  UnsupportedSocialProviderException,
} from '../exceptions/social-auth.exception';
import { SocialAuthConfigType } from '../interfaces/social-auth-config.interface';
import { SOCIAL_AUTH_ERROR_CODES } from '../social-auth.error-codes';

@Injectable()
export class SocialAuthService {
  constructor(
    private readonly configService: ConfigService<{
      auth: AuthConfigType;
      socialAuth: SocialAuthConfigType;
    }>,
    @InjectRepository(SocialEntity)
    private readonly socialRepo: Repository<SocialEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(SessionEntity)
    private readonly sessionRepo: Repository<SessionEntity>,
    private readonly authService: AuthService,
  ) {}

  /**
   * Save or update social user information and create a session
   * @param payload Social user data from OAuth provider
   * @returns Authorization code for subsequent login
   * @throws {SocialProfileDataException} When profile data is incomplete
   * @throws {SocialAuthException} When social user creation fails
   */
  async saveSocialUser(payload: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    picture: string;
    accessToken: string;
    refreshToken: string;
    provider: SocialProviderEnum;
  }) {
    try {
      // Validate required profile data
      this.validateProfileData(payload);

      // Create or update social provider record
      const socialProvider = this.socialRepo.create({
        provider: payload.provider,
        providerId: payload.id,
        email: payload.email,
        firstName: payload.firstName,
        lastName: payload.lastName,
        picture: payload.picture,
        accessToken: payload.accessToken,
        refreshToken: payload.refreshToken,
      });

      await this.socialRepo.upsert(socialProvider, {
        conflictPaths: ['providerId', 'provider'],
      });

      // Find or create user
      let user = await this.userRepo.findOne({
        where: { email: payload.email },
      });

      if (!user) {
        user = this.userRepo.create({
          email: payload.email,
          firstName: payload.firstName,
          lastName: payload.lastName,
        });
        await this.userRepo.save(user);
      }

      // Create session using core auth service
      const session = await this.authService.createSession(user.id, '', '');

      // Generate authorization code
      const authorizationCode = base64Encode(session.id);

      return {
        authorizationCode,
      };
    } catch (error) {
      if (error instanceof SocialProfileDataException) {
        throw error;
      }

      throw new SocialAuthException(
        SOCIAL_AUTH_ERROR_CODES.PROVIDER_AUTHENTICATION_FAILED,
        `Failed to save social user for provider ${payload.provider}: ${error.message}`,
      );
    }
  }

  /**
   * Login user with authorization code from social OAuth flow
   * @param authorizationCode Base64 encoded session ID
   * @param ipAddress Client IP address
   * @param userAgent Client user agent
   * @returns Login response with tokens and user data
   * @throws {SocialAuthException} When authorization code is invalid or expired
   */
  async loginWithCode(
    authorizationCode: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<LoginDto> {
    try {
      // Validate authorization code format
      if (!authorizationCode || typeof authorizationCode !== 'string') {
        throw new SocialAuthException(
          SOCIAL_AUTH_ERROR_CODES.INVALID_AUTHORIZATION_CODE,
        );
      }

      // Decode session ID from authorization code
      let sessionId: string;
      try {
        sessionId = base64Decode(authorizationCode);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        throw new SocialAuthException(
          SOCIAL_AUTH_ERROR_CODES.INVALID_AUTHORIZATION_CODE,
          'Failed to decode authorization code',
        );
      }

      // Find session
      const session = await this.sessionRepo.findOne({
        where: { id: sessionId },
      });

      if (!session) {
        throw new SocialAuthException(
          SOCIAL_AUTH_ERROR_CODES.INVALID_AUTHORIZATION_CODE,
        );
      }

      // Check session expiration
      const sessionExpires = this.configService.getOrThrow(
        'auth.sessionExpires',
        {
          infer: true,
        },
      );

      if (session.isExpired(sessionExpires)) {
        throw new HttpErrorException(AUTH_ERROR_CODES.SESSION_EXPIRED);
      }

      // Validate user exists
      if (!session.userId) {
        throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
      }

      const user = await this.userRepo.findOne({
        where: { id: session.userId },
      });

      if (!user) {
        throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
      }

      // Update session with client information
      session.ipAddress = ipAddress;
      session.userAgent = userAgent;
      await this.sessionRepo.save(session);

      // Generate hash for token generation
      const hash = crypto
        .createHash('sha256')
        .update(randomStringGenerator())
        .digest('hex');

      // Generate tokens using core auth service
      const { accessToken, refreshToken, tokenExpires } =
        await this.authService.getTokensData({
          userId: user.id,
          sessionId: session.id,
          hash: hash,
        });

      return toDto(LoginDto, {
        refreshToken,
        accessToken,
        tokenExpires,
        user,
      });
    } catch (error) {
      if (
        error instanceof HttpErrorException ||
        error instanceof SocialAuthException
      ) {
        throw error;
      }

      throw new SocialAuthException(
        SOCIAL_AUTH_ERROR_CODES.PROVIDER_AUTHENTICATION_FAILED,
        `Login with authorization code failed: ${error.message}`,
      );
    }
  }

  /**
   * Link social account to existing user
   * @param userId Existing user ID
   * @param socialData Social provider data
   */
  async linkSocialAccount(
    userId: string,
    socialData: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      picture: string;
      accessToken: string;
      refreshToken: string;
      provider: SocialProviderEnum;
    },
  ): Promise<void> {
    // Verify user exists
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    // Check if social account is already linked to another user
    const existingSocial = await this.socialRepo.findOne({
      where: {
        provider: socialData.provider,
        providerId: socialData.id,
      },
    });

    if (existingSocial) {
      // Check if it's linked to a different user via email
      const existingUser = await this.userRepo.findOne({
        where: { email: socialData.email },
      });

      if (existingUser && existingUser.id !== userId) {
        throw new SocialAuthException(
          SOCIAL_AUTH_ERROR_CODES.SOCIAL_ACCOUNT_ALREADY_LINKED,
        );
      }
    }

    // Create or update social provider record
    const socialProvider = this.socialRepo.create({
      provider: socialData.provider,
      providerId: socialData.id,
      email: socialData.email,
      firstName: socialData.firstName,
      lastName: socialData.lastName,
      picture: socialData.picture,
      accessToken: socialData.accessToken,
      refreshToken: socialData.refreshToken,
    });

    await this.socialRepo.upsert(socialProvider, {
      conflictPaths: ['providerId', 'provider'],
    });
  }

  /**
   * Get social accounts linked to a user
   * @param userId User ID
   * @returns Array of linked social accounts
   */
  async getUserSocialAccounts(userId: string): Promise<SocialEntity[]> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    return this.socialRepo.find({
      where: { email: user.email },
      select: [
        'id',
        'provider',
        'providerId',
        'email',
        'firstName',
        'lastName',
        'picture',
        'createdAt',
        'updatedAt',
      ], // Exclude sensitive tokens
    });
  }

  /**
   * Unlink social account from user
   * @param userId User ID
   * @param provider Social provider to unlink
   */
  async unlinkSocialAccount(
    userId: string,
    provider: SocialProviderEnum,
  ): Promise<void> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    const socialAccount = await this.socialRepo.findOne({
      where: {
        email: user.email,
        provider: provider,
      },
    });

    if (!socialAccount) {
      throw new SocialAuthException(
        SOCIAL_AUTH_ERROR_CODES.SOCIAL_ACCOUNT_NOT_FOUND,
      );
    }

    await this.socialRepo.remove(socialAccount);
  }

  /**
   * Handle OAuth callback and process user data
   * @param provider Social provider
   * @param profile OAuth profile data
   * @param accessToken OAuth access token
   * @param refreshToken OAuth refresh token
   * @returns Authorization code for login
   * @throws {SocialOAuthCallbackException} When OAuth callback fails
   * @throws {SocialProfileDataException} When profile data is incomplete
   */
  async handleOAuthCallback(
    provider: SocialProviderEnum,
    profile: any,
    accessToken: string,
    refreshToken?: string,
  ) {
    try {
      // Validate OAuth callback data
      if (!profile) {
        throw new SocialOAuthCallbackException(
          provider,
          'No profile data received',
        );
      }

      if (!accessToken) {
        throw new SocialOAuthCallbackException(
          provider,
          'No access token received',
        );
      }

      // Extract user data from provider profile
      const userData = this.extractUserDataFromProfile(provider, profile);

      return this.saveSocialUser({
        id: userData.id,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        picture: userData.picture,
        accessToken,
        refreshToken: refreshToken || '',
        provider,
      });
    } catch (error) {
      if (
        error instanceof SocialOAuthCallbackException ||
        error instanceof SocialProfileDataException ||
        error instanceof SocialAuthException
      ) {
        throw error;
      }

      throw new SocialOAuthCallbackException(
        provider,
        'OAuth callback processing failed',
        error.message,
      );
    }
  }

  /**
   * Extract standardized user data from provider-specific profile
   * @param provider Social provider
   * @param profile Provider-specific profile data
   * @returns Standardized user data
   * @throws {UnsupportedSocialProviderException} When provider is not supported
   * @throws {SocialProfileDataException} When profile data is incomplete
   */
  private extractUserDataFromProfile(
    provider: SocialProviderEnum,
    profile: any,
  ): {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    picture: string;
  } {
    let userData: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      picture: string;
    };

    switch (provider) {
      case SocialProviderEnum.GOOGLE:
        userData = {
          id: profile.id,
          email: profile.emails?.[0]?.value || '',
          firstName: profile.name?.givenName || '',
          lastName: profile.name?.familyName || '',
          picture: profile.photos?.[0]?.value || '',
        };
        break;

      case SocialProviderEnum.FACEBOOK:
        userData = {
          id: profile.id,
          email: profile.emails?.[0]?.value || '',
          firstName: profile.name?.givenName || '',
          lastName: profile.name?.familyName || '',
          picture: profile.photos?.[0]?.value || '',
        };
        break;

      case SocialProviderEnum.MICROSOFT:
        userData = {
          id: profile.id,
          email: profile.emails?.[0]?.value || profile.upn || '',
          firstName: profile.name?.givenName || '',
          lastName: profile.name?.familyName || '',
          picture: profile.photos?.[0]?.value || '',
        };
        break;

      default:
        throw new UnsupportedSocialProviderException(provider, [
          SocialProviderEnum.GOOGLE,
          SocialProviderEnum.FACEBOOK,
          SocialProviderEnum.MICROSOFT,
        ]);
    }

    // Validate extracted data
    this.validateProfileData({ ...userData, provider });

    return userData;
  }

  /**
   * Validate profile data completeness
   * @param payload Profile data to validate
   * @throws {SocialProfileDataException} When required profile data is missing
   */
  private validateProfileData(payload: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    provider: SocialProviderEnum;
  }): void {
    const missing: string[] = [];

    if (!payload.id) missing.push('id');
    if (!payload.email) missing.push('email');
    if (!payload.firstName) missing.push('firstName');
    if (!payload.lastName) missing.push('lastName');

    if (missing.length > 0) {
      throw new SocialProfileDataException(payload.provider, missing);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(payload.email)) {
      throw new SocialProfileDataException(payload.provider, [
        'email (invalid format)',
      ]);
    }
  }
}
