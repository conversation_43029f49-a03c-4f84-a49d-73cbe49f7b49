import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import {
  SocialProviderConfigException,
  SocialProviderDependencyException,
  UnsupportedSocialProviderException,
} from './exceptions/social-auth.exception';
import { SocialAuthConfigType } from './interfaces/social-auth-config.interface';

class SocialAuthEnvironmentVariablesValidator {
  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_FACEBOOK_CLIENT_ID: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_FACEBOOK_CLIENT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_FACEBOOK_REDIRECT_URL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_GOOGLE_CLIENT_ID: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_GOOGLE_CLIENT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_GOOGLE_REDIRECT_URL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_MICROSOFT_CLIENT_ID: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_MICROSOFT_CLIENT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_MICROSOFT_CALLBACK_URL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_MICROSOFT_TENANT: string;
}

export const socialAuthConfig = registerAs<SocialAuthConfigType>(
  'socialAuth',
  () => {
    validateConfig(process.env, SocialAuthEnvironmentVariablesValidator);

    return {
      facebook: {
        clientId: process.env.AUTH_FACEBOOK_CLIENT_ID || '',
        clientSecret: process.env.AUTH_FACEBOOK_CLIENT_SECRET || '',
        redirectUrl: process.env.AUTH_FACEBOOK_REDIRECT_URL || '',
      },
      google: {
        clientId: process.env.AUTH_GOOGLE_CLIENT_ID || '',
        clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET || '',
        callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL || '',
      },
      microsoft: {
        clientId: process.env.AUTH_MICROSOFT_CLIENT_ID || '',
        clientSecret: process.env.AUTH_MICROSOFT_CLIENT_SECRET || '',
        callbackURL: process.env.AUTH_MICROSOFT_CALLBACK_URL || '',
        tenant: process.env.AUTH_MICROSOFT_TENANT || '',
      },
    };
  },
);

/**
 * Validate social auth configuration for enabled providers
 * @param config Social auth configuration
 * @param enabledProviders Array of enabled provider names
 * @throws {SocialProviderConfigException} When provider configuration is incomplete
 * @throws {SocialProviderDependencyException} When required dependencies are missing
 * @throws {UnsupportedSocialProviderException} When provider is not supported
 */
export const validateSocialAuthConfig = (
  config: SocialAuthConfigType,
  enabledProviders?: string[],
): void => {
  if (!enabledProviders || enabledProviders.length === 0) {
    return;
  }

  const supportedProviders = ['google', 'facebook', 'microsoft'];
  const configErrors: Error[] = [];

  for (const provider of enabledProviders) {
    if (!supportedProviders.includes(provider)) {
      configErrors.push(
        new UnsupportedSocialProviderException(provider, supportedProviders),
      );
      continue;
    }

    try {
      switch (provider) {
        case 'google':
          validateGoogleConfig(config.google);
          validateGoogleDependency();
          break;
        case 'facebook':
          validateFacebookConfig(config.facebook);
          validateFacebookDependency();
          break;
        case 'microsoft':
          validateMicrosoftConfig(config.microsoft);
          validateMicrosoftDependency();
          break;
      }
    } catch (error) {
      configErrors.push(error);
    }
  }

  // If there are multiple errors, throw the first one with details about all errors
  if (configErrors.length > 0) {
    if (configErrors.length === 1) {
      throw configErrors[0];
    } else {
      const errorMessages = configErrors.map((err) => err.message).join('; ');
      throw new Error(
        `Multiple SocialAuth configuration errors: ${errorMessages}`,
      );
    }
  }
};

/**
 * Validate Google provider configuration
 */
function validateGoogleConfig(config?: any): void {
  if (!config) {
    throw new SocialProviderConfigException(
      'google',
      ['clientId', 'clientSecret', 'callbackURL'],
      'Google provider configuration is missing',
    );
  }

  const missing: string[] = [];
  if (!config.clientId) missing.push('clientId');
  if (!config.clientSecret) missing.push('clientSecret');
  if (!config.callbackURL) missing.push('callbackURL');

  if (missing.length > 0) {
    throw new SocialProviderConfigException(
      'google',
      missing,
      'Set AUTH_GOOGLE_CLIENT_ID, AUTH_GOOGLE_CLIENT_SECRET, and AUTH_GOOGLE_REDIRECT_URL environment variables',
    );
  }

  // Validate URL format
  try {
    new URL(config.callbackURL);
  } catch {
    throw new SocialProviderConfigException(
      'google',
      ['callbackURL'],
      'callbackURL must be a valid URL',
    );
  }
}

/**
 * Validate Facebook provider configuration
 */
function validateFacebookConfig(config?: any): void {
  if (!config) {
    throw new SocialProviderConfigException(
      'facebook',
      ['clientId', 'clientSecret', 'redirectUrl'],
      'Facebook provider configuration is missing',
    );
  }

  const missing: string[] = [];
  if (!config.clientId) missing.push('clientId');
  if (!config.clientSecret) missing.push('clientSecret');
  if (!config.redirectUrl) missing.push('redirectUrl');

  if (missing.length > 0) {
    throw new SocialProviderConfigException(
      'facebook',
      missing,
      'Set AUTH_FACEBOOK_CLIENT_ID, AUTH_FACEBOOK_CLIENT_SECRET, and AUTH_FACEBOOK_REDIRECT_URL environment variables',
    );
  }

  // Validate URL format
  try {
    new URL(config.redirectUrl);
  } catch {
    throw new SocialProviderConfigException(
      'facebook',
      ['redirectUrl'],
      'redirectUrl must be a valid URL',
    );
  }
}

/**
 * Validate Microsoft provider configuration
 */
function validateMicrosoftConfig(config?: any): void {
  if (!config) {
    throw new SocialProviderConfigException(
      'microsoft',
      ['clientId', 'clientSecret', 'callbackURL', 'tenant'],
      'Microsoft provider configuration is missing',
    );
  }

  const missing: string[] = [];
  if (!config.clientId) missing.push('clientId');
  if (!config.clientSecret) missing.push('clientSecret');
  if (!config.callbackURL) missing.push('callbackURL');
  if (!config.tenant) missing.push('tenant');

  if (missing.length > 0) {
    throw new SocialProviderConfigException(
      'microsoft',
      missing,
      'Set AUTH_MICROSOFT_CLIENT_ID, AUTH_MICROSOFT_CLIENT_SECRET, AUTH_MICROSOFT_CALLBACK_URL, and AUTH_MICROSOFT_TENANT environment variables',
    );
  }

  // Validate URL format
  try {
    new URL(config.callbackURL);
  } catch {
    throw new SocialProviderConfigException(
      'microsoft',
      ['callbackURL'],
      'callbackURL must be a valid URL',
    );
  }

  // Validate tenant format (should be GUID or domain)
  const tenantRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i;
  if (!tenantRegex.test(config.tenant)) {
    throw new SocialProviderConfigException(
      'microsoft',
      ['tenant'],
      'tenant must be a valid GUID or domain name',
    );
  }
}

/**
 * Validate Google OAuth dependency
 */
function validateGoogleDependency(): void {
  try {
    require.resolve('passport-google-oauth20');
  } catch {
    throw new SocialProviderDependencyException(
      'google',
      'passport-google-oauth20',
      'npm install passport-google-oauth20 @types/passport-google-oauth20',
    );
  }
}

/**
 * Validate Facebook OAuth dependency
 */
function validateFacebookDependency(): void {
  try {
    require.resolve('passport-facebook');
  } catch {
    throw new SocialProviderDependencyException(
      'facebook',
      'passport-facebook',
      'npm install passport-facebook @types/passport-facebook',
    );
  }
}

/**
 * Validate Microsoft OAuth dependency
 */
function validateMicrosoftDependency(): void {
  try {
    require.resolve('passport-microsoft');
  } catch {
    throw new SocialProviderDependencyException(
      'microsoft',
      'passport-microsoft',
      'npm install passport-microsoft',
    );
  }
}
