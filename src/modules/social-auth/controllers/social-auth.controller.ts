import { getIpAddress, getUserAgent } from '@app/common/utils/request';
import { SocialProviderEnum } from '@app/modules/social-auth/enums/social-provider.enum';
import { FacebookAuthGuard } from '@app/modules/social-auth/guards/facebook-auth.guard';
import { GoogleAuthGuard } from '@app/modules/social-auth/guards/google-auth.guard';
import { MicrosoftAuthGuard } from '@app/modules/social-auth/guards/microsoft-auth.guard';
import { SocialAuthService } from '@app/modules/social-auth/services/social-auth.service';
import {
  Controller,
  Get,
  HttpStatus,
  Logger,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import {
  SocialAuthException,
  SocialOAuthCallbackException,
} from '../exceptions/social-auth.exception';
import { SOCIAL_AUTH_ERROR_CODES } from '../social-auth.error-codes';

@ApiTags('Social Authentication')
@Controller({
  path: 'social-auth',
  version: '1',
})
export class SocialAuthController {
  private readonly logger = new Logger(SocialAuthController.name);

  constructor(private readonly socialAuthService: SocialAuthService) {}

  @ApiOperation({
    summary: 'Initiate Facebook OAuth login',
    description:
      'Redirects user to Facebook OAuth authorization page. The state parameter should contain the callback URL.',
  })
  @ApiQuery({
    name: 'state',
    description:
      'Callback URL where user will be redirected after OAuth completion',
    required: true,
    type: String,
    example: 'https://yourapp.com/auth/callback',
  })
  @ApiResponse({
    status: 200,
    description: 'Redirects to Facebook OAuth authorization page',
  })
  @ApiResponse({
    status: 302,
    description: 'Redirect to Facebook OAuth authorization page',
  })
  @Get('/facebook/login')
  @UseGuards(FacebookAuthGuard)
  facebookLogin() {
    return HttpStatus.OK;
  }

  @ApiOperation({
    summary: 'Facebook OAuth callback handler',
    description:
      'Handles the callback from Facebook OAuth and processes user authentication. Generates authorization code and redirects to the original callback URL.',
  })
  @ApiQuery({
    name: 'state',
    description: 'Original callback URL passed during login initiation',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'code',
    description: 'Authorization code from Facebook OAuth',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 302,
    description:
      'Redirects to callback URL with authorization code or error parameters',
  })
  @ApiBadRequestResponse({
    description:
      'OAuth callback failed - invalid callback URL or missing user data',
    schema: {
      type: 'object',
      properties: {
        error: { type: 'string', example: 'oauth_callback_failed' },
        error_description: {
          type: 'string',
          example: 'Missing callback URL in state parameter',
        },
      },
    },
  })
  @Get('/facebook/redirect')
  @UseGuards(FacebookAuthGuard)
  async facebookLoginRedirect(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const callback = req.query.state as string;
      const user = req.user as any;

      // Validate callback URL
      if (!callback) {
        throw new SocialOAuthCallbackException(
          'facebook',
          'Missing callback URL in state parameter',
        );
      }

      // Validate user data from OAuth
      if (!user) {
        throw new SocialOAuthCallbackException(
          'facebook',
          'No user data received from OAuth provider',
        );
      }

      this.logger.debug(`Facebook OAuth callback for user: ${user.email}`);

      const { authorizationCode } = await this.socialAuthService.saveSocialUser(
        {
          provider: SocialProviderEnum.FACEBOOK,
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          picture: user.picture,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
        },
      );

      // Validate and construct callback URL
      let urlCallback: URL;
      try {
        urlCallback = new URL(callback);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        throw new SocialOAuthCallbackException(
          'facebook',
          'Invalid callback URL format',
        );
      }

      urlCallback.searchParams.append('code', authorizationCode);

      this.logger.debug(`Redirecting to: ${urlCallback.toString()}`);
      return res.redirect(urlCallback.toString());
    } catch (error) {
      this.logger.error(
        `Facebook OAuth callback error: ${error.message}`,
        error.stack,
      );

      // Redirect to error page or callback with error
      const callback = req.query.state as string;
      if (callback) {
        try {
          const urlCallback = new URL(callback);
          urlCallback.searchParams.append('error', 'oauth_callback_failed');
          urlCallback.searchParams.append('error_description', error.message);
          return res.redirect(urlCallback.toString());
        } catch {
          // If callback URL is invalid, return error response
        }
      }

      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'oauth_callback_failed',
        error_description: error.message,
      });
      return;
    }
  }

  @Get('/facebook/delete')
  @ApiOperation({
    summary: 'Handle Facebook data deletion callback',
    description:
      'Webhook endpoint for Facebook to notify about user data deletion requests as per Facebook platform policy.',
  })
  @ApiQuery({
    name: 'providerId',
    description: 'Facebook user ID for the deletion request',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Data deletion request acknowledged',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        data: { type: 'string', example: 'facebook_user_id' },
      },
    },
  })
  facebookDelete(@Query() query: any) {
    const providerId = query.providerId as string;
    console.log(query);
    return {
      statusCode: HttpStatus.OK,
      data: providerId,
    };
  }

  @ApiOperation({
    summary: 'Initiate Google OAuth login',
    description:
      'Redirects user to Google OAuth authorization page. The state parameter should contain the callback URL.',
  })
  @ApiQuery({
    name: 'state',
    description:
      'Callback URL where user will be redirected after OAuth completion',
    required: true,
    type: String,
    example: 'https://yourapp.com/auth/callback',
  })
  @ApiResponse({
    status: 200,
    description: 'Redirects to Google OAuth authorization page',
  })
  @ApiResponse({
    status: 302,
    description: 'Redirect to Google OAuth authorization page',
  })
  @Get('/google/login')
  @UseGuards(GoogleAuthGuard)
  googleAuth() {
    return HttpStatus.OK;
  }

  @ApiOperation({
    summary: 'Google OAuth callback handler',
    description:
      'Handles the callback from Google OAuth and processes user authentication. Generates authorization code and redirects to the original callback URL.',
  })
  @ApiQuery({
    name: 'state',
    description: 'Original callback URL passed during login initiation',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'code',
    description: 'Authorization code from Google OAuth',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 302,
    description:
      'Redirects to callback URL with authorization code or error parameters',
  })
  @ApiBadRequestResponse({
    description:
      'OAuth callback failed - invalid callback URL or missing user data',
    schema: {
      type: 'object',
      properties: {
        error: { type: 'string', example: 'oauth_callback_failed' },
        error_description: {
          type: 'string',
          example: 'Missing callback URL in state parameter',
        },
      },
    },
  })
  @Get('/google/redirect')
  @UseGuards(GoogleAuthGuard)
  async googleLoginRedirect(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const callback = req.query.state as string;
      const user = req.user as any;

      // Validate callback URL
      if (!callback) {
        throw new SocialOAuthCallbackException(
          'google',
          'Missing callback URL in state parameter',
        );
      }

      // Validate user data from OAuth
      if (!user) {
        throw new SocialOAuthCallbackException(
          'google',
          'No user data received from OAuth provider',
        );
      }

      this.logger.debug(`Google OAuth callback for user: ${user.email}`);

      const { authorizationCode } = await this.socialAuthService.saveSocialUser(
        {
          provider: SocialProviderEnum.GOOGLE,
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          picture: user.picture,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
        },
      );

      // Validate and construct callback URL
      let urlCallback: URL;
      try {
        urlCallback = new URL(callback);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        throw new SocialOAuthCallbackException(
          'google',
          'Invalid callback URL format',
        );
      }

      urlCallback.searchParams.append('code', authorizationCode);

      this.logger.debug(`Redirecting to: ${urlCallback.toString()}`);
      return res.redirect(urlCallback.toString());
    } catch (error) {
      this.logger.error(
        `Google OAuth callback error: ${error.message}`,
        error.stack,
      );

      // Redirect to error page or callback with error
      const callback = req.query.state as string;
      if (callback) {
        try {
          const urlCallback = new URL(callback);
          urlCallback.searchParams.append('error', 'oauth_callback_failed');
          urlCallback.searchParams.append('error_description', error.message);
          return res.redirect(urlCallback.toString());
        } catch {
          // If callback URL is invalid, return error response
        }
      }

      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'oauth_callback_failed',
        error_description: error.message,
      });
      return;
    }
  }

  @ApiOperation({
    summary: 'Initiate Microsoft OAuth login',
    description:
      'Redirects user to Microsoft OAuth authorization page. The state parameter should contain the callback URL.',
  })
  @ApiQuery({
    name: 'state',
    description:
      'Callback URL where user will be redirected after OAuth completion',
    required: true,
    type: String,
    example: 'https://yourapp.com/auth/callback',
  })
  @ApiResponse({
    status: 200,
    description: 'Redirects to Microsoft OAuth authorization page',
  })
  @ApiResponse({
    status: 302,
    description: 'Redirect to Microsoft OAuth authorization page',
  })
  @Get('/microsoft/login')
  @UseGuards(MicrosoftAuthGuard)
  microsoftAuth() {
    return HttpStatus.OK;
  }

  @ApiOperation({
    summary: 'Microsoft OAuth callback handler',
    description:
      'Handles the callback from Microsoft OAuth and processes user authentication. Generates authorization code and redirects to the original callback URL.',
  })
  @ApiQuery({
    name: 'state',
    description: 'Original callback URL passed during login initiation',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'code',
    description: 'Authorization code from Microsoft OAuth',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 302,
    description:
      'Redirects to callback URL with authorization code or error parameters',
  })
  @ApiBadRequestResponse({
    description:
      'OAuth callback failed - invalid callback URL or missing user data',
    schema: {
      type: 'object',
      properties: {
        error: { type: 'string', example: 'oauth_callback_failed' },
        error_description: {
          type: 'string',
          example: 'Missing callback URL in state parameter',
        },
      },
    },
  })
  @Get('/microsoft/redirect')
  @UseGuards(MicrosoftAuthGuard)
  async microsoftLoginRedirect(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const callback = req.query.state as string;
      const user = req.user as any;

      // Validate callback URL
      if (!callback) {
        throw new SocialOAuthCallbackException(
          'microsoft',
          'Missing callback URL in state parameter',
        );
      }

      // Validate user data from OAuth
      if (!user) {
        throw new SocialOAuthCallbackException(
          'microsoft',
          'No user data received from OAuth provider',
        );
      }

      this.logger.debug(`Microsoft OAuth callback for user: ${user.email}`);

      const { authorizationCode } = await this.socialAuthService.saveSocialUser(
        {
          provider: SocialProviderEnum.MICROSOFT,
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          picture: user.picture,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
        },
      );

      // Validate and construct callback URL
      let urlCallback: URL;
      try {
        urlCallback = new URL(callback);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        throw new SocialOAuthCallbackException(
          'microsoft',
          'Invalid callback URL format',
        );
      }

      urlCallback.searchParams.append('code', authorizationCode);

      this.logger.debug(`Redirecting to: ${urlCallback.toString()}`);
      return res.redirect(urlCallback.toString());
    } catch (error) {
      this.logger.error(
        `Microsoft OAuth callback error: ${error.message}`,
        error.stack,
      );

      // Redirect to error page or callback with error
      const callback = req.query.state as string;
      if (callback) {
        try {
          const urlCallback = new URL(callback);
          urlCallback.searchParams.append('error', 'oauth_callback_failed');
          urlCallback.searchParams.append('error_description', error.message);
          return res.redirect(urlCallback.toString());
        } catch {
          // If callback URL is invalid, return error response
        }
      }

      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'oauth_callback_failed',
        error_description: error.message,
      });
      return;
    }
  }

  @ApiOperation({
    summary: 'Login with authorization code',
    description:
      'Exchanges an authorization code (obtained from OAuth callback) for access tokens and user session. This completes the social authentication flow.',
  })
  @ApiQuery({
    name: 'code',
    description: 'Authorization code received from OAuth callback',
    required: true,
    type: String,
    example: 'auth_code_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully authenticated user with authorization code',
    schema: {
      type: 'object',
      properties: {
        accessToken: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        refreshToken: { type: 'string', example: 'refresh_token_12345' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'user_12345' },
            email: { type: 'string', example: '<EMAIL>' },
            firstName: { type: 'string', example: 'John' },
            lastName: { type: 'string', example: 'Doe' },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid or missing authorization code',
    schema: {
      type: 'object',
      properties: {
        error: { type: 'string', example: 'INVALID_AUTHORIZATION_CODE' },
        message: { type: 'string', example: 'Authorization code is required' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error during authentication process',
  })
  @Get('/login/code')
  async loginWithCode(@Query('code') code: string, @Req() req: Request) {
    try {
      // Validate authorization code parameter
      if (!code) {
        throw new SocialAuthException(
          SOCIAL_AUTH_ERROR_CODES.INVALID_AUTHORIZATION_CODE,
        );
      }

      const ipAddress = getIpAddress(req);
      const userAgent = getUserAgent(req);

      this.logger.debug(`Login with code attempt from IP: ${ipAddress}`);

      return await this.socialAuthService.loginWithCode(
        code,
        ipAddress,
        userAgent,
      );
    } catch (error) {
      this.logger.error(`Login with code error: ${error.message}`, error.stack);
      throw error;
    }
  }
}
