# Social Authentication Module

The SocialAuthModule is a **dynamic module** that provides OAuth authentication for Google, Facebook, and Microsoft providers. It's designed to be completely optional and separate from the core authentication system, allowing you to include social login functionality only when needed.

## Table of Contents

1. [Features](#features)
2. [Quick Start](#quick-start)
3. [Configuration](#configuration)
4. [Provider Setup](#provider-setup)
5. [Usage Examples](#usage-examples)
6. [Error Handling](#error-handling)
7. [Migration Guide](#migration-guide)
8. [Advanced Examples](#advanced-examples)
9. [Testing](#testing)
10. [Troubleshooting](#troubleshooting)
11. [API Reference](#api-reference)

## Features

- 🔧 **Dynamic Configuration**: Configure via options passed to `forRoot()` or `forRootAsync()`
- 🌍 **Environment Integration**: Load configuration from environment variables
- 📦 **Selective Providers**: Enable only the social providers you need
- ⚡ **Async Configuration**: Support for async configuration loading
- 🔄 **Runtime Flexibility**: Switch social providers based on runtime conditions
- 🎯 **TypeScript Support**: Full type safety with comprehensive interfaces
- 🔒 **Optional Dependencies**: Social provider SDKs are only required when used
- 🧩 **Modular Design**: Completely separate from core auth module
- 🛡️ **Comprehensive Error Handling**: Detailed error messages and validation
- 🔍 **Dependency Validation**: Automatic detection of missing dependencies

## Quick Start

### Basic Usage

Enable all configured social providers:

```typescript
import { SocialAuthModule } from '@modules/social-auth';

@Module({
  imports: [
    SocialAuthModule.forRoot({
      providers: {
        google: {
          clientId: 'your-google-client-id',
          clientSecret: 'your-google-client-secret',
          callbackURL: 'http://localhost:3000/social-auth/google/redirect',
        },
        facebook: {
          clientId: 'your-facebook-app-id',
          clientSecret: 'your-facebook-app-secret',
          redirectUrl: 'http://localhost:3000/social-auth/facebook/redirect',
        },
      },
    }),
  ],
})
export class AppModule {}
```

### Enable Specific Providers

Only enable the social providers you need:

```typescript
import { SocialProviderEnum } from '@modules/social-auth';

@Module({
  imports: [
    SocialAuthModule.forRoot({
      providers: {
        google: {
          clientId: process.env.AUTH_GOOGLE_CLIENT_ID,
          clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET,
          callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL,
        },
      },
      enabledProviders: [SocialProviderEnum.GOOGLE],
    }),
  ],
})
export class AppModule {}
```

### Environment-Based Configuration

Use environment variables for configuration:

```typescript
@Module({
  imports: [
    SocialAuthModule.forRoot(), // Uses environment variables
  ],
})
export class AppModule {}
```

## Configuration

### SocialAuthModuleOptions

```typescript
interface SocialAuthModuleOptions {
  /** Provider configurations */
  providers: {
    google?: GoogleProviderConfig;
    facebook?: FacebookProviderConfig;
    microsoft?: MicrosoftProviderConfig;
  };
  
  /** Explicitly enable specific providers */
  enabledProviders?: SocialProviderEnum[];
}
```

### Provider Configuration Interfaces

#### Google Provider
```typescript
interface GoogleProviderConfig {
  clientId: string;
  clientSecret: string;
  callbackURL: string;
}
```

#### Facebook Provider
```typescript
interface FacebookProviderConfig {
  clientId: string;
  clientSecret: string;
  redirectUrl: string;
}
```

#### Microsoft Provider
```typescript
interface MicrosoftProviderConfig {
  clientId: string;
  clientSecret: string;
  callbackURL: string;
  tenant: string; // Azure AD tenant ID or domain
}
```

### Async Configuration

#### Using ConfigService

```typescript
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    SocialAuthModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        providers: {
          google: {
            clientId: configService.get('AUTH_GOOGLE_CLIENT_ID'),
            clientSecret: configService.get('AUTH_GOOGLE_CLIENT_SECRET'),
            callbackURL: configService.get('AUTH_GOOGLE_REDIRECT_URL'),
          },
          facebook: {
            clientId: configService.get('AUTH_FACEBOOK_CLIENT_ID'),
            clientSecret: configService.get('AUTH_FACEBOOK_CLIENT_SECRET'),
            redirectUrl: configService.get('AUTH_FACEBOOK_REDIRECT_URL'),
          },
        },
        enabledProviders: [SocialProviderEnum.GOOGLE, SocialProviderEnum.FACEBOOK],
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

#### Configuration Service Pattern

```typescript
@Injectable()
export class SocialAuthConfigService {
  constructor(private configService: ConfigService) {}

  createSocialAuthOptions(): SocialAuthModuleOptions {
    return {
      providers: {
        google: {
          clientId: this.configService.get('AUTH_GOOGLE_CLIENT_ID'),
          clientSecret: this.configService.get('AUTH_GOOGLE_CLIENT_SECRET'),
          callbackURL: this.configService.get('AUTH_GOOGLE_REDIRECT_URL'),
        },
      },
      enabledProviders: this.getEnabledProviders(),
    };
  }

  private getEnabledProviders(): SocialProviderEnum[] {
    const providers = [];
    if (this.configService.get('AUTH_GOOGLE_CLIENT_ID')) {
      providers.push(SocialProviderEnum.GOOGLE);
    }
    return providers;
  }
}
```

### Conditional Provider Enabling

#### Feature Flag Based

```typescript
@Module({
  imports: [
    SocialAuthModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const enabledProviders = [];

        if (configService.get('FEATURE_GOOGLE_AUTH') === 'true') {
          enabledProviders.push(SocialProviderEnum.GOOGLE);
        }

        if (configService.get('FEATURE_FACEBOOK_AUTH') === 'true') {
          enabledProviders.push(SocialProviderEnum.FACEBOOK);
        }

        return {
          providers: {
            google: {
              clientId: configService.get('AUTH_GOOGLE_CLIENT_ID'),
              clientSecret: configService.get('AUTH_GOOGLE_CLIENT_SECRET'),
              callbackURL: configService.get('AUTH_GOOGLE_REDIRECT_URL'),
            },
            facebook: {
              clientId: configService.get('AUTH_FACEBOOK_CLIENT_ID'),
              clientSecret: configService.get('AUTH_FACEBOOK_CLIENT_SECRET'),
              redirectUrl: configService.get('AUTH_FACEBOOK_REDIRECT_URL'),
            },
          },
          enabledProviders,
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

#### Environment-Based Provider Selection

```typescript
@Module({
  imports: [
    SocialAuthModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const environment = configService.get('NODE_ENV');
        const enabledProviders = [];

        // Always enable Google in production
        if (environment === 'production') {
          enabledProviders.push(SocialProviderEnum.GOOGLE);
        }

        // Enable Facebook only if configured
        if (configService.get('AUTH_FACEBOOK_CLIENT_ID')) {
          enabledProviders.push(SocialProviderEnum.FACEBOOK);
        }

        // Enable Microsoft for enterprise environments
        if (configService.get('ENABLE_ENTERPRISE_AUTH') === 'true') {
          enabledProviders.push(SocialProviderEnum.MICROSOFT);
        }

        return {
          providers: {
            google: {
              clientId: configService.get('AUTH_GOOGLE_CLIENT_ID'),
              clientSecret: configService.get('AUTH_GOOGLE_CLIENT_SECRET'),
              callbackURL: configService.get('AUTH_GOOGLE_REDIRECT_URL'),
            },
            facebook: {
              clientId: configService.get('AUTH_FACEBOOK_CLIENT_ID'),
              clientSecret: configService.get('AUTH_FACEBOOK_CLIENT_SECRET'),
              redirectUrl: configService.get('AUTH_FACEBOOK_REDIRECT_URL'),
            },
            microsoft: {
              clientId: configService.get('AUTH_MICROSOFT_CLIENT_ID'),
              clientSecret: configService.get('AUTH_MICROSOFT_CLIENT_SECRET'),
              callbackURL: configService.get('AUTH_MICROSOFT_CALLBACK_URL'),
              tenant: configService.get('AUTH_MICROSOFT_TENANT'),
            },
          },
          enabledProviders,
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## Provider Setup

### Google OAuth Setup

1. **Create Google OAuth Application**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Create OAuth 2.0 credentials

2. **Required Configuration**:
   ```typescript
   google: {
     clientId: 'your-google-client-id.googleusercontent.com',
     clientSecret: 'your-google-client-secret',
     callbackURL: 'http://localhost:3000/social-auth/google/redirect',
   }
   ```

3. **Environment Variables**:
   ```bash
   AUTH_GOOGLE_CLIENT_ID=your-google-client-id.googleusercontent.com
   AUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
   AUTH_GOOGLE_REDIRECT_URL=http://localhost:3000/social-auth/google/redirect
   ```

4. **Required Dependencies**:
   ```bash
   npm install passport-google-oauth20 @types/passport-google-oauth20
   ```

### Facebook OAuth Setup

1. **Create Facebook App**:
   - Go to [Facebook Developers](https://developers.facebook.com/)
   - Create a new app
   - Add Facebook Login product
   - Configure OAuth redirect URIs

2. **Required Configuration**:
   ```typescript
   facebook: {
     clientId: 'your-facebook-app-id',
     clientSecret: 'your-facebook-app-secret',
     redirectUrl: 'http://localhost:3000/social-auth/facebook/redirect',
   }
   ```

3. **Environment Variables**:
   ```bash
   AUTH_FACEBOOK_CLIENT_ID=your-facebook-app-id
   AUTH_FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
   AUTH_FACEBOOK_REDIRECT_URL=http://localhost:3000/social-auth/facebook/redirect
   ```

4. **Required Dependencies**:
   ```bash
   npm install passport-facebook @types/passport-facebook
   ```

### Microsoft OAuth Setup

1. **Create Azure AD Application**:
   - Go to [Azure Portal](https://portal.azure.com/)
   - Navigate to Azure Active Directory > App registrations
   - Create a new registration
   - Configure redirect URIs

2. **Required Configuration**:
   ```typescript
   microsoft: {
     clientId: 'your-azure-app-id',
     clientSecret: 'your-azure-app-secret',
     callbackURL: 'http://localhost:3000/social-auth/microsoft/redirect',
     tenant: 'your-tenant-id-or-domain.com',
   }
   ```

3. **Environment Variables**:
   ```bash
   AUTH_MICROSOFT_CLIENT_ID=your-azure-app-id
   AUTH_MICROSOFT_CLIENT_SECRET=your-azure-app-secret
   AUTH_MICROSOFT_CALLBACK_URL=http://localhost:3000/social-auth/microsoft/redirect
   AUTH_MICROSOFT_TENANT=your-tenant-id-or-domain.com
   ```

4. **Required Dependencies**:
   ```bash
   npm install passport-microsoft
   ```

## Usage Examples

### API Endpoints

When the SocialAuthModule is imported, the following endpoints become available:

#### Google OAuth
- `GET /social-auth/google/login` - Initiate Google OAuth flow
- `GET /social-auth/google/redirect` - Google OAuth callback

#### Facebook OAuth
- `GET /social-auth/facebook/login` - Initiate Facebook OAuth flow
- `GET /social-auth/facebook/redirect` - Facebook OAuth callback

#### Microsoft OAuth
- `GET /social-auth/microsoft/login` - Initiate Microsoft OAuth flow
- `GET /social-auth/microsoft/redirect` - Microsoft OAuth callback

#### Generic OAuth
- `POST /social-auth/login/code` - Handle OAuth authorization codes

### Using in Services

#### Basic Usage

```typescript
import { SocialAuthService } from '@modules/social-auth';

@Injectable()
export class UserService {
  constructor(
    private readonly socialAuthService: SocialAuthService,
  ) {}

  async handleSocialLogin(provider: string, profile: any): Promise<any> {
    return this.socialAuthService.validateSocialUser(provider, profile);
  }
}
```

#### Integration with Core Auth

```typescript
@Injectable()
export class AuthService {
  constructor(
    private readonly socialAuthService: SocialAuthService,
    private readonly jwtService: JwtService,
  ) {}

  async socialLogin(provider: string, profile: any): Promise<{ accessToken: string }> {
    const user = await this.socialAuthService.validateSocialUser(provider, profile);
    const payload = { sub: user.id, email: user.email };
    
    return {
      accessToken: this.jwtService.sign(payload),
    };
  }
}
```

#### Custom Service Integration

```typescript
@Injectable()
export class UserSocialAuthService {
  constructor(
    private readonly socialAuthService: SocialAuthService,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async handleSocialLogin(provider: string, profile: any) {
    // Validate social user
    const socialUser = await this.socialAuthService.validateSocialUser(
      provider,
      profile,
    );

    // Check if user exists
    let user = await this.userService.findByEmail(socialUser.email);

    if (!user) {
      // Create new user
      user = await this.userService.create({
        email: socialUser.email,
        firstName: socialUser.firstName,
        lastName: socialUser.lastName,
        avatar: socialUser.avatar,
        isEmailVerified: true, // Social accounts are pre-verified
      });
    }

    // Generate JWT token
    const payload = { sub: user.id, email: user.email };
    const accessToken = this.jwtService.sign(payload);

    return {
      user,
      accessToken,
      provider,
    };
  }
}
```

#### Custom Controller Integration

```typescript
@Controller('auth')
export class CustomSocialAuthController {
  constructor(
    private readonly userSocialAuthService: UserSocialAuthService,
  ) {}

  @Get('google')
  @UseGuards(GoogleAuthGuard)
  async googleAuth(@Req() req: Request) {
    // This route initiates the Google OAuth flow
  }

  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  async googleAuthCallback(@Req() req: Request, @Res() res: Response) {
    try {
      const result = await this.userSocialAuthService.handleSocialLogin(
        'google',
        req.user,
      );

      // Redirect to frontend with token
      res.redirect(`${process.env.FRONTEND_URL}/auth/success?token=${result.accessToken}`);
    } catch (error) {
      res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=${error.message}`);
    }
  }
}
```

## Error Handling

The module provides comprehensive error handling across all components:

### Error Codes

All error codes are defined with the namespace `SOCIAL_AUTH`:

| Error Code | Status | Description |
|------------|--------|-------------|
| `PROVIDER_NOT_CONFIGURED` | 400 | Social provider is not configured or enabled |
| `PROVIDER_NOT_SUPPORTED` | 400 | Social provider is not supported |
| `MISSING_PROVIDER_DEPENDENCY` | 500 | Required social provider dependency is missing |
| `INVALID_PROVIDER_CONFIG` | 500 | Social provider configuration is invalid or incomplete |
| `OAUTH_CALLBACK_ERROR` | 400 | OAuth callback failed or returned an error |
| `OAUTH_STATE_MISMATCH` | 400 | OAuth state parameter mismatch - possible CSRF attack |
| `OAUTH_ACCESS_DENIED` | 401 | User denied access to social provider |
| `SOCIAL_ACCOUNT_ALREADY_LINKED` | 409 | Social account is already linked to another user |
| `SOCIAL_ACCOUNT_NOT_FOUND` | 404 | Social account not found |
| `INVALID_AUTHORIZATION_CODE` | 400 | Invalid or expired authorization code |
| `PROVIDER_AUTHENTICATION_FAILED` | 401 | Authentication with social provider failed |
| `PROFILE_DATA_INCOMPLETE` | 400 | Social provider profile data is incomplete or invalid |
| `SOCIAL_EMAIL_MISMATCH` | 400 | Social account email does not match user email |
| `PROVIDER_SERVICE_UNAVAILABLE` | 503 | Social provider service is temporarily unavailable |

### Exception Classes

#### Base Exception
- `SocialAuthException`: Base class for all social auth errors

#### Specific Exceptions
- `SocialProviderConfigException`: Configuration errors with missing field details
- `SocialProviderDependencyException`: Missing dependency errors with install commands
- `SocialOAuthCallbackException`: OAuth callback errors with provider context
- `SocialProviderAuthException`: Authentication failures with provider context
- `SocialProfileDataException`: Profile data validation errors with missing fields
- `UnsupportedSocialProviderException`: Unsupported provider errors with supported list
- `SocialProviderServiceException`: Provider service unavailability with HTTP status

### Configuration Validation

The `SocialAuthModule` validates configuration during initialization:

```typescript
// Validates provider configurations
validateSocialAuthConfig(config, enabledProviders);

// Validates dependencies are installed
require.resolve('passport-google-oauth20');
```

### Provider Specific Validation

#### Google Provider
- Validates `clientId`, `clientSecret`, `callbackURL`
- Validates URL format for `callbackURL`
- Checks for `passport-google-oauth20` dependency

#### Facebook Provider
- Validates `clientId`, `clientSecret`, `redirectUrl`
- Validates URL format for `redirectUrl`
- Checks for `passport-facebook` dependency

#### Microsoft Provider
- Validates `clientId`, `clientSecret`, `callbackURL`, `tenant`
- Validates URL format for `callbackURL`
- Validates tenant format (GUID or domain)
- Checks for `passport-microsoft` dependency

### Error Handling Examples

#### Basic Error Handling
```typescript
try {
  const result = await socialAuthService.loginWithCode(code, ip, userAgent);
  return result;
} catch (error) {
  if (error instanceof SocialAuthException) {
    // Handle social auth specific errors
    logger.error(`Social auth error: ${error.message}`);
    throw error;
  }
  // Handle other errors
  throw new InternalServerErrorException('Login failed');
}
```

#### Configuration Error Handling
```typescript
@Module({
  imports: [
    SocialAuthModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        try {
          const config = {
            providers: {
              google: {
                clientId: configService.get('AUTH_GOOGLE_CLIENT_ID'),
                clientSecret: configService.get('AUTH_GOOGLE_CLIENT_SECRET'),
                callbackURL: configService.get('AUTH_GOOGLE_REDIRECT_URL'),
              },
            },
            enabledProviders: [SocialProviderEnum.GOOGLE],
          };

          // Validate configuration
          if (!config.providers.google.clientId) {
            throw new Error('Google OAuth client ID is required');
          }

          return config;
        } catch (error) {
          console.error('SocialAuth configuration error:', error.message);
          
          // Return minimal configuration or throw error
          return {
            providers: {},
            enabledProviders: [],
          };
        }
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## Migration Guide

### Migrating from Core Auth Module

If you're upgrading from a version where social auth was part of the core auth module:

#### Before (Integrated)
```typescript
// Old structure - social auth was part of AuthModule
@Module({
  imports: [
    AuthModule, // Included social auth automatically
  ],
})
export class AppModule {}
```

#### After (Separated)
```typescript
// New structure - social auth is optional
@Module({
  imports: [
    AuthModule, // Core auth only
    SocialAuthModule.forRoot({
      providers: {
        google: {
          clientId: process.env.AUTH_GOOGLE_CLIENT_ID,
          clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET,
          callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL,
        },
      },
    }),
  ],
})
export class AppModule {}
```

### Migration Steps

1. **Update Module Imports**:
   ```typescript
   // Before
   import { AuthModule } from '@modules/auth';
   
   // After
   import { AuthModule } from '@modules/auth';
   import { SocialAuthModule } from '@modules/social-auth';
   ```

2. **Update Service Imports**:
   ```typescript
   // Before
   import { SocialService } from '@modules/auth';
   
   // After
   import { SocialAuthService } from '@modules/social-auth';
   ```

3. **Update Import Paths**:
   ```typescript
   // Before
   import { SocialEntity } from '@modules/auth/entities/social.entity';
   import { GoogleAuthGuard } from '@modules/auth/guards/google-auth.guard';
   
   // After
   import { SocialEntity } from '@modules/social-auth/entities/social.entity';
   import { GoogleAuthGuard } from '@modules/social-auth/guards/google-auth.guard';
   ```

4. **Update Route Paths**:
   ```typescript
   // Before
   GET /auth/google/login
   GET /auth/google/redirect
   
   // After
   GET /social-auth/google/login
   GET /social-auth/google/redirect
   ```

5. **Update Frontend Integration**:
   ```javascript
   // Before
   const googleLoginUrl = '/auth/google/login';
   
   // After
   const googleLoginUrl = '/social-auth/google/login';
   ```

6. **Install Optional Dependencies** (if not already installed):
   ```bash
   # Only install the providers you need
   npm install passport-google-oauth20 @types/passport-google-oauth20
   npm install passport-facebook @types/passport-facebook
   npm install passport-microsoft
   ```

### Database Migration

**No database migration is required.** The `SocialEntity` remains compatible with existing database schemas. The entity has been moved but maintains the same structure and relationships.

### Backward Compatibility

#### ✅ Compatible
- Database entities and relationships
- JWT token generation and session handling
- User creation and login flow
- Error codes and messages
- Environment variable names
- OAuth provider configurations

#### ⚠️ Changed
- Import paths for social auth related classes
- API endpoint paths (prefix changed from `/auth` to `/social-auth`)
- Service names (`SocialService` → `SocialAuthService`)
- Module structure (social auth is now separate)

### Common Migration Issues

#### Issue 1: Import Errors
**Error:**
```
Cannot find module '@modules/auth/services/social.service'
```

**Solution:**
```typescript
// Change this
import { SocialService } from '@modules/auth/services/social.service';

// To this
import { SocialAuthService } from '@modules/social-auth/services/social-auth.service';
```

#### Issue 2: Route Not Found
**Error:**
```
404 Not Found: GET /auth/google/login
```

**Solution:**
Update your frontend or API calls to use the new route prefix:
```javascript
// Change this
const loginUrl = '/auth/google/login';

// To this
const loginUrl = '/social-auth/google/login';
```

#### Issue 3: Provider Not Registered
**Error:**
```
No provider found for GoogleStrategy
```

**Solution:**
Ensure you've imported the SocialAuthModule and configured the provider:
```typescript
@Module({
  imports: [
    SocialAuthModule.forRoot({
      providers: {
        google: {
          clientId: process.env.AUTH_GOOGLE_CLIENT_ID,
          clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET,
          callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL,
        },
      },
    }),
  ],
})
export class AppModule {}
```

#### Issue 4: Missing Dependencies
**Error:**
```
Missing required dependency: passport-google-oauth20
```

**Solution:**
Install the required dependency:
```bash
npm install passport-google-oauth20 @types/passport-google-oauth20
```

### Validation Checklist

After migration, verify the following:

- [ ] Application starts without errors
- [ ] Social login endpoints are accessible
- [ ] OAuth flows work for all enabled providers
- [ ] User creation and linking works correctly
- [ ] JWT tokens are generated properly
- [ ] Database relationships are intact
- [ ] Tests pass
- [ ] Frontend integration works
- [ ] Error handling works as expected
- [ ] Configuration validation works

## Advanced Examples

### Multi-Tenant Configuration

```typescript
@Injectable()
export class TenantSocialAuthService {
  constructor(private configService: ConfigService) {}

  getSocialAuthConfigForTenant(tenantId: string): SocialAuthModuleOptions {
    const tenantConfig = this.getTenantConfig(tenantId);

    return {
      providers: {
        google: tenantConfig.google ? {
          clientId: tenantConfig.google.clientId,
          clientSecret: tenantConfig.google.clientSecret,
          callbackURL: `${tenantConfig.baseUrl}/social-auth/google/redirect`,
        } : undefined,
        facebook: tenantConfig.facebook ? {
          clientId: tenantConfig.facebook.clientId,
          clientSecret: tenantConfig.facebook.clientSecret,
          redirectUrl: `${tenantConfig.baseUrl}/social-auth/facebook/redirect`,
        } : undefined,
      },
      enabledProviders: tenantConfig.enabledProviders,
    };
  }

  private getTenantConfig(tenantId: string) {
    // This would typically come from a database or configuration service
    const tenantConfigs = {
      'tenant-1': {
        baseUrl: 'https://tenant1.example.com',
        enabledProviders: [SocialProviderEnum.GOOGLE, SocialProviderEnum.MICROSOFT],
        google: {
          clientId: process.env.TENANT1_GOOGLE_CLIENT_ID,
          clientSecret: process.env.TENANT1_GOOGLE_CLIENT_SECRET,
        },
      },
      'tenant-2': {
        baseUrl: 'https://tenant2.example.com',
        enabledProviders: [SocialProviderEnum.GOOGLE, SocialProviderEnum.FACEBOOK],
        google: {
          clientId: process.env.TENANT2_GOOGLE_CLIENT_ID,
          clientSecret: process.env.TENANT2_GOOGLE_CLIENT_SECRET,
        },
        facebook: {
          clientId: process.env.TENANT2_FACEBOOK_CLIENT_ID,
          clientSecret: process.env.TENANT2_FACEBOOK_CLIENT_SECRET,
        },
      },
    };

    return tenantConfigs[tenantId] || tenantConfigs['tenant-1'];
  }
}
```

### Multiple Module Instances

```typescript
@Module({
  imports: [
    // Consumer-facing social auth
    SocialAuthModule.forRoot({
      providers: {
        google: { /* consumer config */ },
        facebook: { /* consumer config */ },
      },
      enabledProviders: [SocialProviderEnum.GOOGLE, SocialProviderEnum.FACEBOOK],
    }),
    
    // Enterprise social auth (different module instance)
    SocialAuthModule.forRootAsync({
      useFactory: () => ({
        providers: {
          microsoft: { /* enterprise config */ },
        },
        enabledProviders: [SocialProviderEnum.MICROSOFT],
      }),
    }),
  ],
})
export class AppModule {}
```

## Testing

### Unit Testing with Mock Configuration

```typescript
describe('SocialAuthService', () => {
  let service: SocialAuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        SocialAuthModule.forRoot({
          providers: {
            google: {
              clientId: 'test-google-client-id',
              clientSecret: 'test-google-client-secret',
              callbackURL: 'http://localhost:3000/social-auth/google/redirect',
            },
          },
          enabledProviders: [SocialProviderEnum.GOOGLE],
        }),
      ],
    }).compile();

    service = module.get<SocialAuthService>(SocialAuthService);
  });

  it('should validate social user', async () => {
    const mockProfile = {
      id: '123456789',
      emails: [{ value: '<EMAIL>' }],
      name: { givenName: 'John', familyName: 'Doe' },
      photos: [{ value: 'https://example.com/photo.jpg' }],
    };

    const result = await service.validateSocialUser('google', mockProfile);
    
    expect(result).toBeDefined();
    expect(result.email).toBe('<EMAIL>');
    expect(result.firstName).toBe('John');
    expect(result.lastName).toBe('Doe');
  });
});
```

### Integration Testing

```typescript
describe('SocialAuth Integration', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AuthModule,
        UserModule,
        SocialAuthModule.forRoot({
          providers: {
            google: {
              clientId: 'test-google-client-id',
              clientSecret: 'test-google-client-secret',
              callbackURL: 'http://localhost:3000/social-auth/google/redirect',
            },
          },
          enabledProviders: [SocialProviderEnum.GOOGLE],
        }),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/social-auth/google/login (GET)', () => {
    return request(app.getHttpServer())
      .get('/social-auth/google/login')
      .expect(302); // Redirect to Google OAuth
  });
});
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies Error**:
   ```
   Error: Missing required dependency: passport-google-oauth20
   ```
   **Solution**: Install the required dependency:
   ```bash
   npm install passport-google-oauth20 @types/passport-google-oauth20
   ```

2. **Configuration Validation Error**:
   ```
   Error: Google provider configuration is incomplete
   ```
   **Solution**: Ensure all required configuration fields are provided:
   ```typescript
   google: {
     clientId: 'required',
     clientSecret: 'required',
     callbackURL: 'required',
   }
   ```

3. **OAuth Callback URL Mismatch**:
   ```
   Error: redirect_uri_mismatch
   ```
   **Solution**: Ensure the callback URL in your provider configuration matches the one registered in the OAuth provider's console.

4. **Module Not Loading Providers**:
   **Solution**: Check that `enabledProviders` includes the providers you want to use, or ensure provider configurations are complete.

### Debug Mode

Enable debug logging to troubleshoot configuration issues:

```typescript
SocialAuthModule.forRootAsync({
  useFactory: (configService: ConfigService) => {
    const config = {
      // ... your configuration
    };
    
    // Log configuration in development
    if (configService.get('NODE_ENV') === 'development') {
      console.log('SocialAuth Configuration:', config);
    }
    
    return config;
  },
  inject: [ConfigService],
})
```

## Best Practices

### 1. Environment-Specific Configuration

```typescript
// Development - enable all providers for testing
if (environment === 'development') {
  return {
    enabledProviders: [
      SocialProviderEnum.GOOGLE,
      SocialProviderEnum.FACEBOOK,
      SocialProviderEnum.MICROSOFT,
    ],
  };
}

// Production - enable only configured providers
if (environment === 'production') {
  return {
    enabledProviders: [SocialProviderEnum.GOOGLE], // Only Google in production
  };
}
```

### 2. Selective Provider Loading

Only load the providers you need to reduce bundle size and dependencies:

```typescript
SocialAuthModule.forRoot({
  enabledProviders: [SocialProviderEnum.GOOGLE], // Only load Google provider
})
```

### 3. Secure Configuration

Keep sensitive credentials in environment variables:

```typescript
// ✅ Good - use environment variables
providers: {
  google: {
    clientId: process.env.AUTH_GOOGLE_CLIENT_ID,
    clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL,
  },
}

// ❌ Bad - hardcoded credentials
providers: {
  google: {
    clientId: 'hardcoded-client-id',
    clientSecret: 'hardcoded-secret',
    callbackURL: 'http://localhost:3000/callback',
  },
}
```

### 4. Type Safety

Use TypeScript interfaces for configuration:

```typescript
import { SocialAuthModuleOptions, SocialProviderEnum } from '@modules/social-auth';

const socialAuthConfig: SocialAuthModuleOptions = {
  providers: {
    google: {
      clientId: process.env.AUTH_GOOGLE_CLIENT_ID!,
      clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET!,
      callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL!,
    },
  },
  enabledProviders: [SocialProviderEnum.GOOGLE],
};
```

## API Reference

### Classes
- `SocialAuthModule` - Main dynamic module
- `SocialAuthService` - Service for handling social authentication
- `SocialAuthController` - Controller with OAuth endpoints

### Interfaces
- `SocialAuthModuleOptions` - Module configuration options
- `GoogleProviderConfig` - Google OAuth configuration
- `FacebookProviderConfig` - Facebook OAuth configuration
- `MicrosoftProviderConfig` - Microsoft OAuth configuration

### Enums
- `SocialProviderEnum` - Available social providers

### Exceptions
- `SocialProviderConfigException` - Configuration validation errors
- `SocialProviderDependencyException` - Missing dependency errors
- `UnsupportedSocialProviderException` - Unsupported provider errors
- `SocialAuthException` - Base class for all social auth errors
- `SocialOAuthCallbackException` - OAuth callback errors
- `SocialProviderAuthException` - Authentication failures
- `SocialProfileDataException` - Profile data validation errors
- `SocialProviderServiceException` - Provider service unavailability

For more detailed API documentation, see the TypeScript interfaces in the source code.