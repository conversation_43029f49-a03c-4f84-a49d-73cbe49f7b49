import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { ErrorCode } from '@app/common/types';
import { SOCIAL_AUTH_ERROR_CODES } from '../social-auth.error-codes';

/**
 * Base exception class for social authentication errors
 */
export class SocialAuthException extends HttpErrorException {
  constructor(errorCode: ErrorCode, details?: string) {
    const message = details
      ? `${errorCode.message}: ${details}`
      : errorCode.message;
    super({
      ...errorCode,
      message,
    });
  }
}

/**
 * Exception for provider configuration errors
 */
export class SocialProviderConfigException extends SocialAuthException {
  constructor(provider: string, missingFields: string[], details?: string) {
    const message = `Provider "${provider}" configuration error. Missing: ${missingFields.join(', ')}`;
    const fullMessage = details ? `${message}. ${details}` : message;

    super({
      ...SOCIAL_AUTH_ERROR_CODES.INVALID_PROVIDER_CONFIG,
      message: fullMessage,
    });
  }
}

/**
 * Exception for missing provider dependencies
 */
export class SocialProviderDependencyException extends SocialAuthException {
  constructor(provider: string, dependency: string, installCommand?: string) {
    let message = `Provider "${provider}" requires dependency "${dependency}" to be installed`;
    if (installCommand) {
      message += `. Install with: ${installCommand}`;
    }

    super({
      ...SOCIAL_AUTH_ERROR_CODES.MISSING_PROVIDER_DEPENDENCY,
      message,
    });
  }
}

/**
 * Exception for OAuth callback errors
 */
export class SocialOAuthCallbackException extends SocialAuthException {
  constructor(provider: string, error: string, description?: string) {
    let message = `OAuth callback failed for provider "${provider}": ${error}`;
    if (description) {
      message += ` - ${description}`;
    }

    super({
      ...SOCIAL_AUTH_ERROR_CODES.OAUTH_CALLBACK_ERROR,
      message,
    });
  }
}

/**
 * Exception for provider authentication failures
 */
export class SocialProviderAuthException extends SocialAuthException {
  constructor(provider: string, reason?: string) {
    const message = reason
      ? `Authentication failed for provider "${provider}": ${reason}`
      : `Authentication failed for provider "${provider}"`;

    super({
      ...SOCIAL_AUTH_ERROR_CODES.PROVIDER_AUTHENTICATION_FAILED,
      message,
    });
  }
}

/**
 * Exception for profile data validation errors
 */
export class SocialProfileDataException extends SocialAuthException {
  constructor(provider: string, missingFields: string[]) {
    const message = `Profile data from provider "${provider}" is incomplete. Missing: ${missingFields.join(', ')}`;

    super({
      ...SOCIAL_AUTH_ERROR_CODES.PROFILE_DATA_INCOMPLETE,
      message,
    });
  }
}

/**
 * Exception for unsupported providers
 */
export class UnsupportedSocialProviderException extends SocialAuthException {
  constructor(provider: string, supportedProviders: string[]) {
    const message = `Provider "${provider}" is not supported. Supported providers: ${supportedProviders.join(', ')}`;

    super({
      ...SOCIAL_AUTH_ERROR_CODES.PROVIDER_NOT_SUPPORTED,
      message,
    });
  }
}

/**
 * Exception for provider service unavailability
 */
export class SocialProviderServiceException extends SocialAuthException {
  constructor(provider: string, statusCode?: number, details?: string) {
    let message = `Provider "${provider}" service is unavailable`;
    if (statusCode) {
      message += ` (HTTP ${statusCode})`;
    }
    if (details) {
      message += `: ${details}`;
    }

    super({
      ...SOCIAL_AUTH_ERROR_CODES.PROVIDER_SERVICE_UNAVAILABLE,
      message,
    });
  }
}
