// Module
// Controllers
export { SocialAuthController } from './controllers/social-auth.controller';
// Entities
export { SocialEntity } from './entities/social.entity';
// Enums
export { SocialProviderEnum } from './enums/social-provider.enum';
// Exceptions
export * from './exceptions/social-auth.exception';
export { FacebookAuthGuard } from './guards/facebook-auth.guard';
// Guards
export { GoogleAuthGuard } from './guards/google-auth.guard';
export { MicrosoftAuthGuard } from './guards/microsoft-auth.guard';
// Interfaces
export * from './interfaces/social-auth-config.interface';
// Services
export { SocialAuthService } from './services/social-auth.service';
// Configuration
export {
  socialAuthConfig,
  validateSocialAuthConfig,
} from './social-auth.config';
// Error Codes
export { SOCIAL_AUTH_ERROR_CODES } from './social-auth.error-codes';
export { SocialAuthModule } from './social-auth.module';
export { FacebookStrategy } from './strategies/facebook.strategy';
// Strategies
export { GoogleStrategy } from './strategies/google.strategy';
export { MicrosoftStrategy } from './strategies/microsoft.strategy';
// Error Handling Utilities
export { SocialAuthErrorHandler } from './utils/error-handler.util';
