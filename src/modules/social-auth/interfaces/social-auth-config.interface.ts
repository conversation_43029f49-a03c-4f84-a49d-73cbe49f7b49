import { SocialProviderEnum } from '../enums/social-provider.enum';

export interface GoogleProviderConfig {
  clientId: string;
  clientSecret: string;
  callbackURL: string;
}

export interface FacebookProviderConfig {
  clientId: string;
  clientSecret: string;
  redirectUrl: string;
}

export interface MicrosoftProviderConfig {
  clientId: string;
  clientSecret: string;
  callbackURL: string;
  tenant: string;
}

export interface SocialAuthConfigType {
  google?: GoogleProviderConfig;
  facebook?: FacebookProviderConfig;
  microsoft?: MicrosoftProviderConfig;
}

export interface SocialAuthModuleOptions {
  providers: {
    google?: GoogleProviderConfig;
    facebook?: FacebookProviderConfig;
    microsoft?: MicrosoftProviderConfig;
  };
  enabledProviders?: SocialProviderEnum[];
}

export interface SocialAuthModuleAsyncOptions {
  imports?: any[];
  useFactory: (
    ...args: any[]
  ) => Promise<SocialAuthModuleOptions> | SocialAuthModuleOptions;
  inject?: any[];
}
