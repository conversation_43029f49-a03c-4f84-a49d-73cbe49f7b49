import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Profile, Strategy } from 'passport-facebook';
import { SocialProviderEnum } from '../enums/social-provider.enum';
import {
  SocialOAuthCallbackException,
  SocialProfileDataException,
  SocialProviderAuthException,
} from '../exceptions/social-auth.exception';
import { SocialAuthConfigType } from '../interfaces/social-auth-config.interface';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  private readonly logger = new Logger(FacebookStrategy.name);

  constructor(
    private readonly configService: ConfigService<{
      socialAuth: SocialAuthConfigType;
    }>,
  ) {
    const facebookConfig = configService.getOrThrow('socialAuth.facebook', {
      infer: true,
    });

    // Validate configuration completeness
    if (
      !facebookConfig.clientId ||
      !facebookConfig.clientSecret ||
      !facebookConfig.redirectUrl
    ) {
      throw new SocialProviderAuthException(
        SocialProviderEnum.FACEBOOK,
        'Facebook OAuth configuration is incomplete',
      );
    }

    super({
      clientID: facebookConfig.clientId,
      clientSecret: facebookConfig.clientSecret,
      callbackURL: facebookConfig.redirectUrl,
      scope: 'email',
      profileFields: ['emails', 'name', 'photos'],
      passReqToCallback: true,
    });
  }

  authenticate(req: Request, options: any): void {
    try {
      // Validate callback URL if provided
      if (req.query.callback) {
        const callbackUrl = req.query.callback as string;
        try {
          new URL(callbackUrl);
        } catch {
          this.logger.error(`Invalid callback URL provided: ${callbackUrl}`);
          throw new SocialOAuthCallbackException(
            SocialProviderEnum.FACEBOOK,
            'Invalid callback URL format',
          );
        }

        return super.authenticate(req, {
          ...options,
          state: callbackUrl,
        });
      }
      return super.authenticate(req, options);
    } catch (error) {
      this.logger.error(`Facebook authentication error: ${error.message}`);
      if (error instanceof SocialOAuthCallbackException) {
        throw error;
      }
      throw new SocialProviderAuthException(
        SocialProviderEnum.FACEBOOK,
        `Authentication failed: ${error.message}`,
      );
    }
  }

  validate(
    request: Request,
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: (err: any, user: any, info?: any) => void,
  ): any {
    try {
      // Validate access token
      if (!accessToken) {
        this.logger.error('No access token received from Facebook');
        return done(
          new SocialProviderAuthException(
            SocialProviderEnum.FACEBOOK,
            'No access token received',
          ),
          false,
        );
      }

      // Validate profile data
      if (!profile) {
        this.logger.error('No profile data received from Facebook');
        return done(
          new SocialProfileDataException(SocialProviderEnum.FACEBOOK, [
            'profile',
          ]),
          false,
        );
      }

      // Extract and validate required fields
      const { name, emails } = profile;
      const missingFields: string[] = [];

      if (!profile.id) missingFields.push('id');
      if (!emails || !emails[0] || !emails[0].value)
        missingFields.push('email');
      if (!name || !name.givenName) missingFields.push('firstName');
      if (!name || !name.familyName) missingFields.push('lastName');

      if (missingFields.length > 0) {
        this.logger.error(
          `Missing required profile fields: ${missingFields.join(', ')}`,
        );
        return done(
          new SocialProfileDataException(
            SocialProviderEnum.FACEBOOK,
            missingFields,
          ),
          false,
        );
      }

      // Validate email format
      const email = emails![0].value;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        this.logger.error(`Invalid email format received: ${email}`);
        return done(
          new SocialProfileDataException(SocialProviderEnum.FACEBOOK, [
            'email (invalid format)',
          ]),
          false,
        );
      }

      const payload = {
        id: profile.id,
        email: email,
        firstName: name!.givenName,
        lastName: name!.familyName,
        picture:
          profile.photos && profile.photos[0] ? profile.photos[0].value : '',
        accessToken,
        refreshToken: refreshToken || '',
        state: request.query.state,
      };

      this.logger.debug(
        `Facebook OAuth validation successful for user: ${payload.email}`,
      );
      done(null, payload);
    } catch (error) {
      this.logger.error(`Facebook profile validation error: ${error.message}`);
      if (
        error instanceof SocialProviderAuthException ||
        error instanceof SocialProfileDataException
      ) {
        return done(error, false);
      }
      return done(
        new SocialProviderAuthException(
          SocialProviderEnum.FACEBOOK,
          `Profile validation failed: ${error.message}`,
        ),
        false,
      );
    }
  }
}
