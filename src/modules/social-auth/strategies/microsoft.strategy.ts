import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Strategy } from 'passport-microsoft';
import { SocialProviderEnum } from '../enums/social-provider.enum';
import {
  SocialOAuthCallbackException,
  SocialProfileDataException,
  SocialProviderAuthException,
} from '../exceptions/social-auth.exception';
import { SocialAuthConfigType } from '../interfaces/social-auth-config.interface';

@Injectable()
export class MicrosoftStrategy extends PassportStrategy(Strategy, 'microsoft') {
  private readonly logger = new Logger(MicrosoftStrategy.name);

  constructor(
    private readonly configService: ConfigService<{
      socialAuth: SocialAuthConfigType;
    }>,
  ) {
    const microsoftConfig = configService.getOrThrow('socialAuth.microsoft', {
      infer: true,
    });

    // Validate configuration completeness
    if (
      !microsoftConfig.clientId ||
      !microsoftConfig.clientSecret ||
      !microsoftConfig.callbackURL ||
      !microsoftConfig.tenant
    ) {
      throw new SocialProviderAuthException(
        SocialProviderEnum.MICROSOFT,
        'Microsoft OAuth configuration is incomplete',
      );
    }

    super({
      clientID: microsoftConfig.clientId,
      clientSecret: microsoftConfig.clientSecret,
      callbackURL: microsoftConfig.callbackURL,
      tenant: microsoftConfig.tenant,
      passReqToCallback: true,
      scope: ['user.read'],
    });
  }

  authenticate(req: Request, options: any): void {
    try {
      // Validate callback URL if provided
      if (req.query.callback) {
        const callbackUrl = req.query.callback as string;
        try {
          new URL(callbackUrl);
        } catch {
          this.logger.error(`Invalid callback URL provided: ${callbackUrl}`);
          throw new SocialOAuthCallbackException(
            SocialProviderEnum.MICROSOFT,
            'Invalid callback URL format',
          );
        }

        return super.authenticate(req, {
          ...options,
          state: callbackUrl,
        });
      }
      return super.authenticate(req, options);
    } catch (error) {
      this.logger.error(`Microsoft authentication error: ${error.message}`);
      if (error instanceof SocialOAuthCallbackException) {
        throw error;
      }
      throw new SocialProviderAuthException(
        SocialProviderEnum.MICROSOFT,
        `Authentication failed: ${error.message}`,
      );
    }
  }

  validate(
    request: Request,
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: (err: any, user: any, info?: any) => void,
  ): any {
    try {
      // Validate access token
      if (!accessToken) {
        this.logger.error('No access token received from Microsoft');
        return done(
          new SocialProviderAuthException(
            SocialProviderEnum.MICROSOFT,
            'No access token received',
          ),
          false,
        );
      }

      // Validate profile data
      if (!profile) {
        this.logger.error('No profile data received from Microsoft');
        return done(
          new SocialProfileDataException(SocialProviderEnum.MICROSOFT, [
            'profile',
          ]),
          false,
        );
      }

      // Extract and validate required fields
      const { name, emails } = profile;
      const missingFields: string[] = [];

      if (!profile.id) missingFields.push('id');
      if (!emails || !emails[0] || !emails[0].value) {
        // Microsoft might use upn instead of emails
        if (!profile.upn) {
          missingFields.push('email');
        }
      }
      if (!name || !name.givenName) missingFields.push('firstName');
      if (!name || !name.familyName) missingFields.push('lastName');

      if (missingFields.length > 0) {
        this.logger.error(
          `Missing required profile fields: ${missingFields.join(', ')}`,
        );
        return done(
          new SocialProfileDataException(
            SocialProviderEnum.MICROSOFT,
            missingFields,
          ),
          false,
        );
      }

      // Get email from emails array or upn
      const email = emails && emails[0] ? emails[0].value : profile.upn;

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        this.logger.error(`Invalid email format received: ${email}`);
        return done(
          new SocialProfileDataException(SocialProviderEnum.MICROSOFT, [
            'email (invalid format)',
          ]),
          false,
        );
      }

      const user = {
        id: profile.id,
        email: email,
        firstName: name!.givenName,
        lastName: name!.familyName,
        picture:
          profile.photos && profile.photos[0] ? profile.photos[0].value : '',
        accessToken,
        refreshToken: refreshToken || '',
        state: request.query.state,
      };

      this.logger.debug(
        `Microsoft OAuth validation successful for user: ${user.email}`,
      );
      done(null, user);
    } catch (error) {
      this.logger.error(`Microsoft profile validation error: ${error.message}`);
      if (
        error instanceof SocialProviderAuthException ||
        error instanceof SocialProfileDataException
      ) {
        return done(error, false);
      }
      return done(
        new SocialProviderAuthException(
          SocialProviderEnum.MICROSOFT,
          `Profile validation failed: ${error.message}`,
        ),
        false,
      );
    }
  }
}
