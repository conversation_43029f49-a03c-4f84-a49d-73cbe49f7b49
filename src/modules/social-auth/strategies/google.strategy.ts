import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { SocialProviderEnum } from '../enums/social-provider.enum';
import {
  SocialOAuthCallbackException,
  SocialProfileDataException,
  SocialProviderAuthException,
} from '../exceptions/social-auth.exception';
import { SocialAuthConfigType } from '../interfaces/social-auth-config.interface';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new Logger(GoogleStrategy.name);

  constructor(
    private readonly configService: ConfigService<{
      socialAuth: SocialAuthConfigType;
    }>,
  ) {
    const googleConfig = configService.getOrThrow('socialAuth.google', {
      infer: true,
    });

    // Validate configuration completeness
    if (
      !googleConfig.clientId ||
      !googleConfig.clientSecret ||
      !googleConfig.callbackURL
    ) {
      throw new SocialProviderAuthException(
        SocialProviderEnum.GOOGLE,
        'Google OAuth configuration is incomplete',
      );
    }

    super({
      clientID: googleConfig.clientId,
      clientSecret: googleConfig.clientSecret,
      callbackURL: googleConfig.callbackURL,
      scope: ['email', 'profile'],
      passReqToCallback: true,
    });
  }

  authenticate(req: Request, options?: any): void {
    try {
      // Validate callback URL if provided
      if (req.query.callback) {
        const callbackUrl = req.query.callback as string;
        try {
          new URL(callbackUrl);
        } catch {
          this.logger.error(`Invalid callback URL provided: ${callbackUrl}`);
          throw new SocialOAuthCallbackException(
            SocialProviderEnum.GOOGLE,
            'Invalid callback URL format',
          );
        }

        return super.authenticate(req, {
          ...options,
          state: callbackUrl,
        });
      }
      return super.authenticate(req, options);
    } catch (error) {
      this.logger.error(`Google authentication error: ${error.message}`);
      if (error instanceof SocialOAuthCallbackException) {
        throw error;
      }
      throw new SocialProviderAuthException(
        SocialProviderEnum.GOOGLE,
        `Authentication failed: ${error.message}`,
      );
    }
  }

  validate(
    request: Request,
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): any {
    try {
      // Validate access token
      if (!accessToken) {
        this.logger.error('No access token received from Google');
        return done(
          new SocialProviderAuthException(
            SocialProviderEnum.GOOGLE,
            'No access token received',
          ),
          false,
        );
      }

      // Validate profile data
      if (!profile) {
        this.logger.error('No profile data received from Google');
        return done(
          new SocialProfileDataException(SocialProviderEnum.GOOGLE, [
            'profile',
          ]),
          false,
        );
      }

      // Extract and validate required fields
      const { name, emails, photos } = profile;
      const missingFields: string[] = [];

      if (!profile.id) missingFields.push('id');
      if (!emails || !emails[0] || !emails[0].value)
        missingFields.push('email');
      if (!name || !name.givenName) missingFields.push('firstName');
      if (!name || !name.familyName) missingFields.push('lastName');

      if (missingFields.length > 0) {
        this.logger.error(
          `Missing required profile fields: ${missingFields.join(', ')}`,
        );
        return done(
          new SocialProfileDataException(
            SocialProviderEnum.GOOGLE,
            missingFields,
          ),
          false,
        );
      }

      // Validate email format
      const email = emails![0].value;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        this.logger.error(`Invalid email format received: ${email}`);
        return done(
          new SocialProfileDataException(SocialProviderEnum.GOOGLE, [
            'email (invalid format)',
          ]),
          false,
        );
      }

      const user = {
        id: profile.id,
        email: email,
        firstName: name!.givenName,
        lastName: name!.familyName,
        picture: photos && photos[0] ? photos[0].value : '',
        accessToken,
        refreshToken: refreshToken || '',
        state: request.query.state,
      };

      this.logger.debug(
        `Google OAuth validation successful for user: ${user.email}`,
      );
      done(null, user);
    } catch (error) {
      this.logger.error(`Google profile validation error: ${error.message}`);
      if (
        error instanceof SocialProviderAuthException ||
        error instanceof SocialProfileDataException
      ) {
        return done(error, false);
      }
      return done(
        new SocialProviderAuthException(
          SocialProviderEnum.GOOGLE,
          `Profile validation failed: ${error.message}`,
        ),
        false,
      );
    }
  }
}
