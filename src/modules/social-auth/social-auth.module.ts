import { AuthModule } from '@app/modules/auth/auth.module';
import { SessionEntity } from '@app/modules/auth/entities/session.entity';
import { SOCIAL_AUTH_MODULE_OPTIONS } from '@app/modules/social-auth/social-auth.constant';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SocialAuthController } from './controllers/social-auth.controller';
import { SocialEntity } from './entities/social.entity';
import { SocialProviderEnum } from './enums/social-provider.enum';
import { FacebookAuthGuard } from './guards/facebook-auth.guard';
import { GoogleAuthGuard } from './guards/google-auth.guard';
import { MicrosoftAuthGuard } from './guards/microsoft-auth.guard';
import {
  SocialAuthConfigType,
  SocialAuthModuleAsyncOptions,
  SocialAuthModuleOptions,
} from './interfaces/social-auth-config.interface';
import { SocialAuthService } from './services/social-auth.service';
import {
  socialAuthConfig,
  validateSocialAuthConfig,
} from './social-auth.config';
import { FacebookStrategy } from './strategies/facebook.strategy';
import { GoogleStrategy } from './strategies/google.strategy';
import { MicrosoftStrategy } from './strategies/microsoft.strategy';

@Module({})
export class SocialAuthModule {
  static forRoot(options: SocialAuthModuleOptions): DynamicModule {
    // Validate configuration before creating providers
    this.validateModuleConfiguration(options);

    const providers = this.createProviders(options);

    return {
      module: SocialAuthModule,
      imports: [
        ConfigModule.forRoot({
          load: [socialAuthConfig],
        }),
        TypeOrmModule.forFeature([SocialEntity, UserEntity, SessionEntity]),
        AuthModule,
      ],
      controllers: [SocialAuthController],
      providers: [
        {
          provide: SOCIAL_AUTH_MODULE_OPTIONS,
          useValue: options,
        },
        SocialAuthService,
        ...providers,
      ],
      exports: [
        SocialAuthService,
        SOCIAL_AUTH_MODULE_OPTIONS,
        ...providers.map((p) =>
          typeof p === 'object' && 'provide' in p ? p.provide : p,
        ),
      ],
    };
  }

  static async forRootAsync(
    options: SocialAuthModuleAsyncOptions,
  ): Promise<DynamicModule> {
    // Create a temporary context to resolve the async configuration
    const { NestFactory } = await import('@nestjs/core');
    const tempApp = await NestFactory.createApplicationContext({
      module: class TempModule {},
      imports: options.imports || [],
      providers: this.createAsyncProviders(options),
    });

    try {
      // Resolve the configuration
      const config = await tempApp.get(SOCIAL_AUTH_MODULE_OPTIONS);

      // Validate configuration before creating providers
      this.validateModuleConfiguration(config);

      // Create providers based on resolved configuration
      const dynamicProviders = this.createProviders(config);

      return {
        module: SocialAuthModule,
        imports: [
          ConfigModule.forRoot({
            load: [socialAuthConfig],
          }),
          TypeOrmModule.forFeature([SocialEntity, UserEntity, SessionEntity]),
          ...(options.imports || []),
          AuthModule,
        ],
        controllers: [SocialAuthController],
        providers: [
          {
            provide: SOCIAL_AUTH_MODULE_OPTIONS,
            useValue: config,
          },
          SocialAuthService,
          ...dynamicProviders,
        ],
        exports: [
          SocialAuthService,
          SOCIAL_AUTH_MODULE_OPTIONS,
          ...dynamicProviders.map((p) =>
            typeof p === 'object' && 'provide' in p ? p.provide : p,
          ),
        ],
      };
    } finally {
      await tempApp.close();
    }
  }

  private static createProviders(options: SocialAuthModuleOptions): Provider[] {
    const providers: Provider[] = [];

    // Add conditional providers based on enabled providers
    if (this.isProviderEnabled(SocialProviderEnum.GOOGLE, options)) {
      providers.push(...this.createGoogleProviders());
    }

    if (this.isProviderEnabled(SocialProviderEnum.FACEBOOK, options)) {
      providers.push(...this.createFacebookProviders());
    }

    if (this.isProviderEnabled(SocialProviderEnum.MICROSOFT, options)) {
      providers.push(...this.createMicrosoftProviders());
    }

    return providers;
  }

  private static createAsyncProviders(
    options: SocialAuthModuleAsyncOptions,
  ): Provider[] {
    return [
      {
        provide: SOCIAL_AUTH_MODULE_OPTIONS,
        useFactory: options.useFactory,
        inject: options.inject || [],
      },
    ];
  }

  /**
   * Validate module configuration and enabled providers
   */
  private static validateModuleConfiguration(
    options: SocialAuthModuleOptions,
  ): void {
    if (!options.providers) {
      throw new Error('SocialAuthModule: providers configuration is required');
    }

    // If enabledProviders is specified, validate each enabled provider has required config
    if (options.enabledProviders && options.enabledProviders.length > 0) {
      const configToValidate: SocialAuthConfigType = {
        google: options.providers.google,
        facebook: options.providers.facebook,
        microsoft: options.providers.microsoft,
      };

      validateSocialAuthConfig(configToValidate, options.enabledProviders);
    } else {
      // If no enabledProviders specified, validate any provider that has partial config
      this.validatePartialConfigurations(options);
    }
  }

  /**
   * Validate partial configurations to ensure complete setup
   */
  private static validatePartialConfigurations(
    options: SocialAuthModuleOptions,
  ): void {
    const { google, facebook, microsoft } = options.providers;

    // Check Google configuration
    if (
      google &&
      (!google.clientId || !google.clientSecret || !google.callbackURL)
    ) {
      throw new Error(
        'SocialAuthModule: Google provider configuration is incomplete. ' +
          'Required: clientId, clientSecret, callbackURL',
      );
    }

    // Check Facebook configuration
    if (
      facebook &&
      (!facebook.clientId || !facebook.clientSecret || !facebook.redirectUrl)
    ) {
      throw new Error(
        'SocialAuthModule: Facebook provider configuration is incomplete. ' +
          'Required: clientId, clientSecret, redirectUrl',
      );
    }

    // Check Microsoft configuration
    if (
      microsoft &&
      (!microsoft.clientId ||
        !microsoft.clientSecret ||
        !microsoft.callbackURL ||
        !microsoft.tenant)
    ) {
      throw new Error(
        'SocialAuthModule: Microsoft provider configuration is incomplete. ' +
          'Required: clientId, clientSecret, callbackURL, tenant',
      );
    }
  }

  /**
   * Create Google provider factories
   */
  private static createGoogleProviders(): Provider[] {
    return [
      {
        provide: GoogleStrategy,
        useFactory: (configService: ConfigService) => {
          try {
            // Validate Google configuration exists
            const googleConfig = configService.get('socialAuth.google');
            if (
              !googleConfig?.clientId ||
              !googleConfig?.clientSecret ||
              !googleConfig?.callbackURL
            ) {
              throw new Error(
                'Google OAuth configuration is incomplete or missing',
              );
            }

            // Validate dependency availability
            try {
              require.resolve('passport-google-oauth20');
            } catch {
              throw new Error(
                'Missing required dependency: passport-google-oauth20. ' +
                  'Install with: npm install passport-google-oauth20 @types/passport-google-oauth20',
              );
            }

            return new GoogleStrategy(configService);
          } catch (error) {
            throw new Error(
              `SocialAuthModule: Failed to create Google strategy. ${error.message}`,
            );
          }
        },
        inject: [ConfigService],
      },
      GoogleAuthGuard,
    ];
  }

  /**
   * Create Facebook provider factories
   */
  private static createFacebookProviders(): Provider[] {
    return [
      {
        provide: FacebookStrategy,
        useFactory: (configService: ConfigService) => {
          try {
            // Validate Facebook configuration exists
            const facebookConfig = configService.get('socialAuth.facebook');
            if (
              !facebookConfig?.clientId ||
              !facebookConfig?.clientSecret ||
              !facebookConfig?.redirectUrl
            ) {
              throw new Error(
                'Facebook OAuth configuration is incomplete or missing',
              );
            }

            // Validate dependency availability
            try {
              require.resolve('passport-facebook');
            } catch {
              throw new Error(
                'Missing required dependency: passport-facebook. ' +
                  'Install with: npm install passport-facebook @types/passport-facebook',
              );
            }

            return new FacebookStrategy(configService);
          } catch (error) {
            throw new Error(
              `SocialAuthModule: Failed to create Facebook strategy. ${error.message}`,
            );
          }
        },
        inject: [ConfigService],
      },
      FacebookAuthGuard,
    ];
  }

  /**
   * Create Microsoft provider factories
   */
  private static createMicrosoftProviders(): Provider[] {
    return [
      {
        provide: MicrosoftStrategy,
        useFactory: (configService: ConfigService) => {
          try {
            // Validate Microsoft configuration exists
            const microsoftConfig = configService.get('socialAuth.microsoft');
            if (
              !microsoftConfig?.clientId ||
              !microsoftConfig?.clientSecret ||
              !microsoftConfig?.callbackURL ||
              !microsoftConfig?.tenant
            ) {
              throw new Error(
                'Microsoft OAuth configuration is incomplete or missing',
              );
            }

            // Validate dependency availability
            try {
              require.resolve('passport-microsoft');
            } catch {
              throw new Error(
                'Missing required dependency: passport-microsoft. ' +
                  'Install with: npm install passport-microsoft',
              );
            }

            return new MicrosoftStrategy(configService);
          } catch (error) {
            throw new Error(
              `SocialAuthModule: Failed to create Microsoft strategy. ${error.message}`,
            );
          }
        },
        inject: [ConfigService],
      },
      MicrosoftAuthGuard,
    ];
  }

  private static isProviderEnabled(
    provider: SocialProviderEnum,
    options: SocialAuthModuleOptions,
  ): boolean {
    if (!options.enabledProviders) {
      // If no specific providers are enabled, check if config exists and is complete
      switch (provider) {
        case SocialProviderEnum.GOOGLE:
          return !!(
            options.providers.google?.clientId &&
            options.providers.google?.clientSecret &&
            options.providers.google?.callbackURL
          );
        case SocialProviderEnum.FACEBOOK:
          return !!(
            options.providers.facebook?.clientId &&
            options.providers.facebook?.clientSecret &&
            options.providers.facebook?.redirectUrl
          );
        case SocialProviderEnum.MICROSOFT:
          return !!(
            options.providers.microsoft?.clientId &&
            options.providers.microsoft?.clientSecret &&
            options.providers.microsoft?.callbackURL &&
            options.providers.microsoft?.tenant
          );
        default:
          return false;
      }
    }

    return options.enabledProviders.includes(provider);
  }
}
