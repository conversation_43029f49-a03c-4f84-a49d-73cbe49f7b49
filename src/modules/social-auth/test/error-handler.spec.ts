import { SocialProviderEnum } from '../enums/social-provider.enum';
import { SocialAuthErrorHandler } from '../utils/error-handler.util';

// Mock the logger to avoid console output during tests
jest.mock('@nestjs/common', () => ({
  Logger: jest.fn().mockImplementation(() => ({
    error: jest.fn(),
    log: jest.fn(),
    debug: jest.fn(),
  })),
}));

describe('SocialAuthErrorHandler', () => {
  describe('handleProviderServiceError', () => {
    it('should handle network connection errors', () => {
      const error = {
        code: 'ENOTFOUND',
        message: 'getaddrinfo ENOTFOUND api.google.com',
      };
      const exception = SocialAuthErrorHandler.handleProviderServiceError(
        SocialProviderEnum.GOOGLE,
        error,
        'token exchange',
      );

      expect(exception.message).toContain('google');
      expect(exception.message).toContain('Network connection failed');
    });

    it('should handle HTTP status errors', () => {
      const error = {
        response: { status: 429 },
        message: 'Rate limit exceeded',
      };
      const exception = SocialAuthErrorHandler.handleProviderServiceError(
        SocialProviderEnum.FACEBOOK,
        error,
      );

      expect(exception.message).toContain('facebook');
      expect(exception.message).toContain('Rate limit exceeded');
    });

    it('should handle timeout errors', () => {
      const error = { code: 'ETIMEDOUT', message: 'Request timeout' };
      const exception = SocialAuthErrorHandler.handleProviderServiceError(
        SocialProviderEnum.MICROSOFT,
        error,
      );

      expect(exception.message).toContain('microsoft');
      expect(exception.message).toContain('Request timeout');
    });

    it('should handle 401 unauthorized errors', () => {
      const error = { response: { status: 401 }, message: 'Unauthorized' };
      const exception = SocialAuthErrorHandler.handleProviderServiceError(
        SocialProviderEnum.GOOGLE,
        error,
      );

      expect(exception.message).toContain('Invalid credentials');
    });

    it('should handle 403 forbidden errors', () => {
      const error = { response: { status: 403 }, message: 'Forbidden' };
      const exception = SocialAuthErrorHandler.handleProviderServiceError(
        SocialProviderEnum.FACEBOOK,
        error,
      );

      expect(exception.message).toContain('Access forbidden');
    });
  });

  describe('handleOAuthCallbackError', () => {
    it('should map access_denied error', () => {
      const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
        SocialProviderEnum.GOOGLE,
        'access_denied',
      );

      expect(exception.message).toContain('User denied access');
      expect(exception.message).toContain('cancelled the authorization');
    });

    it('should map server_error', () => {
      const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
        SocialProviderEnum.FACEBOOK,
        'server_error',
        'Internal server error',
      );

      expect(exception.message).toContain('Provider server error');
      expect(exception.message).toContain('unexpected condition');
    });

    it('should map invalid_request error', () => {
      const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
        SocialProviderEnum.MICROSOFT,
        'invalid_request',
      );

      expect(exception.message).toContain('Invalid OAuth request');
      expect(exception.message).toContain('malformed or invalid');
    });

    it('should map unauthorized_client error', () => {
      const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
        SocialProviderEnum.GOOGLE,
        'unauthorized_client',
      );

      expect(exception.message).toContain('Unauthorized client');
      expect(exception.message).toContain('not authorized to use');
    });

    it('should handle unknown errors', () => {
      const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
        SocialProviderEnum.MICROSOFT,
        'unknown_error',
        'Something went wrong',
      );

      expect(exception.message).toContain('unknown_error');
      expect(exception.message).toContain('Something went wrong');
    });
  });

  describe('validateCallbackUrl', () => {
    it('should validate correct HTTPS URL', () => {
      const url = SocialAuthErrorHandler.validateCallbackUrl(
        'https://example.com/callback',
      );
      expect(url).toBeInstanceOf(URL);
      expect(url.protocol).toBe('https:');
      expect(url.hostname).toBe('example.com');
    });

    it('should validate correct HTTP URL', () => {
      const url = SocialAuthErrorHandler.validateCallbackUrl(
        'http://localhost:3000/callback',
      );
      expect(url).toBeInstanceOf(URL);
      expect(url.protocol).toBe('http:');
      expect(url.hostname).toBe('localhost');
    });

    it('should reject invalid protocols', () => {
      expect(() => {
        SocialAuthErrorHandler.validateCallbackUrl(
          'ftp://example.com/callback',
        );
      }).toThrow();
    });

    it('should reject malformed URLs', () => {
      expect(() => {
        SocialAuthErrorHandler.validateCallbackUrl('not-a-url');
      }).toThrow();
    });

    it('should validate against allowed domains', () => {
      const url = SocialAuthErrorHandler.validateCallbackUrl(
        'https://app.example.com/callback',
        ['example.com', 'localhost'],
      );
      expect(url).toBeInstanceOf(URL);
      expect(url.hostname).toBe('app.example.com');
    });

    it('should reject URLs not in allowed domains', () => {
      expect(() => {
        SocialAuthErrorHandler.validateCallbackUrl(
          'https://malicious.com/callback',
          ['example.com', 'localhost'],
        );
      }).toThrow();
    });

    it('should allow exact domain matches', () => {
      const url = SocialAuthErrorHandler.validateCallbackUrl(
        'https://example.com/callback',
        ['example.com'],
      );
      expect(url).toBeInstanceOf(URL);
    });

    it('should allow subdomain matches', () => {
      const url = SocialAuthErrorHandler.validateCallbackUrl(
        'https://api.example.com/callback',
        ['example.com'],
      );
      expect(url).toBeInstanceOf(URL);
    });
  });

  describe('isRetryableError', () => {
    it('should identify network errors as retryable', () => {
      const error = { code: 'ENOTFOUND' };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
    });

    it('should identify connection refused as retryable', () => {
      const error = { code: 'ECONNREFUSED' };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
    });

    it('should identify timeout errors as retryable', () => {
      const error = { code: 'ETIMEDOUT' };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
    });

    it('should identify 5xx status codes as retryable', () => {
      const error = { response: { status: 503 } };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
    });

    it('should identify rate limit as retryable', () => {
      const error = { response: { status: 429 } };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
    });

    it('should identify all 5xx errors as retryable', () => {
      [500, 502, 503, 504].forEach((status) => {
        const error = { response: { status } };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
      });
    });

    it('should not identify 4xx client errors as retryable', () => {
      const error = { response: { status: 400 } };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(false);
    });

    it('should not identify auth errors as retryable', () => {
      const error = { response: { status: 401 } };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(false);
    });

    it('should not identify forbidden errors as retryable', () => {
      const error = { response: { status: 403 } };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(false);
    });

    it('should identify temporarily unavailable messages as retryable', () => {
      const error = { message: 'Service temporarily unavailable' };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
    });

    it('should not identify generic errors as retryable', () => {
      const error = { message: 'Generic error' };
      expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(false);
    });
  });

  describe('logSuccess', () => {
    it('should log successful operations', () => {
      // This test mainly ensures the method doesn't throw
      expect(() => {
        SocialAuthErrorHandler.logSuccess(
          SocialProviderEnum.GOOGLE,
          'OAuth login',
          'user123',
          { ip: '127.0.0.1' },
        );
      }).not.toThrow();
    });

    it('should log without user ID', () => {
      expect(() => {
        SocialAuthErrorHandler.logSuccess(
          SocialProviderEnum.FACEBOOK,
          'Configuration validation',
        );
      }).not.toThrow();
    });

    it('should log without additional info', () => {
      expect(() => {
        SocialAuthErrorHandler.logSuccess(
          SocialProviderEnum.MICROSOFT,
          'Strategy initialization',
          'user456',
        );
      }).not.toThrow();
    });
  });
});
