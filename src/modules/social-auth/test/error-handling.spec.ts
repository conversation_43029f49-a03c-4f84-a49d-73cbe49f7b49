// Mock the dependencies
jest.mock('@app/common/exception/http-error.exception', () => ({
  HttpErrorException: class MockHttpErrorException extends Error {
    constructor(errorCode: any) {
      super(errorCode.message);
      this.name = 'HttpErrorException';
    }
  },
}));

jest.mock('@app/common/types', () => ({
  ErrorCode: {},
}));

// Import after mocking
import { SocialProviderEnum } from '../enums/social-provider.enum';
import {
  SocialAuthException,
  SocialOAuthCallbackException,
  SocialProfileDataException,
  SocialProviderConfigException,
  SocialProviderDependencyException,
  SocialProviderServiceException,
  UnsupportedSocialProviderException,
} from '../exceptions/social-auth.exception';
import { SOCIAL_AUTH_ERROR_CODES } from '../social-auth.error-codes';
import { SocialAuthErrorHandler } from '../utils/error-handler.util';

// Mock configuration validation function
const validateSocialAuthConfig = jest.fn();

// Mock config type
interface SocialAuthConfigType {
  google?: {
    clientId: string;
    clientSecret: string;
    callbackURL: string;
  };
  facebook?: {
    clientId: string;
    clientSecret: string;
    redirectUrl: string;
  };
  microsoft?: {
    clientId: string;
    clientSecret: string;
    callbackURL: string;
    tenant: string;
  };
}

describe('Social Auth Error Handling', () => {
  describe('Error Codes', () => {
    it('should have all required error codes', () => {
      expect(SOCIAL_AUTH_ERROR_CODES.PROVIDER_NOT_CONFIGURED).toBeDefined();
      expect(SOCIAL_AUTH_ERROR_CODES.PROVIDER_NOT_SUPPORTED).toBeDefined();
      expect(SOCIAL_AUTH_ERROR_CODES.MISSING_PROVIDER_DEPENDENCY).toBeDefined();
      expect(SOCIAL_AUTH_ERROR_CODES.INVALID_PROVIDER_CONFIG).toBeDefined();
      expect(SOCIAL_AUTH_ERROR_CODES.OAUTH_CALLBACK_ERROR).toBeDefined();
      expect(
        SOCIAL_AUTH_ERROR_CODES.SOCIAL_ACCOUNT_ALREADY_LINKED,
      ).toBeDefined();
      expect(SOCIAL_AUTH_ERROR_CODES.INVALID_AUTHORIZATION_CODE).toBeDefined();
    });

    it('should have proper error code format', () => {
      const errorCode = SOCIAL_AUTH_ERROR_CODES.PROVIDER_NOT_CONFIGURED;
      expect(errorCode.code).toMatch(/^SOCIAL_AUTH:\d+$/);
      expect(errorCode.statusCode).toBeDefined();
      expect(errorCode.message).toBeDefined();
    });
  });

  describe('Exception Classes', () => {
    it('should create SocialProviderConfigException with proper message', () => {
      const exception = new SocialProviderConfigException('google', [
        'clientId',
        'clientSecret',
      ]);
      expect(exception.message).toContain('google');
      expect(exception.message).toContain('clientId');
      expect(exception.message).toContain('clientSecret');
    });

    it('should create SocialProviderDependencyException with install command', () => {
      const exception = new SocialProviderDependencyException(
        'google',
        'passport-google-oauth20',
        'npm install passport-google-oauth20',
      );
      expect(exception.message).toContain('google');
      expect(exception.message).toContain('passport-google-oauth20');
      expect(exception.message).toContain('npm install');
    });

    it('should create SocialOAuthCallbackException with provider and error', () => {
      const exception = new SocialOAuthCallbackException(
        'facebook',
        'access_denied',
        'User denied permission',
      );
      expect(exception.message).toContain('facebook');
      expect(exception.message).toContain('access_denied');
      expect(exception.message).toContain('User denied permission');
    });

    it('should create SocialProfileDataException with missing fields', () => {
      const exception = new SocialProfileDataException('microsoft', [
        'email',
        'firstName',
      ]);
      expect(exception.message).toContain('microsoft');
      expect(exception.message).toContain('email');
      expect(exception.message).toContain('firstName');
    });

    it('should create UnsupportedSocialProviderException with supported providers', () => {
      const exception = new UnsupportedSocialProviderException('linkedin', [
        'google',
        'facebook',
        'microsoft',
      ]);
      expect(exception.message).toContain('linkedin');
      expect(exception.message).toContain('google, facebook, microsoft');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate configuration structure', () => {
      // Test basic configuration validation logic
      const config: SocialAuthConfigType = {
        google: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          callbackURL: 'https://example.com/callback',
        },
      };

      expect(config.google?.clientId).toBeDefined();
      expect(config.google?.clientSecret).toBeDefined();
      expect(config.google?.callbackURL).toBeDefined();
    });

    it('should identify incomplete configurations', () => {
      const incompleteConfig: SocialAuthConfigType = {
        google: {
          clientId: 'test',
          clientSecret: '',
          callbackURL: 'http://localhost:3000/callback',
        },
      };

      expect(incompleteConfig.google?.clientSecret).toBeFalsy();
    });
  });

  describe('Error Handler Utility', () => {
    describe('handleProviderServiceError', () => {
      it('should handle network connection errors', () => {
        const error = {
          code: 'ENOTFOUND',
          message: 'getaddrinfo ENOTFOUND api.google.com',
        };
        const exception = SocialAuthErrorHandler.handleProviderServiceError(
          SocialProviderEnum.GOOGLE,
          error,
          'token exchange',
        );

        expect(exception).toBeInstanceOf(SocialProviderServiceException);
        expect(exception.message).toContain('google');
        expect(exception.message).toContain('Network connection failed');
      });

      it('should handle HTTP status errors', () => {
        const error = {
          response: { status: 429 },
          message: 'Rate limit exceeded',
        };
        const exception = SocialAuthErrorHandler.handleProviderServiceError(
          SocialProviderEnum.FACEBOOK,
          error,
        );

        expect(exception).toBeInstanceOf(SocialProviderServiceException);
        expect(exception.message).toContain('facebook');
        expect(exception.message).toContain('Rate limit exceeded');
      });

      it('should handle timeout errors', () => {
        const error = { code: 'ETIMEDOUT', message: 'Request timeout' };
        const exception = SocialAuthErrorHandler.handleProviderServiceError(
          SocialProviderEnum.MICROSOFT,
          error,
        );

        expect(exception).toBeInstanceOf(SocialProviderServiceException);
        expect(exception.message).toContain('microsoft');
        expect(exception.message).toContain('Request timeout');
      });
    });

    describe('handleOAuthCallbackError', () => {
      it('should map access_denied error', () => {
        const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
          SocialProviderEnum.GOOGLE,
          'access_denied',
        );

        expect(exception).toBeInstanceOf(SocialOAuthCallbackException);
        expect(exception.message).toContain('User denied access');
        expect(exception.message).toContain('cancelled the authorization');
      });

      it('should map server_error', () => {
        const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
          SocialProviderEnum.FACEBOOK,
          'server_error',
          'Internal server error',
        );

        expect(exception).toBeInstanceOf(SocialOAuthCallbackException);
        expect(exception.message).toContain('Provider server error');
        expect(exception.message).toContain('unexpected condition');
      });

      it('should handle unknown errors', () => {
        const exception = SocialAuthErrorHandler.handleOAuthCallbackError(
          SocialProviderEnum.MICROSOFT,
          'unknown_error',
          'Something went wrong',
        );

        expect(exception).toBeInstanceOf(SocialOAuthCallbackException);
        expect(exception.message).toContain('unknown_error');
        expect(exception.message).toContain('Something went wrong');
      });
    });

    describe('validateCallbackUrl', () => {
      it('should validate correct HTTPS URL', () => {
        const url = SocialAuthErrorHandler.validateCallbackUrl(
          'https://example.com/callback',
        );
        expect(url).toBeInstanceOf(URL);
        expect(url.protocol).toBe('https:');
      });

      it('should validate correct HTTP URL', () => {
        const url = SocialAuthErrorHandler.validateCallbackUrl(
          'http://localhost:3000/callback',
        );
        expect(url).toBeInstanceOf(URL);
        expect(url.protocol).toBe('http:');
      });

      it('should reject invalid protocols', () => {
        expect(() => {
          SocialAuthErrorHandler.validateCallbackUrl(
            'ftp://example.com/callback',
          );
        }).toThrow(SocialOAuthCallbackException);
      });

      it('should reject malformed URLs', () => {
        expect(() => {
          SocialAuthErrorHandler.validateCallbackUrl('not-a-url');
        }).toThrow(SocialOAuthCallbackException);
      });

      it('should validate against allowed domains', () => {
        const url = SocialAuthErrorHandler.validateCallbackUrl(
          'https://app.example.com/callback',
          ['example.com', 'localhost'],
        );
        expect(url).toBeInstanceOf(URL);
      });

      it('should reject URLs not in allowed domains', () => {
        expect(() => {
          SocialAuthErrorHandler.validateCallbackUrl(
            'https://malicious.com/callback',
            ['example.com', 'localhost'],
          );
        }).toThrow(SocialOAuthCallbackException);
      });
    });

    describe('isRetryableError', () => {
      it('should identify network errors as retryable', () => {
        const error = { code: 'ENOTFOUND' };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
      });

      it('should identify timeout errors as retryable', () => {
        const error = { code: 'ETIMEDOUT' };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
      });

      it('should identify 5xx status codes as retryable', () => {
        const error = { response: { status: 503 } };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
      });

      it('should identify rate limit as retryable', () => {
        const error = { response: { status: 429 } };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(true);
      });

      it('should not identify 4xx client errors as retryable', () => {
        const error = { response: { status: 400 } };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(false);
      });

      it('should not identify auth errors as retryable', () => {
        const error = { response: { status: 401 } };
        expect(SocialAuthErrorHandler.isRetryableError(error)).toBe(false);
      });
    });

    describe('handleGenericError', () => {
      it('should pass through SocialAuthException', () => {
        const originalError = new SocialAuthException(
          SOCIAL_AUTH_ERROR_CODES.PROVIDER_NOT_CONFIGURED,
        );
        const result = SocialAuthErrorHandler.handleGenericError(
          originalError,
          'test context',
        );

        expect(result).toBe(originalError);
      });

      it('should map ValidationError', () => {
        const error = { name: 'ValidationError', message: 'Invalid input' };
        const result = SocialAuthErrorHandler.handleGenericError(
          error,
          'validation',
        );

        expect(result).toBeInstanceOf(SocialAuthException);
        expect(result.message).toContain('Validation failed');
      });

      it('should map TypeError for missing properties', () => {
        const error = {
          name: 'TypeError',
          message: "Cannot read property 'email' of undefined",
        };
        const result = SocialAuthErrorHandler.handleGenericError(
          error,
          'profile parsing',
        );

        expect(result).toBeInstanceOf(SocialAuthException);
        expect(result.message).toContain('Missing required data');
      });

      it('should handle generic errors', () => {
        const error = { name: 'Error', message: 'Something went wrong' };
        const result = SocialAuthErrorHandler.handleGenericError(
          error,
          'operation',
        );

        expect(result).toBeInstanceOf(SocialAuthException);
        expect(result.message).toContain('operation failed');
      });
    });
  });
});
