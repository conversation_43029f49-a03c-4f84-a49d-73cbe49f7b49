import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const SOCIAL_AUTH_NAMESPACE = 'SOCIAL_AUTH';

type SocialAuthErrorCodes =
  | 'PROVIDER_NOT_CONFIGURED'
  | 'PROVIDER_NOT_SUPPORTED'
  | 'MISSING_PROVIDER_DEPENDENCY'
  | 'INVALID_PROVIDER_CONFIG'
  | 'OAUTH_CALLBACK_ERROR'
  | 'OAUTH_STATE_MISMATCH'
  | 'OAUTH_ACCESS_DENIED'
  | 'SOCIAL_ACCOUNT_ALREADY_LINKED'
  | 'SOCIAL_ACCOUNT_NOT_FOUND'
  | 'INVALID_AUTHORIZATION_CODE'
  | 'PROVIDER_AUTHENTICATION_FAILED'
  | 'PROFILE_DATA_INCOMPLETE'
  | 'SOCIAL_EMAIL_MISMATCH'
  | 'PROVIDER_SERVICE_UNAVAILABLE';

export const SOCIAL_AUTH_ERROR_CODES: Record<SocialAuthErrorCodes, ErrorCode> =
  {
    PROVIDER_NOT_CONFIGURED: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20001`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Social provider is not configured or enabled',
    },
    PROVIDER_NOT_SUPPORTED: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20002`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Social provider is not supported',
    },
    MISSING_PROVIDER_DEPENDENCY: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20003`,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Required social provider dependency is missing',
    },
    INVALID_PROVIDER_CONFIG: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20004`,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Social provider configuration is invalid or incomplete',
    },
    OAUTH_CALLBACK_ERROR: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20005`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'OAuth callback failed or returned an error',
    },
    OAUTH_STATE_MISMATCH: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20006`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'OAuth state parameter mismatch - possible CSRF attack',
    },
    OAUTH_ACCESS_DENIED: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20007`,
      statusCode: HttpStatus.UNAUTHORIZED,
      message: 'User denied access to social provider',
    },
    SOCIAL_ACCOUNT_ALREADY_LINKED: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20008`,
      statusCode: HttpStatus.CONFLICT,
      message: 'Social account is already linked to another user',
    },
    SOCIAL_ACCOUNT_NOT_FOUND: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20009`,
      statusCode: HttpStatus.NOT_FOUND,
      message: 'Social account not found',
    },
    INVALID_AUTHORIZATION_CODE: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20010`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Invalid or expired authorization code',
    },
    PROVIDER_AUTHENTICATION_FAILED: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20011`,
      statusCode: HttpStatus.UNAUTHORIZED,
      message: 'Authentication with social provider failed',
    },
    PROFILE_DATA_INCOMPLETE: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20012`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Social provider profile data is incomplete or invalid',
    },
    SOCIAL_EMAIL_MISMATCH: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20013`,
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Social account email does not match user email',
    },
    PROVIDER_SERVICE_UNAVAILABLE: {
      code: `${SOCIAL_AUTH_NAMESPACE}:20014`,
      statusCode: HttpStatus.SERVICE_UNAVAILABLE,
      message: 'Social provider service is temporarily unavailable',
    },
  };
