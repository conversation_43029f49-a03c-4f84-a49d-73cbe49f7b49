import { Logger } from '@nestjs/common';
import { SocialProviderEnum } from '../enums/social-provider.enum';
import {
  SocialAuthException,
  SocialOAuthCallbackException,
  SocialProviderAuthException,
  SocialProviderServiceException,
} from '../exceptions/social-auth.exception';
import { SOCIAL_AUTH_ERROR_CODES } from '../social-auth.error-codes';

/**
 * Utility class for handling social auth errors consistently
 */
export class SocialAuthErrorHandler {
  private static readonly logger = new Logger(SocialAuthErrorHandler.name);

  /**
   * Handle OAuth provider service errors (network, API errors)
   * @param provider Social provider
   * @param error Original error
   * @param context Additional context for logging
   * @returns Appropriate social auth exception
   */
  static handleProviderServiceError(
    provider: SocialProviderEnum,
    error: any,
    context?: string,
  ): SocialProviderServiceException {
    const contextMsg = context ? ` (${context})` : '';
    this.logger.error(
      `Provider service error for ${provider}${contextMsg}: ${error.message}`,
      error.stack,
    );

    // Extract HTTP status code if available
    let statusCode: number | undefined;
    if (error.response?.status) {
      statusCode = error.response.status;
    } else if (error.status) {
      statusCode = error.status;
    }

    // Determine error details based on error type
    let details = error.message;
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      details = 'Network connection failed';
    } else if (error.code === 'ETIMEDOUT') {
      details = 'Request timeout';
    } else if (statusCode === 429) {
      details = 'Rate limit exceeded';
    } else if (statusCode === 401) {
      details = 'Invalid credentials';
    } else if (statusCode === 403) {
      details = 'Access forbidden';
    }

    return new SocialProviderServiceException(provider, statusCode, details);
  }

  /**
   * Handle OAuth callback errors with proper error mapping
   * @param provider Social provider
   * @param error OAuth error from provider
   * @param description Optional error description
   * @returns Appropriate OAuth callback exception
   */
  static handleOAuthCallbackError(
    provider: SocialProviderEnum,
    error: string,
    description?: string,
  ): SocialOAuthCallbackException {
    this.logger.error(
      `OAuth callback error for ${provider}: ${error}${description ? ` - ${description}` : ''}`,
    );

    // Map common OAuth errors to user-friendly messages
    let mappedError = error;
    let mappedDescription = description;

    switch (error) {
      case 'access_denied':
        mappedError = 'User denied access';
        mappedDescription = 'The user cancelled the authorization process';
        break;
      case 'invalid_request':
        mappedError = 'Invalid OAuth request';
        mappedDescription = 'The OAuth request was malformed or invalid';
        break;
      case 'unauthorized_client':
        mappedError = 'Unauthorized client';
        mappedDescription =
          'The client is not authorized to use this authorization method';
        break;
      case 'unsupported_response_type':
        mappedError = 'Unsupported response type';
        mappedDescription =
          'The authorization server does not support this response type';
        break;
      case 'invalid_scope':
        mappedError = 'Invalid scope';
        mappedDescription = 'The requested scope is invalid or unknown';
        break;
      case 'server_error':
        mappedError = 'Provider server error';
        mappedDescription =
          'The authorization server encountered an unexpected condition';
        break;
      case 'temporarily_unavailable':
        mappedError = 'Service temporarily unavailable';
        mappedDescription =
          'The authorization server is temporarily overloaded or under maintenance';
        break;
    }

    return new SocialOAuthCallbackException(
      provider,
      mappedError,
      mappedDescription,
    );
  }

  /**
   * Handle strategy initialization errors
   * @param provider Social provider
   * @param error Original error
   * @returns Appropriate provider auth exception
   */
  static handleStrategyInitError(
    provider: SocialProviderEnum,
    error: any,
  ): SocialProviderAuthException {
    this.logger.error(
      `Strategy initialization failed for ${provider}: ${error.message}`,
    );

    let reason = 'Strategy initialization failed';
    if (error.message.includes('configuration')) {
      reason = 'Invalid or missing configuration';
    } else if (error.message.includes('dependency')) {
      reason = 'Missing required dependencies';
    } else if (error.message.includes('network')) {
      reason = 'Network connectivity issue';
    }

    return new SocialProviderAuthException(
      provider,
      `${reason}: ${error.message}`,
    );
  }

  /**
   * Handle generic social auth errors with proper logging and context
   * @param error Original error
   * @param context Additional context
   * @param provider Optional provider for context
   * @returns Appropriate social auth exception
   */
  static handleGenericError(
    error: any,
    context: string,
    provider?: SocialProviderEnum,
  ): SocialAuthException {
    const providerMsg = provider ? ` (${provider})` : '';
    this.logger.error(
      `${context}${providerMsg}: ${error.message}`,
      error.stack,
    );

    // If it's already a social auth exception, re-throw it
    if (error instanceof SocialAuthException) {
      return error;
    }

    // Map common error types
    if (error.name === 'ValidationError') {
      return new SocialAuthException(
        SOCIAL_AUTH_ERROR_CODES.INVALID_PROVIDER_CONFIG,
        `Validation failed in ${context}: ${error.message}`,
      );
    }

    if (
      error.name === 'TypeError' &&
      error.message.includes('Cannot read property')
    ) {
      return new SocialAuthException(
        SOCIAL_AUTH_ERROR_CODES.PROFILE_DATA_INCOMPLETE,
        `Missing required data in ${context}: ${error.message}`,
      );
    }

    // Generic error handling
    return new SocialAuthException(
      SOCIAL_AUTH_ERROR_CODES.PROVIDER_AUTHENTICATION_FAILED,
      `${context} failed: ${error.message}`,
    );
  }

  /**
   * Validate and sanitize callback URL
   * @param callbackUrl URL to validate
   * @param allowedDomains Optional list of allowed domains
   * @returns Validated URL object
   * @throws {SocialOAuthCallbackException} When URL is invalid or not allowed
   */
  static validateCallbackUrl(
    callbackUrl: string,
    allowedDomains?: string[],
  ): URL {
    try {
      const url = new URL(callbackUrl);

      // Basic security checks
      if (url.protocol !== 'http:' && url.protocol !== 'https:') {
        throw new Error('Only HTTP and HTTPS protocols are allowed');
      }

      // Check against allowed domains if provided
      if (allowedDomains && allowedDomains.length > 0) {
        const isAllowed = allowedDomains.some((domain) => {
          return url.hostname === domain || url.hostname.endsWith(`.${domain}`);
        });

        if (!isAllowed) {
          throw new Error(
            `Domain ${url.hostname} is not in the allowed domains list`,
          );
        }
      }

      return url;
    } catch (error) {
      throw new SocialOAuthCallbackException(
        'unknown' as SocialProviderEnum,
        'Invalid callback URL',
        error.message,
      );
    }
  }

  /**
   * Log successful operations for audit purposes
   * @param provider Social provider
   * @param operation Operation performed
   * @param userId Optional user ID
   * @param additionalInfo Optional additional information
   */
  static logSuccess(
    provider: SocialProviderEnum,
    operation: string,
    userId?: string,
    additionalInfo?: Record<string, any>,
  ): void {
    const userMsg = userId ? ` for user ${userId}` : '';
    const infoMsg = additionalInfo
      ? ` - ${JSON.stringify(additionalInfo)}`
      : '';
    this.logger.log(
      `${operation} successful for ${provider}${userMsg}${infoMsg}`,
    );
  }

  /**
   * Check if error is retryable (network errors, temporary service issues)
   * @param error Error to check
   * @returns True if error might be temporary and retryable
   */
  static isRetryableError(error: any): boolean {
    // Network errors
    if (
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ETIMEDOUT'
    ) {
      return true;
    }

    // HTTP status codes that might be temporary
    const retryableStatusCodes = [429, 500, 502, 503, 504];
    if (
      error.response?.status &&
      retryableStatusCodes.includes(error.response.status)
    ) {
      return true;
    }

    // Provider-specific temporary errors
    if (error.message?.includes('temporarily unavailable')) {
      return true;
    }

    return false;
  }
}
