import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const TWO_FACTOR_NAMESPACE = 'TWO_FACTOR';

type TwoFactorErrorCodes =
  | 'USER_NOT_FOUND'
  | 'ALREADY_ENABLED'
  | 'NOT_ENABLED'
  | 'SETUP_NOT_FOUND'
  | 'INVALID_CODE'
  | 'INVALID_BACKUP_CODE'
  | 'INVALID_TEMP_TOKEN'
  | 'TEMP_TOKEN_EXPIRED';

export const TWO_FACTOR_ERROR_CODES: Record<TwoFactorErrorCodes, ErrorCode> = {
  USER_NOT_FOUND: {
    code: `${TWO_FACTOR_NAMESPACE}:10000`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'User not found',
  },
  ALREADY_ENABLED: {
    code: `${TWO_FACTOR_NAMESPACE}:10001`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Two-factor authentication is already enabled',
  },
  NOT_ENABLED: {
    code: `${TWO_FACTOR_NAMESPACE}:10002`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Two-factor authentication is not enabled',
  },
  SETUP_NOT_FOUND: {
    code: `${TWO_FACTOR_NAMESPACE}:10003`,
    statusCode: HttpStatus.BAD_REQUEST,
    message:
      'Two-factor authentication setup not found. Please generate setup first.',
  },
  INVALID_CODE: {
    code: `${TWO_FACTOR_NAMESPACE}:10004`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid two-factor authentication code',
  },
  INVALID_BACKUP_CODE: {
    code: `${TWO_FACTOR_NAMESPACE}:10005`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Invalid backup code',
  },
  INVALID_TEMP_TOKEN: {
    code: `${TWO_FACTOR_NAMESPACE}:10006`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid or expired temporary token',
  },
  TEMP_TOKEN_EXPIRED: {
    code: `${TWO_FACTOR_NAMESPACE}:10007`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Temporary token has expired',
  },
};
