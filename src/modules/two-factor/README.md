# Two-Factor Authentication Module

A standalone, loosely-coupled two-factor authentication module for NestJS applications using TOTP (Time-based One-Time Passwords) and backup codes.

## Features

- 🔐 **TOTP-based Authentication** - Industry-standard Time-based One-Time Passwords
- 📱 **QR Code Generation** - Easy setup with authenticator apps
- 🔑 **Backup Codes** - Account recovery with single-use backup codes
- 🏗️ **Modular Architecture** - Loosely coupled, can be easily integrated or removed
- 🛡️ **Secure Token System** - Temporary tokens for two-factor login flow
- 📊 **Comprehensive Status** - Track usage, enabled date, remaining backup codes
- 🔄 **Backup Code Management** - Regenerate backup codes when needed
- 🎯 **TypeScript Support** - Full type safety and IntelliSense support

## Installation

1. **Install dependencies:**
```bash
npm install speakeasy qrcode @types/speakeasy @types/qrcode
```

2. **Import the module:**
```typescript
import { TwoFactorModule } from '@app/modules/two-factor';

@Module({
  imports: [
    TwoFactorModule,
    // ... other modules
  ],
})
export class AppModule {}
```

3. **Run migrations:**
```bash
npm run migration:run
```

## Configuration

Add these environment variables to your `.env` file:

```env
# Two-Factor Authentication Configuration
TWO_FACTOR_ISSUER_NAME=MyApp
TWO_FACTOR_TEMP_TOKEN_SECRET=your-temp-token-secret
TWO_FACTOR_TEMP_TOKEN_EXPIRY=10m
TWO_FACTOR_TOTP_WINDOW=2
TWO_FACTOR_SECRET_LENGTH=32
```

## API Endpoints

### Core 2FA Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/2fa/status` | Get 2FA status and statistics |
| `POST` | `/api/v1/2fa/setup` | Generate setup (QR code + backup codes) |
| `POST` | `/api/v1/2fa/enable` | Enable 2FA with TOTP verification |
| `POST` | `/api/v1/2fa/verify` | Verify TOTP code |
| `POST` | `/api/v1/2fa/verify-backup` | Verify backup code |
| `POST` | `/api/v1/2fa/regenerate-backup-codes` | Generate new backup codes |
| `DELETE` | `/api/v1/2fa/disable` | Disable 2FA |

### Integration with Auth Module

The module provides services that can be integrated with your existing auth flow:

```typescript
import { TwoFactorAuthService } from '@app/modules/two-factor';

@Injectable()
export class AuthService {
  constructor(
    private readonly twoFactorAuthService: TwoFactorAuthService,
  ) {}

  async login(userLogin: UserLoginEntity, userAgent: string, ipAddress: string) {
    // Check if 2FA is required
    const requires2FA = await this.twoFactorAuthService.checkTwoFactorRequired(userLogin.userId);
    
    if (requires2FA) {
      const tempToken = await this.twoFactorAuthService.generateTempToken(
        userLogin.userId,
        userLogin.username,
        userAgent,
        ipAddress,
      );
      
      return {
        requiresTwoFactor: true,
        tempToken,
        message: 'Please provide your two-factor authentication code',
      };
    }

    // Continue with normal login...
  }

  async completeTwoFactorLogin(dto: CompleteTwoFactorLoginDto) {
    const payload = await this.twoFactorAuthService.completeTwoFactorLogin(dto);
    
    // Continue with login using payload.userId, payload.username, etc.
  }
}
```

## Usage Examples

### Frontend Integration

```typescript
// 1. Setup 2FA
const setupResponse = await fetch('/api/v1/2fa/setup', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${accessToken}` }
});
const { qrCodeDataUrl, backupCodes } = await setupResponse.json();

// Display QR code and backup codes to user
document.getElementById('qr-code').src = qrCodeDataUrl;
console.log('Save these backup codes:', backupCodes);

// 2. Enable 2FA
const enableResponse = await fetch('/api/v1/2fa/enable', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ totpCode: userEnteredCode })
});

// 3. Login with 2FA
const loginResponse = await fetch('/api/v1/auth/2fa/login/email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const loginData = await loginResponse.json();

if (loginData.requiresTwoFactor) {
  const totpCode = prompt('Enter your 2FA code or backup code:');
  
  const completeResponse = await fetch('/api/v1/auth/2fa/login/2fa-complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      tempToken: loginData.tempToken, 
      totpCode 
    })
  });
  
  const { accessToken } = await completeResponse.json();
}
```

### Service Usage

```typescript
import { TwoFactorService, TwoFactorAuthService } from '@app/modules/two-factor';

@Injectable()
export class UserService {
  constructor(
    private readonly twoFactorService: TwoFactorService,
    private readonly twoFactorAuthService: TwoFactorAuthService,
  ) {}

  async getUserSecurityStatus(userId: string) {
    const twoFactorStatus = await this.twoFactorService.getStatus(userId);
    return {
      hasTwoFactor: twoFactorStatus.isEnabled,
      twoFactorEnabledAt: twoFactorStatus.enabledAt,
      remainingBackupCodes: twoFactorStatus.remainingBackupCodes,
    };
  }

  async requiresTwoFactor(userId: string): Promise<boolean> {
    return this.twoFactorAuthService.checkTwoFactorRequired(userId);
  }
}
```

## Database Schema

The module creates a `two_factor_auth` table with the following structure:

```sql
CREATE TABLE two_factor_auth (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  secret VARCHAR(255) NOT NULL,
  is_enabled BOOLEAN DEFAULT FALSE,
  backup_codes TEXT NULL,
  last_used_at TIMESTAMP NULL,
  enabled_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Security Features

- **Temporary Tokens**: Login temporary tokens expire in 10 minutes (configurable)
- **TOTP Window**: Accepts codes within configurable time window (default: ±60 seconds)
- **Backup Codes**: 10 single-use backup codes for account recovery
- **Secure Storage**: Secrets are stored securely in dedicated table
- **Audit Trail**: Tracks when 2FA was enabled and last used
- **Loose Coupling**: No direct dependencies on auth module

## Error Handling

The module provides comprehensive error codes:

- `TWO_FACTOR:10000` - User not found
- `TWO_FACTOR:10001` - Already enabled
- `TWO_FACTOR:10002` - Not enabled
- `TWO_FACTOR:10003` - Setup not found
- `TWO_FACTOR:10004` - Invalid code
- `TWO_FACTOR:10005` - Invalid backup code
- `TWO_FACTOR:10006` - Invalid temp token
- `TWO_FACTOR:10007` - Temp token expired

## Testing

```typescript
import { Test } from '@nestjs/testing';
import { TwoFactorModule, TwoFactorService } from '@app/modules/two-factor';

describe('TwoFactorService', () => {
  let service: TwoFactorService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [TwoFactorModule],
    }).compile();

    service = module.get<TwoFactorService>(TwoFactorService);
  });

  it('should generate setup', async () => {
    const setup = await service.generateSetup('user-id');
    expect(setup).toHaveProperty('qrCodeDataUrl');
    expect(setup).toHaveProperty('backupCodes');
  });
});
```

## Recommended Authenticator Apps

- Google Authenticator
- Microsoft Authenticator
- Authy
- 1Password
- Bitwarden Authenticator

## Migration from Integrated 2FA

If you have existing 2FA integrated in your auth module, you can migrate by:

1. Install the TwoFactorModule
2. Migrate existing 2FA data to the new table structure
3. Update your auth service to use TwoFactorAuthService
4. Remove old 2FA code from auth module

This modular approach provides better separation of concerns and makes the codebase more maintainable.
