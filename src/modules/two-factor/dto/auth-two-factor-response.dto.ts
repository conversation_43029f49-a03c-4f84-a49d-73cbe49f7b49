import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class TwoFactorLoginResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Temporary token for two-factor authentication',
    example: 'temp_token_123',
  })
  tempToken: string;

  @Expose()
  @ApiProperty({
    description: 'Whether two-factor authentication is required',
    example: true,
  })
  requiresTwoFactor: boolean;

  @Expose()
  @ApiProperty({
    description: 'Message indicating next steps',
    example: 'Please provide your two-factor authentication code',
  })
  message: string;
}
