import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, Length } from 'class-validator';

export class EnableTwoFactorDto {
  @Expose()
  @ApiProperty({
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}

export class VerifyTwoFactorDto {
  @Expose()
  @ApiProperty({
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}

export class DisableTwoFactorDto {
  @Expose()
  @ApiProperty({
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}

export class VerifyBackupCodeDto {
  @Expose()
  @ApiProperty({
    example: 'abc123def456',
    description: 'Backup code for two-factor authentication',
  })
  @IsString()
  @IsNotEmpty()
  backupCode: string;
}

export class TwoFactorSetupResponseDto {
  @Expose()
  @ApiProperty({
    example: 'JBSWY3DPEHPK3PXP',
    description: 'Base32 encoded secret for manual entry',
  })
  secret: string;

  @Expose()
  @ApiProperty({
    example:
      'otpauth://totp/MyApp:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MyApp',
    description: 'OTP Auth URL for QR code generation',
  })
  otpAuthUrl: string;

  @Expose()
  @ApiProperty({
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    description: 'Base64 encoded QR code image',
  })
  qrCodeDataUrl: string;

  @Expose()
  @ApiProperty({
    example: ['abc123def456', 'ghi789jkl012'],
    description: 'Backup codes for account recovery',
  })
  backupCodes: string[];
}

export class TwoFactorStatusDto {
  @Expose()
  @ApiProperty({
    example: true,
    description: 'Whether two-factor authentication is enabled',
  })
  isEnabled: boolean;

  @Expose()
  @ApiProperty({
    example: '2023-12-01T10:00:00Z',
    description: 'When 2FA was enabled',
  })
  @IsOptional()
  enabledAt?: Date;

  @Expose()
  @ApiProperty({
    example: '2023-12-01T15:30:00Z',
    description: 'When 2FA was last used',
  })
  @IsOptional()
  lastUsedAt?: Date;

  @Expose()
  @ApiProperty({
    example: 5,
    description: 'Number of remaining backup codes',
  })
  @IsOptional()
  remainingBackupCodes?: number;
}

export class CompleteTwoFactorLoginDto {
  @Expose()
  @ApiProperty({
    example: 'temp_token_123',
    description: 'Temporary token received from initial login',
  })
  @IsString()
  @IsNotEmpty()
  tempToken: string;

  @Expose()
  @ApiProperty({
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app',
  })
  @IsString()
  @IsNotEmpty()
  totpCode: string;
}

export class RegenerateBackupCodesDto {
  @Expose()
  @ApiProperty({
    example: '123456',
    description:
      'Six-digit TOTP code from authenticator app to confirm regeneration',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}
