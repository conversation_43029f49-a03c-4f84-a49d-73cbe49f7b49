import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { NullableType } from '@app/common/types';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Column, Entity, JoinColumn, OneToOne } from 'typeorm';

@Entity('two_factor_auth')
export class TwoFactorEntity extends CustomBaseEntity {
  @Column({ name: 'user_id', type: 'uuid', unique: true })
  userId: string;

  @OneToOne(() => UserEntity, {
    createForeignKeyConstraints: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'secret', type: 'varchar', length: 255 })
  secret: string;

  @Column({ name: 'is_enabled', default: false })
  isEnabled: boolean;

  @Column({ name: 'backup_codes', type: 'text', nullable: true })
  backupCodes: NullableType<string>; // JSON string of backup codes

  @Column({ name: 'last_used_at', type: 'timestamp', nullable: true })
  lastUsedAt: NullableType<Date>;

  @Column({ name: 'enabled_at', type: 'timestamp', nullable: true })
  enabledAt: NullableType<Date>;
}
