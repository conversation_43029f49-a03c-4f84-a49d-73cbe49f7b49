import { User } from '@app/modules/auth/decorators/user.decorator';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  DisableTwoFactorDto,
  EnableTwoFactorDto,
  RegenerateBackupCodesDto,
  TwoFactorSetupResponseDto,
  TwoFactorStatusDto,
  VerifyBackupCodeDto,
  VerifyTwoFactorDto,
} from '../dto/two-factor.dto';
import { TwoFactorService } from '../services/two-factor.service';

@ApiTags('Two-Factor Authentication')
@ApiBearerAuth()
@Controller({ path: '2fa', version: '1' })
@UseGuards(JwtAuthGuard)
export class TwoFactorController {
  constructor(private readonly twoFactorService: TwoFactorService) {}

  @ApiOperation({ summary: 'Get two-factor authentication status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication status retrieved successfully',
    type: TwoFactorStatusDto,
  })
  @Get('status')
  @HttpCode(HttpStatus.OK)
  async getStatus(@User() user: JwtPayloadType): Promise<TwoFactorStatusDto> {
    return this.twoFactorService.getStatus(user.sub);
  }

  @ApiOperation({ summary: 'Generate two-factor authentication setup' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication setup generated successfully',
    type: TwoFactorSetupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Two-factor authentication is already enabled',
  })
  @Post('setup')
  @HttpCode(HttpStatus.OK)
  async generateSetup(
    @User() user: JwtPayloadType,
  ): Promise<TwoFactorSetupResponseDto> {
    return this.twoFactorService.generateSetup(user.sub);
  }

  @ApiOperation({ summary: 'Enable two-factor authentication' })
  @ApiBody({ type: EnableTwoFactorDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication enabled successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Invalid TOTP code or two-factor authentication already enabled',
  })
  @Post('enable')
  @HttpCode(HttpStatus.OK)
  async enable(
    @User() user: JwtPayloadType,
    @Body() dto: EnableTwoFactorDto,
  ): Promise<{ message: string }> {
    await this.twoFactorService.enable(user.sub, dto);
    return { message: 'Two-factor authentication enabled successfully' };
  }

  @ApiOperation({ summary: 'Verify two-factor authentication code' })
  @ApiBody({ type: VerifyTwoFactorDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication code verified successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid TOTP code or two-factor authentication not enabled',
  })
  @Post('verify')
  @HttpCode(HttpStatus.OK)
  async verify(
    @User() user: JwtPayloadType,
    @Body() dto: VerifyTwoFactorDto,
  ): Promise<{ valid: boolean }> {
    const valid = await this.twoFactorService.verify(user.sub, dto);
    return { valid };
  }

  @ApiOperation({ summary: 'Verify backup code' })
  @ApiBody({ type: VerifyBackupCodeDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Backup code verified successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid backup code or two-factor authentication not enabled',
  })
  @Post('verify-backup')
  @HttpCode(HttpStatus.OK)
  async verifyBackup(
    @User() user: JwtPayloadType,
    @Body() dto: VerifyBackupCodeDto,
  ): Promise<{ valid: boolean }> {
    const valid = await this.twoFactorService.verifyBackupCode(user.sub, dto);
    return { valid };
  }

  @ApiOperation({ summary: 'Regenerate backup codes' })
  @ApiBody({ type: RegenerateBackupCodesDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Backup codes regenerated successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid TOTP code or two-factor authentication not enabled',
  })
  @Post('regenerate-backup-codes')
  @HttpCode(HttpStatus.OK)
  async regenerateBackupCodes(
    @User() user: JwtPayloadType,
    @Body() dto: RegenerateBackupCodesDto,
  ): Promise<{ backupCodes: string[] }> {
    const backupCodes = await this.twoFactorService.regenerateBackupCodes(
      user.sub,
      dto,
    );
    return { backupCodes };
  }

  @ApiOperation({ summary: 'Disable two-factor authentication' })
  @ApiBody({ type: DisableTwoFactorDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication disabled successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid TOTP code or two-factor authentication not enabled',
  })
  @Delete('disable')
  @HttpCode(HttpStatus.OK)
  async disable(
    @User() user: JwtPayloadType,
    @Body() dto: DisableTwoFactorDto,
  ): Promise<{ message: string }> {
    await this.twoFactorService.disable(user.sub, dto);
    return { message: 'Two-factor authentication disabled successfully' };
  }
}
