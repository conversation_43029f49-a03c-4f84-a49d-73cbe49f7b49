import { getIpAddress, getUserAgent } from '@app/common/utils/request';
import { Public } from '@app/modules/auth/decorators/public.decorator';
import { LoginEmailInput } from '@app/modules/auth/dto/login.input';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { CompleteTwoFactorLoginDto } from '@app/modules/two-factor/dto/two-factor.dto';
import { TwoFactorAuthService } from '@app/modules/two-factor/services/two-factor-auth.service';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

@ApiTags('Authentication')
@Controller({ path: 'auth/2fa', version: '1' })
export class TwoFactorAuthController {
  constructor(
    private readonly twoFactorAuthService: TwoFactorAuthService,
    private readonly authService: AuthService,
  ) {}

  @ApiOperation({ summary: 'Login by email' })
  @ApiBody({ type: LoginEmailInput })
  @Public()
  @Post('login/email')
  @HttpCode(HttpStatus.OK)
  async loginByEmail(@Body() loginDto: LoginEmailInput, @Req() req: Request) {
    const userLogin = await this.authService.validateUserByEmail(
      loginDto.email,
      loginDto.password,
    );

    if (!userLogin) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);

    return this.twoFactorAuthService.loginWithTwoFactor(
      userLogin,
      userAgent,
      ipAddress,
    );
  }

  @ApiOperation({ summary: 'Complete two-factor authentication login' })
  @ApiBody({ type: CompleteTwoFactorLoginDto })
  @Public()
  @Post('login/2fa-complete')
  @HttpCode(HttpStatus.OK)
  async completeTwoFactorLogin(
    @Body() dto: CompleteTwoFactorLoginDto,
    @Req() req: Request,
  ) {
    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);

    return this.twoFactorAuthService.completeTwoFactorLogin(
      dto,
      userAgent,
      ipAddress,
    );
  }
}
