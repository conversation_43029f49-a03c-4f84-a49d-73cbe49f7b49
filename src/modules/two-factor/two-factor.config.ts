import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export type TwoFactorConfigType = {
  issuerName: string;
  tempTokenSecret: string;
  tempTokenExpiry: string;
  totpWindow: number;
  secretLength: number;
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  TWO_FACTOR_ISSUER_NAME: string;

  @IsString()
  @IsOptional()
  TWO_FACTOR_TEMP_TOKEN_SECRET: string;

  @IsString()
  @IsOptional()
  TWO_FACTOR_TEMP_TOKEN_EXPIRY: string;

  @IsNumber()
  @IsOptional()
  TWO_FACTOR_TOTP_WINDOW: number;

  @IsNumber()
  @IsOptional()
  TWO_FACTOR_SECRET_LENGTH: number;
}

export const twoFactorConfig = registerAs('twoFactor', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    issuerName: process.env.TWO_FACTOR_ISSUER_NAME || 'MyApp',
    tempTokenSecret:
      process.env.TWO_FACTOR_TEMP_TOKEN_SECRET || 'temp-token-secret',
    tempTokenExpiry: process.env.TWO_FACTOR_TEMP_TOKEN_EXPIRY || '10m',
    totpWindow: parseInt(process.env.TWO_FACTOR_TOTP_WINDOW || '2', 10),
    secretLength: parseInt(process.env.TWO_FACTOR_SECRET_LENGTH || '32', 10),
  };
});
