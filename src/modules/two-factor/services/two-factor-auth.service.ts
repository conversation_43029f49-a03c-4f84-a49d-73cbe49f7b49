import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { LoginDto } from '@app/modules/auth/dto/login.dto';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { TwoFactorLoginResponseDto } from '@app/modules/two-factor/dto/auth-two-factor-response.dto';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { CompleteTwoFactorLoginDto } from '../dto/two-factor.dto';
import { TwoFactorConfigType } from '../two-factor.config';
import { TWO_FACTOR_ERROR_CODES } from '../two-factor.error-codes';
import { TwoFactorService } from './two-factor.service';

export interface TwoFactorTempTokenPayload {
  userId: string;
  username: string;
  userAgent: string;
  ipAddress: string;
  type: 'two_factor_temp';
  iat: number;
  exp: number;
}

export interface TwoFactorLoginResult {
  requiresTwoFactor: boolean;
  tempToken?: string;
  message?: string;
}

@Injectable()
export class TwoFactorAuthService {
  constructor(
    private readonly authService: AuthService,
    private readonly twoFactorService: TwoFactorService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService<{
      twoFactor: TwoFactorConfigType;
    }>,
  ) {}

  /**
   * Enhanced login method that handles two-factor authentication
   */
  async loginWithTwoFactor(
    userLogin: UserLoginEntity,
    userAgent: string,
    ipAddress: string,
  ): Promise<LoginDto | TwoFactorLoginResponseDto> {
    // Check if user has two-factor authentication enabled
    const requiresTwoFactor = await this.checkTwoFactorRequired(
      userLogin.userId,
    );

    if (requiresTwoFactor) {
      // Generate temporary token for two-factor authentication flow
      const tempToken = await this.generateTempToken(
        userLogin.userId,
        userLogin.username,
        userAgent,
        ipAddress,
      );

      return {
        tempToken,
        requiresTwoFactor: true,
        message: 'Please provide your two-factor authentication code',
      } as TwoFactorLoginResponseDto;
    }

    // Proceed with normal login if 2FA is not enabled
    return this.authService.login(userLogin, userAgent, ipAddress);
  }

  /**
   * Complete two-factor authentication login
   */
  async completeTwoFactorLogin(
    dto: CompleteTwoFactorLoginDto,
    userAgent: string,
    ipAddress: string,
  ): Promise<LoginDto> {
    // Verify the two-factor authentication
    // Verify the temporary token
    const payload = this.verifyTempToken(dto.tempToken);

    // Verify the two-factor code
    const isValidCode = await this.verifyTwoFactorCode(
      payload.userId,
      dto.totpCode,
    );

    if (!isValidCode) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.INVALID_CODE);
    }

    // Create a UserLoginEntity-like object from the payload
    const userLogin = {
      userId: payload.userId,
      username: payload.username,
    } as UserLoginEntity;

    // Complete the login process using the existing auth service
    return this.authService.login(userLogin, userAgent, ipAddress);
  }

  async checkTwoFactorRequired(userId: string): Promise<boolean> {
    return this.twoFactorService.isEnabled(userId);
  }

  async generateTempToken(
    userId: string,
    username: string,
    userAgent: string,
    ipAddress: string,
  ): Promise<string> {
    const config = this.configService.getOrThrow('twoFactor', { infer: true });

    const payload: Omit<TwoFactorTempTokenPayload, 'iat' | 'exp'> = {
      userId,
      username,
      userAgent,
      ipAddress,
      type: 'two_factor_temp',
    };

    return this.jwtService.signAsync(payload, {
      secret: config.tempTokenSecret,
      expiresIn: config.tempTokenExpiry,
    });
  }

  verifyTempToken(token: string): TwoFactorTempTokenPayload {
    const config = this.configService.getOrThrow('twoFactor', { infer: true });

    try {
      const payload = this.jwtService.verify<TwoFactorTempTokenPayload>(token, {
        secret: config.tempTokenSecret,
      });

      if (payload.type !== 'two_factor_temp') {
        throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.INVALID_TEMP_TOKEN);
      }

      return payload;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.TEMP_TOKEN_EXPIRED);
      }
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.INVALID_TEMP_TOKEN);
    }
  }

  async verifyTwoFactorCode(userId: string, code: string): Promise<boolean> {
    try {
      return await this.twoFactorService.verify(userId, { totpCode: code });
    } catch {
      // Try backup code if TOTP fails
      try {
        return await this.twoFactorService.verifyBackupCode(userId, {
          backupCode: code,
        });
      } catch {
        return false;
      }
    }
  }
}
