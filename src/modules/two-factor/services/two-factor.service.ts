import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { UserService } from '@app/modules/user/user.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import * as QRCode from 'qrcode';
import * as speakeasy from 'speakeasy';
import { Repository } from 'typeorm';
import {
  DisableTwoFactorDto,
  EnableTwoFactorDto,
  RegenerateBackupCodesDto,
  TwoFactorSetupResponseDto,
  TwoFactorStatusDto,
  VerifyBackupCodeDto,
  VerifyTwoFactorDto,
} from '../dto/two-factor.dto';
import { TwoFactorEntity } from '../entities/two-factor.entity';
import { TwoFactorConfigType } from '../two-factor.config';
import { TWO_FACTOR_ERROR_CODES } from '../two-factor.error-codes';

@Injectable()
export class TwoFactorService {
  constructor(
    @InjectRepository(TwoFactorEntity)
    private readonly twoFactorRepo: Repository<TwoFactorEntity>,
    private readonly userService: UserService,
    private readonly configService: ConfigService<{
      twoFactor: TwoFactorConfigType;
    }>,
  ) {}

  async generateSetup(userId: string): Promise<TwoFactorSetupResponseDto> {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.USER_NOT_FOUND);
    }

    // Check if 2FA is already enabled
    const existing = await this.twoFactorRepo.findOne({ where: { userId } });
    if (existing?.isEnabled) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.ALREADY_ENABLED);
    }

    const config = this.configService.getOrThrow('twoFactor', { infer: true });

    // Generate a secret
    const secret = speakeasy.generateSecret({
      name: user.email,
      issuer: config.issuerName,
      length: config.secretLength,
    });

    // Store or update the secret (not yet enabled)
    if (existing) {
      existing.secret = secret.base32;
      await this.twoFactorRepo.save(existing);
    } else {
      const twoFactorEntity = this.twoFactorRepo.create({
        userId,
        secret: secret.base32,
        isEnabled: false,
      });
      await this.twoFactorRepo.save(twoFactorEntity);
    }

    // Generate backup codes
    const backupCodes = this.generateBackupCodes();

    // Generate QR code
    const qrCodeDataUrl = await QRCode.toDataURL(secret.otpauth_url!);

    return {
      secret: secret.base32,
      otpAuthUrl: secret.otpauth_url!,
      qrCodeDataUrl,
      backupCodes,
    };
  }

  async enable(userId: string, dto: EnableTwoFactorDto): Promise<void> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });
    if (!twoFactor) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.SETUP_NOT_FOUND);
    }

    if (twoFactor.isEnabled) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.ALREADY_ENABLED);
    }

    // Verify the TOTP code
    const isValid = this.verifyTotpCode(twoFactor.secret, dto.totpCode);
    if (!isValid) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.INVALID_CODE);
    }

    // Enable two-factor authentication
    twoFactor.isEnabled = true;
    twoFactor.enabledAt = new Date();
    await this.twoFactorRepo.save(twoFactor);
  }

  async verify(userId: string, dto: VerifyTwoFactorDto): Promise<boolean> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });
    if (!twoFactor?.isEnabled) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.NOT_ENABLED);
    }

    const isValid = this.verifyTotpCode(twoFactor.secret, dto.totpCode);

    if (isValid) {
      // Update last used timestamp
      twoFactor.lastUsedAt = new Date();
      await this.twoFactorRepo.save(twoFactor);
    }

    return isValid;
  }

  async verifyBackupCode(
    userId: string,
    dto: VerifyBackupCodeDto,
  ): Promise<boolean> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });
    if (!twoFactor?.isEnabled) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.NOT_ENABLED);
    }

    if (!twoFactor.backupCodes) {
      return false;
    }

    const backupCodes = JSON.parse(twoFactor.backupCodes) as string[];
    const codeIndex = backupCodes.indexOf(dto.backupCode);

    if (codeIndex === -1) {
      return false;
    }

    // Remove the used backup code
    backupCodes.splice(codeIndex, 1);
    twoFactor.backupCodes = JSON.stringify(backupCodes);
    twoFactor.lastUsedAt = new Date();
    await this.twoFactorRepo.save(twoFactor);

    return true;
  }

  async disable(userId: string, dto: DisableTwoFactorDto): Promise<void> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });
    if (!twoFactor?.isEnabled) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.NOT_ENABLED);
    }

    // Verify the TOTP code before disabling
    const isValid = this.verifyTotpCode(twoFactor.secret, dto.totpCode);
    if (!isValid) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.INVALID_CODE);
    }

    // Remove the two-factor authentication record
    await this.twoFactorRepo.remove(twoFactor);
  }

  async getStatus(userId: string): Promise<TwoFactorStatusDto> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });

    if (!twoFactor) {
      return { isEnabled: false };
    }

    const remainingBackupCodes = twoFactor.backupCodes
      ? JSON.parse(twoFactor.backupCodes).length
      : 0;

    return {
      isEnabled: twoFactor.isEnabled,
      enabledAt: twoFactor.enabledAt || undefined,
      lastUsedAt: twoFactor.lastUsedAt || undefined,
      remainingBackupCodes,
    };
  }

  async regenerateBackupCodes(
    userId: string,
    dto: RegenerateBackupCodesDto,
  ): Promise<string[]> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });
    if (!twoFactor?.isEnabled) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.NOT_ENABLED);
    }

    // Verify the TOTP code before regenerating
    const isValid = this.verifyTotpCode(twoFactor.secret, dto.totpCode);
    if (!isValid) {
      throw new HttpErrorException(TWO_FACTOR_ERROR_CODES.INVALID_CODE);
    }

    const backupCodes = this.generateBackupCodes();
    twoFactor.backupCodes = JSON.stringify(backupCodes);
    await this.twoFactorRepo.save(twoFactor);

    return backupCodes;
  }

  async isEnabled(userId: string): Promise<boolean> {
    const twoFactor = await this.twoFactorRepo.findOne({ where: { userId } });
    return twoFactor?.isEnabled || false;
  }

  private verifyTotpCode(secret: string, token: string): boolean {
    const config = this.configService.getOrThrow('twoFactor', { infer: true });

    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: config.totpWindow,
    });
  }

  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(6).toString('hex'));
    }
    return codes;
  }
}
