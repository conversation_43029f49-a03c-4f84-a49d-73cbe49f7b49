import { AuthModule } from '@app/modules/auth/auth.module';
import { TwoFactorAuthController } from '@app/modules/two-factor/controllers/two-factor-auth.controller';
import { UserModule } from '@app/modules/user/user.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TwoFactorController } from './controllers/two-factor.controller';
import { TwoFactorEntity } from './entities/two-factor.entity';
import { TwoFactorAuthService } from './services/two-factor-auth.service';
import { TwoFactorService } from './services/two-factor.service';
import { twoFactorConfig, TwoFactorConfigType } from './two-factor.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [twoFactorConfig],
    }),
    TypeOrmModule.forFeature([TwoFactorEntity]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<{
          twoFactor: TwoFactorConfigType;
        }>,
      ) => {
        return {
          secret: configService.get('twoFactor.tempTokenSecret', {
            infer: true,
          }),
          signOptions: {
            expiresIn: configService.get('twoFactor.tempTokenExpiry', {
              infer: true,
            }),
          },
        };
      },
      inject: [ConfigService],
    }),
    AuthModule,
    UserModule,
  ],
  controllers: [TwoFactorController, TwoFactorAuthController],
  providers: [TwoFactorService, TwoFactorAuthService],
  exports: [TwoFactorService, TwoFactorAuthService],
})
export class TwoFactorModule {}
