import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailerConfigType } from '../mailer.config';
import { IMailerService } from './mailer.interface';
import { MailgunMailerService } from './mailgun.service';
import { SendgridMailerService } from './sendgrid.service';
import { SmtpMailerService } from './smtp.service';

@Injectable()
export class MailerFactory {
  _mailerService: IMailerService;
  constructor(
    private readonly configService: ConfigService<{
      mailer: MailerConfigType;
    }>,
  ) {
    const mailerType = this.configService.get('mailer.transport', {
      infer: true,
    });

    switch (mailerType) {
      case 'mailgun':
        this._mailerService = new MailgunMailerService(this.configService);
        break;
      case 'sendgrid':
        this._mailerService = new SendgridMailerService(this.configService);
        break;
      case 'smtp':
      default:
        this._mailerService = new SmtpMailerService(this.configService);
        break;
    }
  }
  getMailerService(): IMailerService {
    return this._mailerService;
  }
}
