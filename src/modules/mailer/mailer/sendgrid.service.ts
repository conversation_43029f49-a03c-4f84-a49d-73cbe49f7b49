import { MailerConfigType } from '@app/modules/mailer/mailer.config';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailContent } from '@sendgrid/helpers/classes/mail';
import * as sgMail from '@sendgrid/mail';
import {
  IMailerService,
  MailerPayload,
  MailSendResult,
} from './mailer.interface';

@Injectable()
export class SendgridMailerService implements IMailerService {
  constructor(
    private readonly configService: ConfigService<{
      mailer: MailerConfigType;
    }>,
  ) {
    const sendgridConfig = this.configService.getOrThrow('mailer.sendgrid', {
      infer: true,
    });
    const apiKey = sendgridConfig.apiKey;
    sgMail.setApiKey(apiKey);
  }

  private readonly logger = new Logger(SendgridMailerService.name);

  async sendMail(payload: MailerPayload): Promise<MailSendResult> {
    try {
      const {
        to,
        from,
        replyTo,
        replyToName,
        bccEmail,
        bccName,
        subject,
        text,
        html,
        attachments,
      } = payload;
      if (!html && !text) {
        throw new Error('HTML or text is required');
      }
      let content: MailContent[] & { 0: MailContent } = [
        {
          type: 'text/plain',
          value: '',
        },
      ];
      if (html) {
        content = [
          {
            type: 'text/html',
            value: html,
          },
        ];
      } else if (text) {
        content = [
          {
            type: 'text/plain',
            value: text,
          },
        ];
      }
      const msg: sgMail.MailDataRequired = {
        to: to,
        from,
        replyTo:
          replyToName && replyTo
            ? {
                name: replyToName,
                email: replyTo,
              }
            : replyTo,
        bcc:
          bccEmail && bccName
            ? {
                email: bccEmail,
                name: bccName,
              }
            : bccEmail,
        subject,
        content,
        attachments:
          attachments?.map((attachment) => ({
            content: attachment.content.toString('base64'),
            filename: attachment.filename,
          })) || undefined,
      };
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const response = await sgMail.send(msg);
      // this.logger.log(response);
      // console.log(response);
      return {
        status: 'sent',
      };
    } catch (error) {
      this.logger.error(error);
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }
}
