import { mailerConfig } from '@app/modules/mailer/mailer.config';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MailerService } from './mailer.service';
import { MailerFactory } from './mailer/mailer.factory';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [mailerConfig],
    }),
  ],
  providers: [MailerService, MailerFactory],
  exports: [MailerService],
})
export class MailerModule {}
