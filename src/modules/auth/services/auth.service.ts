import { toDto } from '@app/common/dto/to-dto';
import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { NullableType } from '@app/common/types';
import {
  generateOtpNumber,
  randomStringGenerator,
} from '@app/common/utils/string';
import { AuthConfigType } from '@app/modules/auth/auth.config';
import { AUTH_ERROR_CODES } from '@app/modules/auth/auth.error-code';
import { ChangePasswordInput } from '@app/modules/auth/dto/change-password.input';
import {
  LoginPasswordLessEmailInput,
  VerifyOtpLoginPasswordLessEmailInput,
} from '@app/modules/auth/dto/login-password-less-email.input';
import { LoginDto } from '@app/modules/auth/dto/login.dto';
import { LoginEmailInput } from '@app/modules/auth/dto/login.input';
import { RefreshTokenInput } from '@app/modules/auth/dto/refresh-token.input';
import { RegisterInput } from '@app/modules/auth/dto/register.input';
import { RequestResetPasswordByEmailLinkInput } from '@app/modules/auth/dto/reset-password-by-email-link.dto';
import {
  RequestResetPasswordByEmailOtpInput,
  VerifyEmailOtpResetPasswordDto,
  VerifyEmailOtpResetPasswordInput,
} from '@app/modules/auth/dto/reset-password-email-otp.dto';
import { ResetPasswordInput } from '@app/modules/auth/dto/reset-password.input';
import { OtpEntity } from '@app/modules/auth/entities/otp.entity';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { OtpTypeEnum } from '@app/modules/auth/enums/otp-type.enum';
import { UserLoginService } from '@app/modules/auth/services/user-login.service';
import { MailerService } from '@app/modules/mailer/mailer.service';
import { UserService } from '@app/modules/user/user.service';
import { SessionEntity } from '@modules/auth/entities/session.entity';
import { JwtRefreshPayloadType } from '@modules/auth/strategies/types/jwt-refresh-payload.type';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import * as ms from 'ms';
import { Repository } from 'typeorm';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private readonly configService: ConfigService<{
      auth: AuthConfigType;
    }>,
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly mailerService: MailerService,
    private readonly userLoginService: UserLoginService,
    @InjectRepository(SessionEntity)
    private readonly sessionRepo: Repository<SessionEntity>,
    @InjectRepository(OtpEntity)
    private readonly otpRepo: Repository<OtpEntity>,
  ) {}

  async register(input: RegisterInput, userAgent: string, ipAddress: string) {
    const user = await this.userService.create({
      ...input,
    });
    const userLogin = await this.userLoginService.create(
      user.id,
      ipAddress,
      userAgent,
    );

    // Log in the user automatically after registration
    const loginResult = await this.login(userLogin, userAgent, ipAddress);

    return loginResult;
  }

  async validateUserByUsername(username: string, password: string) {
    const userLogin = await this.userLoginService.getByUsername(username, [
      'user',
    ]);
    return this._validateUser(userLogin, password);
  }

  async validateUserByEmail(email: string, password: string) {
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    const userLogin = await this.userLoginService.getByUserId(user.id, [
      'user',
    ]);
    return this._validateUser(userLogin, password);
  }

  async _validateUser(
    userLogin: NullableType<UserLoginEntity>,
    password: string,
  ) {
    if (!userLogin) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    if (!userLogin.isActive) {
      throw new HttpErrorException(AUTH_ERROR_CODES.USER_INACTIVE);
    }
    if (userLogin.user && userLogin.user.isActive) {
      throw new HttpErrorException(AUTH_ERROR_CODES.USER_INACTIVE);
    }
    if (userLogin.user && userLogin.user.isEmailVerified) {
      throw new HttpErrorException(AUTH_ERROR_CODES.USER_EMAIL_NOT_VERIFIED);
    }
    if (
      userLogin &&
      userLogin.password &&
      (await bcrypt.compare(password, userLogin.password))
    ) {
      return userLogin;
    }
    throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
  }

  async login(
    userLogin: UserLoginEntity,
    userAgent: string,
    ipAddress: string,
  ) {
    const user = await this.userService.findById(userLogin.userId);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    const hash = crypto
      .createHash('sha256')
      .update(randomStringGenerator())
      .digest('hex');

    const session = new SessionEntity();
    session.userId = userLogin.userId;
    session.hash = hash;
    session.userAgent = userAgent;
    session.ipAddress = ipAddress;

    await this.sessionRepo.save(session);

    const { accessToken, refreshToken, tokenExpires } =
      await this.getTokensData({
        userId: userLogin.userId,
        sessionId: session.id,
        hash,
      });

    return toDto(LoginDto, {
      refreshToken,
      accessToken,
      tokenExpires,
      user,
    });
  }

  async loginByEmail(
    input: LoginEmailInput,
    userAgent: string,
    ipAddress: string,
  ) {
    const userLogin = await this.validateUserByUsername(
      input.email,
      input.password,
    );
    return this.login(userLogin, userAgent, ipAddress);
  }

  async loginPasswordLessEmail(input: LoginPasswordLessEmailInput) {
    const user = await this.userService.findByEmail(input.email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    const loginPasswordLessEmailConfig = this.configService.getOrThrow(
      'auth.loginPasswordLessEmail',
      {
        infer: true,
      },
    );
    const otp = generateOtpNumber(loginPasswordLessEmailConfig.length);

    const otpEntity = new OtpEntity();
    otpEntity.userId = user.id;
    otpEntity.otp = otp;
    otpEntity.type = OtpTypeEnum.LOGIN;
    await this.otpRepo.save(otpEntity);

    const otpExpires = ms(loginPasswordLessEmailConfig.expires) / ms('1m');

    const template = loginPasswordLessEmailConfig.templateEmail;
    if (template) {
      await this.mailerService.sendMailWithTemplate({
        to: user.email,
        template,
        subject: loginPasswordLessEmailConfig.subjectEmail,
        context: {
          otp,
          otpExpiresInMinutes: otpExpires,
        },
      });
    } else {
      await this.mailerService.sendMail({
        to: user.email,
        subject: loginPasswordLessEmailConfig.subjectEmail,
        text: `Hello ${user.firstName},

Your login OTP is ${otp}.

This OTP will expire in ${otpExpires} minutes.

If you did not request this OTP, please ignore this email.

Best regards,
`,
      });
    }
  }

  async verifyOtpLoginPasswordLessEmail(
    input: VerifyOtpLoginPasswordLessEmailInput,
    userAgent: string,
    ipAddress: string,
  ) {
    const { email, otp } = input;
    const otpEntity = await this.otpRepo.findOne({
      where: { otp, type: OtpTypeEnum.LOGIN },
      relations: ['user', 'userLogin'],
    });

    if (!otpEntity || !otpEntity.userLogin || !otpEntity.user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    if (otpEntity?.user?.email !== email) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const expires = ms(resetPasswordConfig.expires);

    if (otpEntity.createdAt.getTime() + expires < Date.now()) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    await this.otpRepo.delete(otpEntity.id);

    const userLogin = otpEntity.userLogin;

    return this.login(userLogin, userAgent, ipAddress);
  }

  async requestResetPasswordByEmailLink(
    input: RequestResetPasswordByEmailLinkInput,
  ) {
    const { email } = input;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    if (!resetPasswordConfig.resetPasswordUrl) {
      this.logger.error('Reset password URL is not set');
      throw new HttpErrorException(AUTH_ERROR_CODES.RESET_PASSWORD_URL_NOT_SET);
    }

    const resetPasswordToken = randomStringGenerator(
      resetPasswordConfig.tokenLength,
    );

    await this.userLoginService.updateByUserId(user.id, {
      resetPasswordToken,
      resetPasswordTokenAt: new Date(),
    });

    const resetPasswordUrl = `${resetPasswordConfig.resetPasswordUrl}?token=${resetPasswordToken}`;
    const expires = ms(resetPasswordConfig.expires) / ms('1m');
    const template = resetPasswordConfig.templateEmailToken;
    if (template) {
      await this.mailerService.sendMailWithTemplate({
        to: user.email,
        template,
        subject: resetPasswordConfig.subjectEmailToken,
        context: {
          resetPasswordUrl,
          linkExpiresInMinutes: expires,
        },
      });
    } else {
      await this.mailerService.sendMail({
        to: user.email,
        subject: resetPasswordConfig.subjectEmailOtp,
        text: `Hello ${user.firstName},
  
Your reset password link is ${resetPasswordUrl}.

This link will expire in ${expires} minutes.

Best regards,`,
      });
    }
  }

  async resetPassword(input: ResetPasswordInput) {
    const { token, newPassword } = input;
    const userLogin =
      await this.userLoginService.getByResetPasswordToken(token);
    if (!userLogin) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const expires = ms(resetPasswordConfig.expires);
    if (
      !userLogin.resetPasswordTokenAt ||
      userLogin.resetPasswordTokenAt.getTime() + expires < Date.now()
    ) {
      throw new HttpErrorException(AUTH_ERROR_CODES.RESET_PASSWORD_EXPIRED);
    }
    const passwordHash = await bcrypt.hash(newPassword, 10);
    await this.userLoginService.update(userLogin.id, {
      resetPasswordToken: null,
      resetPasswordTokenAt: null,
      password: passwordHash,
      passwordChangedAt: new Date(),
    });
  }

  async requestResetPasswordByEmailOtp(
    input: RequestResetPasswordByEmailOtpInput,
  ) {
    const { email } = input;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );

    const otp = generateOtpNumber(resetPasswordConfig.otpCodeLength);

    const otpEntity = new OtpEntity();
    otpEntity.userId = user.id;
    otpEntity.otp = otp;
    otpEntity.type = OtpTypeEnum.RESET_PASSWORD;
    await this.otpRepo.save(otpEntity);
    const otpExpires = ms(resetPasswordConfig.expires) / ms('1m');

    const template = resetPasswordConfig.templateEmailOtp;
    if (template) {
      await this.mailerService.sendMailWithTemplate({
        to: user.email,
        template,
        subject: resetPasswordConfig.subjectEmailOtp,
        context: {
          otp,
          otpExpiresInMinutes: otpExpires,
        },
      });
    } else {
      await this.mailerService.sendMail({
        to: user.email,
        subject: resetPasswordConfig.subjectEmailOtp,
        text: `Hello ${user.firstName},
  
Your reset password OTP is ${otp}.

This OTP will expire in ${otpExpires} minutes.

If you did not request this OTP, please ignore this email.

Best regards,
`,
      });
    }
  }

  async verifyEmailOtpResetPassword(
    input: VerifyEmailOtpResetPasswordInput,
  ): Promise<VerifyEmailOtpResetPasswordDto> {
    const { email, otp } = input;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    const otpEntity = await this.otpRepo.findOne({
      where: { otp, type: OtpTypeEnum.RESET_PASSWORD },
      relations: ['user'],
    });

    if (!otpEntity || !otpEntity.user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    if (otpEntity?.user?.email !== email) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const expires = ms(resetPasswordConfig.expires);

    if (otpEntity.createdAt.getTime() + expires < Date.now()) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    await this.otpRepo.delete(otpEntity.id);

    const resetPasswordToken = randomStringGenerator(
      resetPasswordConfig.tokenLength,
    );

    await this.userLoginService.updateByUserId(user.id, {
      resetPasswordToken,
      resetPasswordTokenAt: new Date(),
    });

    return {
      token: resetPasswordToken,
    };
  }

  async createSession(
    userId: NullableType<string>,
    userAgent: NullableType<string>,
    ipAddress: NullableType<string>,
    hash?: NullableType<string>,
  ) {
    const session = new SessionEntity();
    session.userId = userId;
    session.userAgent = userAgent;
    session.ipAddress = ipAddress;
    session.hash = hash || null;
    await this.sessionRepo.save(session);
    return session;
  }

  public async getTokensData(data: {
    userId: string;
    sessionId: string;
    hash: string;
  }) {
    const accessTokenConfig = this.configService.getOrThrow(
      'auth.accessToken',
      {
        infer: true,
      },
    );
    const refreshTokenConfig = this.configService.getOrThrow(
      'auth.refreshToken',
      {
        infer: true,
      },
    );
    const tokenExpiresIn = accessTokenConfig.expires;

    // Use a simple method to set token expiration
    const tokenExpires = Date.now() + ms(accessTokenConfig.expires); // Default to 1 hour

    const [accessToken, refreshToken] = await Promise.all([
      await this.jwtService.signAsync(
        {
          sub: data.userId,
          sessionId: data.sessionId,
        },
        {
          secret: accessTokenConfig.secret,
          privateKey: accessTokenConfig.privateKey,
          expiresIn: tokenExpiresIn,
        },
      ),
      await this.jwtService.signAsync(
        {
          sessionId: data.sessionId,
          hash: data.hash,
        },
        {
          secret: refreshTokenConfig.secret,
          privateKey: refreshTokenConfig.privateKey,
          expiresIn: refreshTokenConfig.expires,
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
      tokenExpires,
    };
  }

  async refreshToken(input: RefreshTokenInput) {
    let jwtRefreshPayload: JwtRefreshPayloadType;
    const refreshTokenConfig = this.configService.getOrThrow(
      'auth.refreshToken',
      {
        infer: true,
      },
    );
    try {
      jwtRefreshPayload = this.jwtService.verify(input.refreshToken, {
        secret: refreshTokenConfig.secret,
        publicKey: refreshTokenConfig.publicKey,
      });
    } catch (error: any) {
      this.logger.error(error);
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_REFRESH_TOKEN);
    }

    const session = await this.sessionRepo.findOne({
      where: {
        id: jwtRefreshPayload.sessionId,
      },
    });

    if (!session) {
      throw new HttpErrorException(AUTH_ERROR_CODES.SESSION_NOT_FOUND);
    }

    if (session.hash !== jwtRefreshPayload.hash) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_REFRESH_TOKEN);
    }

    const hash = crypto
      .createHash('sha256')
      .update(randomStringGenerator())
      .digest('hex');

    await this.sessionRepo.update(session.id, {
      hash,
    });

    const { accessToken, refreshToken, tokenExpires } =
      await this.getTokensData({
        userId: session.userId as string,
        sessionId: session.id,
        hash,
      });

    return {
      accessToken,
      refreshToken,
      tokenExpires,
    };
  }

  async logout(sessionId: string) {
    await this.sessionRepo.delete(sessionId);
  }

  async changePassword(
    userId: string,
    input: ChangePasswordInput,
  ): Promise<void> {
    const userLogin = await this.userLoginService.getByUserId(userId);
    if (!userLogin) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    const { currentPassword, newPassword } = input;
    // Verify current password
    const isPasswordValid =
      userLogin.password &&
      (await bcrypt.compare(currentPassword, userLogin.password));

    if (!isPasswordValid) {
      throw new HttpErrorException(AUTH_ERROR_CODES.CURRENT_PASSWORD_INCORRECT);
    }

    // Hash and update with new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    userLogin.password = hashedPassword;

    await this.userLoginService.update(userLogin.id, {
      password: hashedPassword,
      passwordChangedAt: new Date(),
    });
  }
}
