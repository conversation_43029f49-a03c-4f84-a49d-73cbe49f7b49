import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { NullableType } from '@app/common/types';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Exclude } from 'class-transformer';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity('user_logins')
export class UserLoginEntity extends CustomBaseEntity {
  @Column({ name: 'user_id', unique: true })
  userId: string;

  @Column({ name: 'username', unique: true })
  username: string;

  @Column({ name: 'ip_address', nullable: true })
  ipAddress: string;

  @Column({ name: 'user_agent', nullable: true })
  userAgent: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'password', type: 'varchar', length: 255, nullable: true })
  @Exclude()
  password: NullableType<string>;

  @Column({ name: 'password_changed_at', type: 'timestamp', nullable: true })
  passwordChangedAt: NullableType<Date>;

  @Column({
    name: 'reset_password_token',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  resetPasswordToken: NullableType<string>;

  @Column({
    name: 'reset_password_token_at',
    type: 'timestamp',
    nullable: true,
  })
  resetPasswordTokenAt: NullableType<Date>;

  @ManyToOne(() => UserEntity, {
    createForeignKeyConstraints: false,
    nullable: true,
  })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
