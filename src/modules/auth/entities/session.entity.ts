import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { NullableType } from '@app/common/types';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
@Entity('sessions')
export class SessionEntity extends CustomBaseEntity {
  @Column({
    name: 'user_id',
    type: 'uuid',
    nullable: true,
  })
  userId: NullableType<string>;

  @ManyToOne(() => UserEntity, {
    createForeignKeyConstraints: false,
    nullable: true,
  })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({
    name: 'hash',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  hash: NullableType<string>;

  @Column({
    name: 'user_agent',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  userAgent: NullableType<string>;

  @Column({
    name: 'ip_address',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  ipAddress: NullableType<string>;

  isExpired(expiresIn: number): boolean {
    const now = new Date();
    const diff = now.getTime() - this.createdAt.getTime();
    return diff > expiresIn;
  }
}
