import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const AUTH_NAMESPACE = 'AUTH';

type AuthErrorCodes =
  | 'INVALID_CREDENTIALS'
  | 'INVALID_OTP'
  | 'OTP_EXPIRED'
  | 'INVALID_RESET_PASSWORD_TOKEN'
  | 'SESSION_EXPIRED'
  | 'SESSION_NOT_FOUND'
  | 'RESET_PASSWORD_EXPIRED'
  | 'CURRENT_PASSWORD_INCORRECT'
  | 'INVALID_REFRESH_TOKEN'
  | 'RESET_PASSWORD_URL_NOT_SET'
  | 'USER_INACTIVE'
  | 'USER_EMAIL_NOT_VERIFIED';

export const AUTH_ERROR_CODES: Record<AuthErrorCodes, ErrorCode> = {
  INVALID_CREDENTIALS: {
    code: `${AUTH_NAMESPACE}:10000`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid credentials',
  },
  INVALID_OTP: {
    code: `${AUTH_NAMESPACE}:10001`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid OTP',
  },
  OTP_EXPIRED: {
    code: `${AUTH_NAMESPACE}:10002`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'OTP expired',
  },
  INVALID_RESET_PASSWORD_TOKEN: {
    code: `${AUTH_NAMESPACE}:10003`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid reset password token',
  },
  SESSION_EXPIRED: {
    code: `${AUTH_NAMESPACE}:10004`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Session expired',
  },
  SESSION_NOT_FOUND: {
    code: `${AUTH_NAMESPACE}:10005`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Session not found',
  },
  RESET_PASSWORD_EXPIRED: {
    code: `${AUTH_NAMESPACE}:10006`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Reset password token expired',
  },
  CURRENT_PASSWORD_INCORRECT: {
    code: `${AUTH_NAMESPACE}:10007`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Current password is incorrect',
  },
  INVALID_REFRESH_TOKEN: {
    code: `${AUTH_NAMESPACE}:10008`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid refresh token',
  },
  RESET_PASSWORD_URL_NOT_SET: {
    code: `${AUTH_NAMESPACE}:10009`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Reset password URL is not configured',
  },
  USER_INACTIVE: {
    code: `${AUTH_NAMESPACE}:10010`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'User is inactive',
  },
  USER_EMAIL_NOT_VERIFIED: {
    code: `${AUTH_NAMESPACE}:10011`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'User email is not verified',
  },
};
