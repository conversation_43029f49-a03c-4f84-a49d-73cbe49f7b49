import { UserRoleEnum } from '@app/modules/user/enums/user-role.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class TokenDto {
  @ApiProperty({ description: 'Access token' })
  @Expose()
  accessToken: string;

  @ApiProperty({ description: 'Refresh token' })
  @Expose()
  refreshToken: string;

  @ApiProperty({ description: 'Token expires in' })
  @Expose()
  tokenExpires: number;
}

export class ProfileDto {
  @ApiProperty({ description: 'User ID' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'User email' })
  @Expose()
  email: string;

  @ApiProperty({ description: 'User role', enum: UserRoleEnum })
  @Expose()
  role: UserRoleEnum;
}

export class LoginDto extends TokenDto {
  @ApiProperty({ description: 'User profile', type: ProfileDto })
  @Type(() => ProfileDto)
  @Expose()
  user: ProfileDto;
}
