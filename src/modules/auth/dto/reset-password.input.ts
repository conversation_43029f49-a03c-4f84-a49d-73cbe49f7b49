import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class ResetPasswordInput {
  @ApiProperty({
    description: 'The token of the user',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  token: string;

  @ApiProperty({
    description: 'The new password of the user',
    example: 'Password123',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  newPassword: string;
}
