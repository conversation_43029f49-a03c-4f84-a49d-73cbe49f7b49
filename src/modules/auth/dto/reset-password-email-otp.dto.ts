import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsNumberString } from 'class-validator';

export class RequestResetPasswordByEmailOtpInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  @Expose()
  email: string;
}

export class VerifyEmailOtpResetPasswordInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  @Expose()
  email: string;

  @ApiProperty({
    description: 'The OTP code',
    example: '123456',
  })
  @IsNumberString()
  @IsNotEmpty()
  @Expose()
  otp: string;
}

export class VerifyEmailOtpResetPasswordDto {
  @ApiProperty({
    description: 'The reset password token',
    example: '1234567890',
  })
  @Expose()
  token: string;
}
