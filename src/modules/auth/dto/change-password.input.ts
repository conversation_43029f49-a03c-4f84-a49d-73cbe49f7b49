import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class ChangePasswordInput {
  @ApiProperty({
    description: 'The current password of the user',
    example: 'CurrentPassword123',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  currentPassword: string;

  @ApiProperty({
    description: 'The new password for the user',
    example: 'NewPassword123',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Expose()
  newPassword: string;
}
