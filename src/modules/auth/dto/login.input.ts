import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

export class LoginEmailInput {
  @ApiProperty({ description: 'The email of the user' })
  @Expose()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'The password of the user' })
  @Expose()
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;
}

export class LoginInput {
  @ApiProperty({ description: 'The username of the user' })
  @Expose()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ description: 'The password of the user' })
  @Expose()
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;
}
