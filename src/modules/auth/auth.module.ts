import { authConfig, AuthConfigType } from '@app/modules/auth/auth.config';
import { OtpEntity } from '@app/modules/auth/entities/otp.entity';
import { SessionEntity } from '@app/modules/auth/entities/session.entity';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { UserLoginService } from '@app/modules/auth/services/user-login.service';
import { JwtStrategy } from '@app/modules/auth/strategies/jwt.strategy';
import { MailerModule } from '@app/modules/mailer/mailer.module';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { UserModule } from '@app/modules/user/user.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [authConfig],
    }),
    TypeOrmModule.forFeature([
      SessionEntity,
      UserEntity,
      OtpEntity,
      UserLoginEntity,
    ]),
    PassportModule,
    UserModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<{
          auth: AuthConfigType;
        }>,
      ) => {
        const accessToken = configService.getOrThrow('auth.accessToken', {
          infer: true,
        });
        return {
          secret: accessToken.secret,
          privateKey: accessToken.privateKey,
          publicKey: accessToken.publicKey,
          signOptions: accessToken.algorithm
            ? {
                algorithm: accessToken.algorithm,
              }
            : undefined,
        };
      },
      inject: [ConfigService],
    }),
    MailerModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, UserLoginService, JwtStrategy],
  exports: [AuthService, UserLoginService, JwtModule, PassportModule],
})
export class AuthModule {}
