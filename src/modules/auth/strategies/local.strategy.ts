import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      usernameField: 'email',
    });
  }

  async validate(email: string, password: string): Promise<UserLoginEntity> {
    const userLogin = await this.authService.validateUserByUsername(
      email,
      password,
    );
    if (!userLogin) {
      throw new UnauthorizedException();
    }
    return userLogin;
  }
}
