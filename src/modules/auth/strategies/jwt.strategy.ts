import { AuthConfigType } from '@app/modules/auth/auth.config';
import { JwtPayloadType } from '@modules/auth/strategies/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    configService: ConfigService<{
      auth: AuthConfigType;
    }>,
  ) {
    const accessToken = configService.getOrThrow('auth.accessToken', {
      infer: true,
    });
    const secretOrKey = accessToken.secret || accessToken.privateKey;
    if (!secretOrKey) {
      throw new Error('secretOrKey is not defined');
    }
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: secretOrKey,
    });
  }

  public validate(payload: JwtPayloadType): JwtPayloadType {
    if (!payload.sub) {
      throw new UnauthorizedException();
    }
    return payload;
  }
}
