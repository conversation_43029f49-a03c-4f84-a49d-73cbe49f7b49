import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { Transform } from 'class-transformer';
import { IsIn, IsNumber, IsOptional, IsString } from 'class-validator';
import { Algorithm } from 'jsonwebtoken';
import * as ms from 'ms';

export type AuthConfigType = {
  accessToken: {
    algorithm?: Algorithm;
    secret?: string;
    publicKey?: string;
    privateKey?: string;
    expires: ms.StringValue;
  };
  refreshToken: {
    algorithm?: Algorithm;
    secret?: string;
    publicKey?: string;
    privateKey?: string;
    expires: ms.StringValue;
  };
  forgot: {
    secret?: string;
    expires?: ms.StringValue;
  };
  confirmEmail: {
    secret?: string;
    expires?: ms.StringValue;
  };
  sessionExpires: number;
  loginPasswordLessEmail?: {
    expires: ms.StringValue;
    length: number;
    templateEmail?: string;
    subjectEmail: string;
  };
  resetPassword?: {
    expires: ms.StringValue;
    otpCodeLength: number;
    templateEmailOtp?: string;
    subjectEmailOtp: string;
    tokenLength: number;
    templateEmailToken?: string;
    subjectEmailToken: string;
    resetPasswordUrl?: string;
  };
};

class EnvironmentVariablesValidator {
  @IsIn([
    'HS256',
    'HS384',
    'HS512',
    'RS256',
    'RS384',
    'RS512',
    'ES256',
    'ES384',
    'ES512',
    'PS256',
    'PS384',
    'PS512',
    'none',
  ])
  @IsOptional()
  AUTH_ACCESS_ALGORITHM: Algorithm;

  @IsString()
  @IsOptional()
  AUTH_ACCESS_SECRET: string;

  @IsString()
  AUTH_ACCESS_TOKEN_EXPIRES_IN: string;

  @IsString()
  @IsOptional()
  AUTH_ACCESS_PUBLIC_KEY: string;

  @IsString()
  @IsOptional()
  AUTH_ACCESS_PRIVATE_KEY: string;

  @IsIn([
    'HS256',
    'HS384',
    'HS512',
    'RS256',
    'RS384',
    'RS512',
    'ES256',
    'ES384',
    'ES512',
    'PS256',
    'PS384',
    'PS512',
    'none',
  ])
  @IsOptional()
  AUTH_REFRESH_ALGORITHM: Algorithm;

  @IsString()
  @IsOptional()
  AUTH_REFRESH_SECRET: string;

  @IsString()
  AUTH_REFRESH_TOKEN_EXPIRES_IN: string;

  @IsString()
  @IsOptional()
  AUTH_REFRESH_PUBLIC_KEY: string;

  @IsString()
  @IsOptional()
  AUTH_REFRESH_PRIVATE_KEY: string;

  @IsString()
  AUTH_FORGOT_SECRET: string;

  @IsString()
  AUTH_FORGOT_TOKEN_EXPIRES_IN: string;

  @IsString()
  AUTH_CONFIRM_EMAIL_SECRET: string;

  @IsString()
  AUTH_CONFIRM_EMAIL_TOKEN_EXPIRES_IN: string;

  @IsString()
  @IsOptional()
  AUTH_SESSION_EXPIRES_IN: string;

  @IsString()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_EXPIRES_IN: string;

  @IsNumber()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_LENGTH: number;

  @IsString()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_TEMPLATE: string;

  @IsString()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_SUBJECT_EMAIL: string;

  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_EXPIRES_IN: string;

  @IsNumber()
  @IsOptional()
  AUTH_RESET_PASSWORD_OTP_CODE_LENGTH: number;

  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_OTP_CODE_TEMPLATE: string;

  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_OTP_CODE_SUBJECT_EMAIL: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  AUTH_RESET_PASSWORD_TOKEN_LENGTH: number;

  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_TOKEN_TEMPLATE: string;

  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_TOKEN_SUBJECT_EMAIL: string;

  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_URL: string;
}

export const authConfig = registerAs<AuthConfigType>('auth', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    accessToken: {
      algorithm: process.env.AUTH_ACCESS_ALGORITHM as Algorithm,
      secret: process.env.AUTH_ACCESS_SECRET,
      expires:
        (process.env.AUTH_ACCESS_TOKEN_EXPIRES_IN as ms.StringValue) || '1h',
      publicKey: process.env.AUTH_ACCESS_PUBLIC_KEY,
      privateKey: process.env.AUTH_ACCESS_PRIVATE_KEY,
    },
    refreshToken: {
      algorithm: process.env.AUTH_REFRESH_ALGORITHM as Algorithm,
      secret: process.env.AUTH_REFRESH_SECRET,
      expires:
        (process.env.AUTH_REFRESH_TOKEN_EXPIRES_IN as ms.StringValue) || '15d',
      publicKey: process.env.AUTH_REFRESH_PUBLIC_KEY,
      privateKey: process.env.AUTH_REFRESH_PRIVATE_KEY,
    },
    forgot: {
      secret: process.env.AUTH_FORGOT_SECRET,
      expires: process.env.AUTH_FORGOT_TOKEN_EXPIRES_IN as ms.StringValue,
    },
    confirmEmail: {
      secret: process.env.AUTH_CONFIRM_EMAIL_SECRET,
      expires: process.env
        .AUTH_CONFIRM_EMAIL_TOKEN_EXPIRES_IN as ms.StringValue,
    },
    loginPasswordLessEmail: {
      expires:
        (process.env
          .AUTH_LOGIN_PASSWORD_LESS_EMAIL_EXPIRES_IN as ms.StringValue) || '5m',
      length: Number(process.env.AUTH_LOGIN_PASSWORD_LESS_EMAIL_LENGTH) || 6,
      template: process.env.AUTH_LOGIN_PASSWORD_LESS_EMAIL_TEMPLATE,
      subjectEmail:
        process.env.AUTH_LOGIN_PASSWORD_LESS_EMAIL_SUBJECT_EMAIL || 'Login OTP',
    },
    resetPassword: {
      expires:
        (process.env.AUTH_RESET_PASSWORD_EXPIRES_IN as ms.StringValue) || '5m',
      otpCodeLength:
        Number(process.env.AUTH_RESET_PASSWORD_OTP_CODE_LENGTH) || 6,
      templateEmailOtp: process.env.AUTH_RESET_PASSWORD_OTP_CODE_TEMPLATE,
      subjectEmailOtp:
        process.env.AUTH_RESET_PASSWORD_OTP_CODE_SUBJECT_EMAIL ||
        'Reset Password',
      tokenLength: Number(process.env.AUTH_RESET_PASSWORD_TOKEN_LENGTH) || 64,
      templateEmailToken: process.env.AUTH_RESET_PASSWORD_TOKEN_TEMPLATE,
      subjectEmailToken:
        process.env.AUTH_RESET_PASSWORD_TOKEN_SUBJECT_EMAIL || 'Reset Password',
      resetPasswordUrl: process.env.AUTH_RESET_PASSWORD_URL,
    },
    sessionExpires: ms(
      (process.env.AUTH_SESSION_EXPIRES_IN as ms.StringValue) || '5m',
    ),
  };
});
