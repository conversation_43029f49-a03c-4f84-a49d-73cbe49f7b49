import { getIpAddress, getUserAgent } from '@app/common/utils/request';
import { Public } from '@app/modules/auth/decorators/public.decorator';
import { ChangePasswordInput } from '@app/modules/auth/dto/change-password.input';
import {
  LoginPasswordLessEmailInput,
  VerifyOtpLoginPasswordLessEmailInput,
} from '@app/modules/auth/dto/login-password-less-email.input';
import { LoginEmailInput } from '@app/modules/auth/dto/login.input';
import { RefreshTokenInput } from '@app/modules/auth/dto/refresh-token.input';
import { RegisterInput } from '@app/modules/auth/dto/register.input';
import { RequestResetPasswordByEmailLinkInput } from '@app/modules/auth/dto/reset-password-by-email-link.dto';
import {
  RequestResetPasswordByEmailOtpInput,
  VerifyEmailOtpResetPasswordDto,
  VerifyEmailOtpResetPasswordInput,
} from '@app/modules/auth/dto/reset-password-email-otp.dto';
import { ResetPasswordInput } from '@app/modules/auth/dto/reset-password.input';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { UserDto } from '@app/modules/user/dto/user.dto';
import { User } from '@modules/auth/decorators/user.decorator';
import { JwtPayloadType } from '@modules/auth/strategies/types/jwt-payload.type';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';

@ApiTags('Authentication')
@Controller({ path: 'auth', version: '1' })
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({ summary: 'Register new user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User registered successfully',
    type: RegisterInput,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email already exists',
  })
  @ApiBody({ type: RegisterInput })
  @Public()
  @HttpCode(HttpStatus.CREATED)
  @Post('register')
  async register(@Body() registerDto: RegisterInput, @Req() req: Request) {
    const userAgent = req.headers['user-agent'] || '';
    const ipAddress = req.ip || '';
    return await this.authService.register(registerDto, userAgent, ipAddress);
  }

  @ApiOperation({ summary: 'Login by email' })
  @ApiBody({ type: LoginEmailInput })
  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('login/email')
  async loginByEmail(@Body() input: LoginEmailInput, @Req() req: Request) {
    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);
    return this.authService.loginByEmail(input, userAgent, ipAddress);
  }

  @ApiOperation({ summary: 'Login with password less email' })
  @ApiBody({ type: LoginPasswordLessEmailInput })
  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('login/password-less/email')
  async loginPasswordLessEmail(@Body() input: LoginPasswordLessEmailInput) {
    return this.authService.loginPasswordLessEmail(input);
  }

  @ApiOperation({ summary: 'Verify OTP for login with password less email' })
  @ApiBody({ type: VerifyOtpLoginPasswordLessEmailInput })
  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('login/password-less/email/verify')
  async verifyOtpLoginPasswordLessEmail(
    @Body() input: VerifyOtpLoginPasswordLessEmailInput,
    @Req() req: Request,
  ) {
    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);

    return this.authService.verifyOtpLoginPasswordLessEmail(
      input,
      userAgent,
      ipAddress,
    );
  }

  @ApiOperation({ summary: 'Refresh token' })
  @ApiBody({ type: RefreshTokenInput })
  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('refresh')
  refreshToken(@Body() input: RefreshTokenInput) {
    return this.authService.refreshToken(input);
  }

  @ApiOperation({ summary: 'Logout' })
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async logout(@User() jwt: JwtPayloadType) {
    await this.authService.logout(jwt.sessionId);
    return { message: 'Successfully logged out' };
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change password' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Password changed successfully',
    type: UserDto,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard)
  @Patch('/change-password')
  async changePassword(
    @User() user: JwtPayloadType,
    @Body() input: ChangePasswordInput,
  ) {
    await this.authService.changePassword(user.sub, input);
  }

  @ApiOperation({ summary: 'Request reset password by email OTP' })
  @ApiBody({ type: RequestResetPasswordByEmailOtpInput })
  @Post('reset-password/email/otp/request')
  @Public()
  async requestResetPasswordByEmailOtp(
    @Body() input: RequestResetPasswordByEmailOtpInput,
  ) {
    return this.authService.requestResetPasswordByEmailOtp(input);
  }

  @ApiOperation({ summary: 'Verify email OTP for reset password' })
  @Post('reset-password/email/otp/verify')
  @ApiBody({ type: VerifyEmailOtpResetPasswordInput })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Reset password token',
    type: VerifyEmailOtpResetPasswordDto,
  })
  @Public()
  async verifyResetPasswordByEmailOtp(
    @Body() input: VerifyEmailOtpResetPasswordInput,
  ) {
    return this.authService.verifyEmailOtpResetPassword(input);
  }

  @ApiOperation({ summary: 'Request reset password by email link' })
  @ApiBody({ type: RequestResetPasswordByEmailLinkInput })
  @Public()
  @HttpCode(HttpStatus.NO_CONTENT)
  @Post('reset-password/email/link/request')
  async requestResetPasswordByEmailLink(
    @Body() input: RequestResetPasswordByEmailLinkInput,
  ) {
    return this.authService.requestResetPasswordByEmailLink(input);
  }

  @ApiOperation({ summary: 'Reset password' })
  @ApiBody({ type: ResetPasswordInput })
  @Public()
  @HttpCode(HttpStatus.NO_CONTENT)
  @Post('reset-password')
  async resetPassword(@Body() input: ResetPasswordInput) {
    await this.authService.resetPassword(input);
  }
}
