import { Inject, Injectable, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { S3ModuleOptions, S3OptionsFactory } from './s3.interfaces';
import { S3Module } from './s3.module';
import { S3Service } from './s3.service';

// Example 1: Basic usage with default configuration
@Module({
  imports: [S3Module],
})
export class BasicS3Module {}

// Example 2: Static configuration
@Module({
  imports: [
    S3Module.forRoot({
      region: 'us-west-2',
      bucket: 'my-app-uploads',
      accessKeyId: 'AKIA...',
      secretAccessKey: 'xxx...',
      isGlobal: true,
    }),
  ],
})
export class StaticS3Module {}

// Example 3: Async configuration with factory
@Module({
  imports: [
    ConfigModule.forRoot(),
    S3Module.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        region: configService.get('AWS_REGION'),
        bucket: configService.get('AWS_S3_BUCKET'),
        accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
        cdnDomain: configService.get('AWS_CLOUDFRONT_DOMAIN'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AsyncS3Module {}

// Example 4: Configuration using class
@Injectable()
export class S3ConfigService implements S3OptionsFactory {
  constructor(private configService: ConfigService) {}

  createS3Options(): S3ModuleOptions {
    return {
      region: this.configService.get('AWS_REGION'),
      bucket: this.configService.get('AWS_S3_BUCKET'),
      accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
    };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot(),
    S3Module.forRootAsync({
      imports: [ConfigModule],
      useClass: S3ConfigService,
    }),
  ],
  providers: [S3ConfigService],
})
export class ClassBasedS3Module {}

// Example 5: Multiple S3 instances
@Module({
  imports: [
    // Default storage for general files
    S3Module.forRoot({
      region: 'us-east-1',
      bucket: 'general-files',
    }),
    // Avatar storage with different configuration
    S3Module.forFeature('AVATAR_S3', {
      region: 'us-west-2',
      bucket: 'user-avatars',
      cdnDomain: 'https://d123456789.cloudfront.net',
    }),
    // Backup storage with async config
    S3Module.forFeatureAsync('BACKUP_S3', {
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        region: configService.get('BACKUP_AWS_REGION'),
        bucket: configService.get('BACKUP_S3_BUCKET'),
        endpoint: configService.get('BACKUP_S3_ENDPOINT'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class MultiInstanceS3Module {}

// Example 6: S3-compatible services
@Module({
  imports: [
    // DigitalOcean Spaces
    S3Module.forRoot({
      region: 'nyc3',
      bucket: 'my-space',
      endpoint: 'https://nyc3.digitaloceanspaces.com',
      accessKeyId: 'your-spaces-key',
      secretAccessKey: 'your-spaces-secret',
      token: 'DO_SPACES',
    }),
    // MinIO
    S3Module.forFeature('MINIO', {
      region: 'us-east-1',
      bucket: 'minio-bucket',
      endpoint: 'http://localhost:9000',
      accessKeyId: 'minioadmin',
      secretAccessKey: 'minioadmin',
    }),
  ],
})
export class S3CompatibleModule {}

// Usage Examples in Services

@Injectable()
export class FileUploadService {
  constructor(private readonly s3Service: S3Service) {}

  async uploadImage(file: Buffer, filename: string) {
    try {
      const result = await this.s3Service.upload({
        Key: `images/${filename}`,
        Body: file,
        ContentType: 'image/jpeg',
      });

      return {
        success: true,
        key: `images/${filename}`,
        etag: result.ETag,
      };
    } catch (error) {
      throw new Error(`Failed to upload image: ${error.message}`);
    }
  }

  async generatePresignedUrl(key: string, expiresIn: number = 3600) {
    return this.s3Service.getSignedUrl({
      key: key,
    });
  }
}

@Injectable()
export class UserAvatarService {
  constructor(
    // Default S3 service
    private readonly s3Service: S3Service,
    // Avatar-specific S3 service
    @Inject('S3Service_AVATAR_S3')
    private readonly avatarS3: S3Service,
  ) {}

  async uploadAvatar(userId: string, file: Buffer) {
    const key = `avatars/${userId}.jpg`;

    const result = await this.avatarS3.upload({
      Key: key,
      Body: file,
      ContentType: 'image/jpeg',
    });

    return {
      userId,
      avatarKey: key,
      uploadResult: result,
    };
  }

  async getAvatarUrl(userId: string) {
    const key = `avatars/${userId}.jpg`;
    return this.avatarS3.getSignedUrl({
      key: key,
    });
  }
}

@Injectable()
export class DocumentService {
  constructor(
    private readonly s3Service: S3Service,
    @Inject('S3Service_BACKUP_S3')
    private readonly backupS3: S3Service,
  ) {}

  async uploadDocument(userId: string, file: Buffer, filename: string) {
    const key = `documents/${userId}/${filename}`;

    // Upload to main bucket
    const uploadResult = await this.s3Service.upload({
      Key: key,
      Body: file,
      ContentType: 'application/octet-stream',
    });

    // Create backup
    await this.backupS3.upload({
      Key: `backups/${key}`,
      Body: file,
    });

    return {
      documentKey: key,
      uploadResult,
      backedUp: true,
    };
  }
}

@Injectable()
export class PresignedUploadService {
  constructor(private readonly s3Service: S3Service) {}

  async createUploadUrl(
    userId: string,
    filename: string,
    contentType: string,
    maxSize: number = 1024 * 1024 * 5, // 5MB
  ) {
    const key = `uploads/${userId}/${Date.now()}-${filename}`;

    const presignedPost = await this.s3Service.createPresignedPost({
      key: key,
      contentLengthRange: [0, maxSize],
      expire: 300, // 5 minutes
    });

    return {
      uploadUrl: presignedPost.url,
      fields: presignedPost.fields,
      key,
    };
  }
}

// Example of error handling
@Injectable()
export class RobustFileService {
  private readonly logger = console; // In real app, use NestJS Logger

  constructor(private readonly s3Service: S3Service) {}

  async uploadWithRetry(
    key: string,
    file: Buffer,
    maxRetries: number = 3,
  ): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(`Upload attempt ${attempt} for key: ${key}`);

        const result = await this.s3Service.upload({
          Key: key,
          Body: file,
        });

        this.logger.log(`Upload successful for key: ${key}`);
        return result;
      } catch (error) {
        lastError = error as Error;
        this.logger.error(
          `Upload attempt ${attempt} failed for key: ${key}`,
          error,
        );

        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(
      `Failed to upload after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`,
    );
  }
}
