import { cloudinaryConfig } from '@app/modules/storage/modules/cloudinary/cloudinary.config';
import { CloudinaryService } from '@app/modules/storage/modules/cloudinary/cloudinary.service';
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  CloudinaryAsyncOptions,
  CloudinaryModuleOptions,
  CloudinaryOptionsFactory,
  CLOUDINARY_OPTIONS,
  createCloudinaryOptionsToken,
} from './cloudinary.interfaces';

/**
 * CloudinaryModule provides file storage capabilities using Cloudinary cloud service.
 *
 * This module can be configured in multiple ways:
 *
 * 1. Static configuration using `forRoot()` method:
 *    ```typescript
 *    // Basic usage
 *    CloudinaryModule.forRoot({
 *      cloudName: 'my-cloud',
 *      apiKey: 'my-key',
 *      apiSecret: 'my-secret',
 *      folder: 'uploads',
 *      isGlobal: true
 *    })
 *
 *    // With custom token for multiple instances
 *    CloudinaryModule.forRoot({
 *      cloudName: 'my-cloud',
 *      folder: 'avatars',
 *      token: 'AVATAR_CLOUDINARY'
 *    })
 *    ```
 *
 * 2. Async configuration using `forRootAsync()` method:
 *    ```typescript
 *    // Using factory function
 *    CloudinaryModule.forRootAsync({
 *      imports: [ConfigModule],
 *      useFactory: (configService: ConfigService) => ({
 *        cloudName: configService.get('CLOUDINARY_CLOUD_NAME'),
 *        apiKey: configService.get('CLOUDINARY_API_KEY'),
 *        apiSecret: configService.get('CLOUDINARY_API_SECRET')
 *      }),
 *      inject: [ConfigService]
 *    })
 *
 *    // Using class
 *    CloudinaryModule.forRootAsync({
 *      useClass: CloudinaryConfigService
 *    })
 *
 *    // Using existing provider
 *    CloudinaryModule.forRootAsync({
 *      useExisting: ConfigService
 *    })
 *    ```
 *
 * 3. Default configuration using environment variables:
 *    ```typescript
 *    // Uses environment variables or defaults
 *    CloudinaryModule
 *    ```
 *
 * 4. Feature-specific instances using `forFeature()` method:
 *    ```typescript
 *    // Create a specific instance with its own configuration
 *    CloudinaryModule.forFeature('AVATAR_CLOUDINARY', {
 *      folder: 'avatars',
 *      secure: true
 *    })
 *    ```
 */

@Module({
  imports: [ConfigModule.forFeature(cloudinaryConfig)],
  providers: [CloudinaryService],
  exports: [CloudinaryService],
})
export class CloudinaryModule {
  /**
   * Get a specific instance of CloudinaryService using a custom token.
   *
   * This method allows you to create multiple instances of the CloudinaryModule
   * with different configurations. This is useful when you need to store different
   * types of files in different folders or with different settings.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     // Main cloudinary for general files
   *     CloudinaryModule.forRoot({
   *       cloudName: 'my-cloud',
   *       folder: 'uploads/general'
   *     }),
   *     // Specific cloudinary for avatars
   *     CloudinaryModule.forFeature('AVATAR_CLOUDINARY', {
   *       folder: 'uploads/avatars',
   *       secure: true
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In your service:
   * @Injectable()
   * export class UserService {
   *   constructor(
   *     private readonly cloudinaryService: CloudinaryService,
   *     @Inject('CloudinaryService_AVATAR_CLOUDINARY')
   *     private readonly avatarCloudinary: CloudinaryService
   *   ) {}
   * }
   * ```
   *
   * @param token The token used to identify the specific instance
   * @param options Optional configuration options for this specific instance
   * @returns A dynamic module with a specific CloudinaryService instance
   */
  static forFeature(
    token: string | symbol,
    options?: CloudinaryModuleOptions,
  ): DynamicModule {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const optionsToken = createCloudinaryOptionsToken(tokenString);
    const serviceToken = `CloudinaryService_${tokenString}`;

    // Create a provider for the options
    const optionsProvider = {
      provide: optionsToken,
      useValue: options,
    };

    // Create a provider for the specific instance
    const serviceProvider = {
      provide: serviceToken,
      useFactory: (
        options: CloudinaryModuleOptions,
        configService: ConfigService,
      ) => {
        return new CloudinaryService(configService, options);
      },
      inject: [optionsToken, ConfigService],
    };

    return {
      module: CloudinaryModule,
      providers: [optionsProvider, serviceProvider],
      exports: [serviceToken],
    };
  }

  /**
   * Get a specific instance of CloudinaryService using a custom token with async options.
   *
   * This method allows you to create multiple instances of the CloudinaryModule
   * with different configurations loaded asynchronously. This is useful when you need
   * to store different types of files in different folders and the configuration
   * needs to be loaded from a service or other async source.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     // Main cloudinary for general files
   *     CloudinaryModule.forRoot(),
   *     // Specific cloudinary for avatars with async config
   *     CloudinaryModule.forFeatureAsync('AVATAR_CLOUDINARY', {
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         folder: configService.get('AVATAR_FOLDER'),
   *         secure: configService.get('AVATAR_SECURE') === 'true'
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In your service:
   * @Injectable()
   * export class UserService {
   *   constructor(
   *     @Inject('CloudinaryService_AVATAR_CLOUDINARY')
   *     private readonly avatarCloudinary: CloudinaryService
   *   ) {}
   *
   *   async uploadAvatar(userId: string, file: Buffer) {
   *     return this.avatarCloudinary.uploadFile(file, `${userId}.jpg`);
   *   }
   * }
   * ```
   *
   * @param token The token used to identify the specific instance
   * @param asyncOptions Async configuration options for this specific instance
   * @returns A dynamic module with a specific CloudinaryService instance
   */
  static forFeatureAsync(
    token: string | symbol,
    asyncOptions: CloudinaryAsyncOptions,
  ): DynamicModule {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const optionsToken = createCloudinaryOptionsToken(tokenString);
    const serviceToken = `CloudinaryService_${tokenString}`;

    const imports = [ConfigModule.forFeature(cloudinaryConfig)];
    if (asyncOptions.imports?.length) {
      imports.push(...asyncOptions.imports);
    }

    // Create async providers for the options
    const optionsProviders = this.createAsyncProviders(
      asyncOptions,
      optionsToken,
    );

    // Create a provider for the specific instance
    const serviceProvider = {
      provide: serviceToken,
      useFactory: (
        options: CloudinaryModuleOptions,
        configService: ConfigService,
      ) => {
        return new CloudinaryService(configService, options);
      },
      inject: [optionsToken, ConfigService],
    };

    return {
      module: CloudinaryModule,
      imports,
      providers: [...optionsProviders, serviceProvider],
      exports: [serviceToken],
    };
  }

  /**
   * Register the module with static options.
   *
   * This method allows you to configure the CloudinaryModule with static options
   * at the time of module import. It's useful when you know your configuration values
   * at application bootstrap time.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     CloudinaryModule.forRoot({
   *       cloudName: 'my-cloud',
   *       apiKey: 'my-key',
   *       apiSecret: 'my-secret',
   *       folder: 'uploads',
   *       isGlobal: true
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * @param options Configuration options for the CloudinaryModule
   * @returns A dynamic module configuration
   */
  static forRoot(options?: CloudinaryModuleOptions): DynamicModule {
    // Use custom token if provided, otherwise use default token
    const token = options?.token
      ? createCloudinaryOptionsToken(options.token.toString())
      : CLOUDINARY_OPTIONS;

    const optionsProvider: Provider = {
      provide: token,
      useValue: options,
    };

    // Create a custom service provider with the specific token
    const serviceProvider = {
      provide: CloudinaryService,
      useFactory: (
        options: CloudinaryModuleOptions,
        configService: ConfigService,
      ) => {
        return new CloudinaryService(configService, options);
      },
      inject: [token, ConfigService],
    };

    // If a custom token is provided, create an additional named service provider
    const providers: Provider[] = [optionsProvider, serviceProvider];

    if (options?.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();

      const serviceToken = `CloudinaryService_${tokenString}`;

      const namedServiceProvider = {
        provide: serviceToken,
        useFactory: (
          options: CloudinaryModuleOptions,
          configService: ConfigService,
        ) => {
          return new CloudinaryService(configService, options);
        },
        inject: [token, ConfigService],
      };

      providers.push(namedServiceProvider);
    }

    const exports: any[] = [CloudinaryService];

    if (options?.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();
      exports.push(`CloudinaryService_${tokenString}`);
    }

    return {
      global: options?.isGlobal,
      module: CloudinaryModule,
      imports: [ConfigModule.forFeature(cloudinaryConfig)],
      providers,
      exports,
    };
  }

  /**
   * Register the module with async options.
   *
   * This method allows you to configure the CloudinaryModule asynchronously,
   * which is useful when configuration values are not available at application bootstrap
   * time and need to be loaded from a configuration service, database, or other source.
   *
   * Example with factory function:
   * ```typescript
   * @Module({
   *   imports: [
   *     ConfigModule.forRoot(),
   *     CloudinaryModule.forRootAsync({
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         cloudName: configService.get('CLOUDINARY_CLOUD_NAME'),
   *         apiKey: configService.get('CLOUDINARY_API_KEY'),
   *         apiSecret: configService.get('CLOUDINARY_API_SECRET'),
   *         isGlobal: true
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * Example with class:
   * ```typescript
   * @Injectable()
   * class CloudinaryConfigService implements CloudinaryOptionsFactory {
   *   constructor(private configService: ConfigService) {}
   *
   *   createCloudinaryOptions(): CloudinaryModuleOptions {
   *     return {
   *       cloudName: this.configService.get('CLOUDINARY_CLOUD_NAME'),
   *       apiKey: this.configService.get('CLOUDINARY_API_KEY'),
   *       apiSecret: this.configService.get('CLOUDINARY_API_SECRET')
   *     };
   *   }
   * }
   *
   * @Module({
   *   imports: [
   *     CloudinaryModule.forRootAsync({
   *       imports: [ConfigModule],
   *       useClass: CloudinaryConfigService
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * @param options Async configuration options for the CloudinaryModule
   * @returns A dynamic module configuration
   */
  static forRootAsync(options: CloudinaryAsyncOptions): DynamicModule {
    const imports = [ConfigModule.forFeature(cloudinaryConfig)];

    if (options.imports?.length) {
      imports.push(...options.imports);
    }

    // Use custom token if provided, otherwise use default token
    const token = options.token
      ? createCloudinaryOptionsToken(options.token.toString())
      : CLOUDINARY_OPTIONS;

    const asyncProviders = this.createAsyncProviders(options, token);

    // Create a custom service provider with the specific token
    const serviceProvider = {
      provide: CloudinaryService,
      useFactory: (
        options: CloudinaryModuleOptions,
        configService: ConfigService,
      ) => {
        return new CloudinaryService(configService, options);
      },
      inject: [token, ConfigService],
    };

    // Combine all providers
    const providers: Provider[] = [...asyncProviders, serviceProvider];

    // If a custom token is provided, create an additional named service provider
    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();

      const serviceToken = `CloudinaryService_${tokenString}`;

      const namedServiceProvider = {
        provide: serviceToken,
        useFactory: (
          options: CloudinaryModuleOptions,
          configService: ConfigService,
        ) => {
          return new CloudinaryService(configService, options);
        },
        inject: [token, ConfigService],
      };

      providers.push(namedServiceProvider);
    }

    const exports: any[] = [CloudinaryService];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();
      exports.push(`CloudinaryService_${tokenString}`);
    }

    return {
      global: options.isGlobal,
      module: CloudinaryModule,
      imports,
      providers,
      exports,
    };
  }

  private static createAsyncProviders(
    options: CloudinaryAsyncOptions,
    token: string = CLOUDINARY_OPTIONS,
  ): Provider[] {
    if (options.useExisting || options.useFactory) {
      return [this.createAsyncOptionsProvider(options, token)];
    }

    // Make sure useClass is defined before using it
    if (!options.useClass) {
      throw new Error(
        'Invalid async options: useClass, useExisting, or useFactory must be provided',
      );
    }

    return [
      this.createAsyncOptionsProvider(options, token),
      {
        provide: options.useClass,
        useClass: options.useClass,
      },
    ];
  }

  private static createAsyncOptionsProvider(
    options: CloudinaryAsyncOptions,
    token: string = CLOUDINARY_OPTIONS,
  ): Provider {
    if (options.useFactory) {
      return {
        provide: token,
        useFactory: options.useFactory,
        inject: options.inject || [],
      };
    }

    // Make sure either useExisting or useClass is defined
    const injectToken = options.useExisting || options.useClass;
    if (!injectToken) {
      throw new Error(
        'Invalid async options: useClass, useExisting, or useFactory must be provided',
      );
    }

    return {
      provide: token,
      useFactory: async (optionsFactory: CloudinaryOptionsFactory) =>
        await optionsFactory.createCloudinaryOptions(),
      inject: [injectToken],
    };
  }
}
