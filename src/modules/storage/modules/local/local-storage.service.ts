import { LocalStorageConfigType } from '@app/modules/storage/modules/local/local-storage.config';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import {
  LocalStorageModuleOptions,
  LOCAL_STORAGE_OPTIONS,
} from './local-storage.interfaces';

@Injectable()
export class LocalStorageService {
  private readonly logger = new Logger(LocalStorageService.name);
  private readonly uploadDir: string;
  private readonly baseUrl: string;

  constructor(
    private readonly configService: ConfigService<{
      localStorage: LocalStorageConfigType;
    }>,
    @Optional()
    @Inject(LOCAL_STORAGE_OPTIONS)
    private readonly options?: LocalStorageModuleOptions,
  ) {
    // If options are provided via injection, use them
    // Otherwise, fall back to ConfigService
    if (options) {
      this.uploadDir = options.uploadDir || 'uploads';
      this.baseUrl = options.baseUrl || 'http://localhost:3000';
      this.logger.log(
        `Using injected LocalStorageModuleOptions${options.token ? ` for token: ${String(options.token)}` : ''}`,
      );
    } else {
      const localConfig = this.configService.get('localStorage', {
        infer: true,
      });

      if (localConfig) {
        this.uploadDir = localConfig.uploadDir;
        this.baseUrl = localConfig.baseUrl;
        this.logger.log('Using ConfigService for LocalStorage configuration');
      } else {
        // Fallback to defaults if neither options nor config are available
        this.uploadDir = 'uploads';
        this.baseUrl = 'http://localhost:3000';
        this.logger.log('Using default LocalStorage configuration');
      }
    }

    this.logger.log(
      `LocalStorage configured with uploadDir: ${this.uploadDir}, baseUrl: ${this.baseUrl}`,
    );
  }

  /**
   * Upload a file using the local storage provider
   */
  async uploadFile(
    file: Buffer,
    filePath: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    options?: Record<string, any>,
  ) {
    const fullPath = path.join(process.cwd(), this.uploadDir, filePath);
    const dirPath = path.dirname(fullPath);

    // Ensure directory exists
    await this.ensureDirectoryExists(dirPath);

    // Write file to disk
    await fs.writeFile(fullPath, file);

    // Get file stats
    const fileStats = await fs.stat(fullPath);

    return {
      url: `/${this.uploadDir}/${filePath}`,
      path: filePath,
      metadata: {
        size: fileStats.size,
        lastModified: fileStats.mtime,
      },
    };
  }

  /**
   * Delete a file using the local storage provider
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, filePath);
      await fs.unlink(fullPath);
      return true;
    } catch (error) {
      console.error(`Error deleting file: ${error.message}`);
      return false;
    }
  }

  /**
   * Get public URL for a file
   */
  getPublicUrl(filePath: string): string {
    return `${this.baseUrl}/${this.uploadDir}/${filePath}`;
  }

  /**
   * Check if a file exists in local storage
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, filePath);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(filePath: string): Promise<{
    size: number;
    lastModified: Date;
    created: Date;
  } | null> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, filePath);
      const stats = await fs.stat(fullPath);
      return {
        size: stats.size,
        lastModified: stats.mtime,
        created: stats.birthtime,
      };
    } catch (error) {
      this.logger.error(`Error getting file metadata: ${error.message}`);
      return null;
    }
  }

  /**
   * List files in a directory
   */
  async listFiles(directoryPath: string = ''): Promise<string[]> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, directoryPath);
      const files = await fs.readdir(fullPath);
      return files;
    } catch (error) {
      this.logger.error(`Error listing files: ${error.message}`);
      return [];
    }
  }

  /**
   * Create a directory
   */
  async createDirectory(directoryPath: string): Promise<boolean> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, directoryPath);
      await fs.mkdir(fullPath, { recursive: true });
      return true;
    } catch (error) {
      this.logger.error(`Error creating directory: ${error.message}`);
      return false;
    }
  }

  /**
   * Delete a directory and all its contents
   */
  async deleteDirectory(directoryPath: string): Promise<boolean> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, directoryPath);
      await fs.rmdir(fullPath, { recursive: true });
      return true;
    } catch (error) {
      this.logger.error(`Error deleting directory: ${error.message}`);
      return false;
    }
  }

  /**
   * Copy a file to another location
   */
  async copyFile(
    sourcePath: string,
    destinationPath: string,
  ): Promise<boolean> {
    try {
      const sourceFullPath = path.join(
        process.cwd(),
        this.uploadDir,
        sourcePath,
      );
      const destFullPath = path.join(
        process.cwd(),
        this.uploadDir,
        destinationPath,
      );

      // Ensure destination directory exists
      const destDir = path.join(destFullPath, '..');
      await fs.mkdir(destDir, { recursive: true });

      await fs.copyFile(sourceFullPath, destFullPath);
      return true;
    } catch (error) {
      this.logger.error(`Error copying file: ${error.message}`);
      return false;
    }
  }

  /**
   * Move a file to another location
   */
  async moveFile(
    sourcePath: string,
    destinationPath: string,
  ): Promise<boolean> {
    try {
      const sourceFullPath = path.join(
        process.cwd(),
        this.uploadDir,
        sourcePath,
      );
      const destFullPath = path.join(
        process.cwd(),
        this.uploadDir,
        destinationPath,
      );

      // Ensure destination directory exists
      const destDir = path.join(destFullPath, '..');
      await fs.mkdir(destDir, { recursive: true });

      await fs.rename(sourceFullPath, destFullPath);
      return true;
    } catch (error) {
      this.logger.error(`Error moving file: ${error.message}`);
      return false;
    }
  }

  /**
   * Get file content as buffer
   */
  async getFileContent(filePath: string): Promise<Buffer | null> {
    try {
      const fullPath = path.join(process.cwd(), this.uploadDir, filePath);
      return await fs.readFile(fullPath);
    } catch (error) {
      this.logger.error(`Error reading file content: ${error.message}`);
      return null;
    }
  }

  /**
   * Ensure a directory exists
   * @param dirPath - Directory path
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.stat(dirPath);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(dirPath, { recursive: true });
    }
  }
}
