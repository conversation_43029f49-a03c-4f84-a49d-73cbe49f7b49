import { Inject, Injectable } from '@nestjs/common';
import { createLocalStorageOptionsToken } from './local-storage.interfaces';
import { LocalStorageService } from './local-storage.service';

/**
 * Factory for getting specific instances of LocalStorageService
 * This allows for multiple instances with different configurations
 */
@Injectable()
export class LocalStorageFactory {
  constructor(
    // Inject the default LocalStorageService
    private readonly defaultStorage: LocalStorageService,
  ) {}

  /**
   * Get a decorator to inject a specific instance of LocalStorageService by token
   * @param token The token used to identify the specific instance
   * @returns A decorator that injects the specific service instance
   */
  static get(token: string | symbol): any {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const serviceToken = `LocalStorageService_${tokenString}`;

    // Create a decorator factory that injects the specific service instance
    return Inject(serviceToken);
  }

  /**
   * Create a custom injection token for a LocalStorageService instance
   * @param name The name to use for the custom token
   * @returns A unique service token string
   */
  static createServiceToken(name: string | symbol): string {
    const tokenString =
      typeof name === 'symbol' ? name.description : name.toString();
    return `LocalStorageService_${tokenString}`;
  }

  /**
   * Create a custom options token for a LocalStorageService instance
   * @param name The name to use for the custom token
   * @returns A unique options token string
   */
  static createOptionsToken(name: string | symbol): string {
    return createLocalStorageOptionsToken(
      typeof name === 'symbol' ? name.description : name.toString(),
    );
  }

  /**
   * Get the default LocalStorageService instance
   * @returns The default LocalStorageService instance
   */
  getDefault(): LocalStorageService {
    return this.defaultStorage;
  }
}
