import validateConfig from '@app/common/config/validate-config';
import { StorageProvider } from '@modules/storage/enums/storage-provider.enum';
import { registerAs } from '@nestjs/config';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export type StorageConfigType = {
  provider: StorageProvider;
  s3?: {
    region: string;
    bucket: string;
    accessKeyId?: string;
    secretAccessKey?: string;
    endpoint?: string;
    cdnDomain?: string;
  };
  cloudinary?: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
    folder: string;
    secure: boolean;
  };
  local?: {
    uploadDir: string;
    baseUrl: string;
  };
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  STORAGE_PROVIDER: StorageProvider;

  @IsString()
  @IsOptional()
  AWS_REGION: string;

  @IsString()
  @IsOptional()
  AWS_S3_BUCKET: string;

  @IsString()
  @IsOptional()
  AWS_ACCESS_KEY_ID: string;

  @IsString()
  @IsOptional()
  AWS_SECRET_ACCESS_KEY: string;

  @IsString()
  @IsOptional()
  AWS_S3_ENDPOINT: string;

  @IsString()
  @IsOptional()
  AWS_CLOUDFRONT_DOMAIN: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_CLOUD_NAME: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_API_KEY: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_API_SECRET: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_FOLDER: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_SECURE: string;

  @IsString()
  @IsOptional()
  LOCAL_STORAGE_UPLOAD_DIR: string;

  @IsString()
  @IsOptional()
  LOCAL_STORAGE_BASE_URL: string;
}

export const storageConfig = registerAs<StorageConfigType>('storage', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    provider:
      (process.env.STORAGE_PROVIDER as StorageProvider) ||
      StorageProvider.LOCAL,

    // AWS S3 configuration
    s3: {
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_S3_BUCKET || 'my-bucket',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      endpoint: process.env.AWS_S3_ENDPOINT, // Optional, for custom endpoints
      cdnDomain: process.env.AWS_CLOUDFRONT_DOMAIN, // Optional, for CloudFront
    },

    // Cloudinary configuration
    cloudinary: {
      cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
      apiKey: process.env.CLOUDINARY_API_KEY || '',
      apiSecret: process.env.CLOUDINARY_API_SECRET || '',
      folder: process.env.CLOUDINARY_FOLDER || 'assets',
      secure: process.env.CLOUDINARY_SECURE === 'true',
    },
    local: {
      uploadDir: process.env.LOCAL_STORAGE_UPLOAD_DIR || 'uploads',
      baseUrl: process.env.LOCAL_STORAGE_BASE_URL || 'http://localhost:3000',
    },
  };
});
