import { StorageConfigType } from '@app/modules/storage/storage.config';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IStorageProvider } from '../interfaces/storage-provider.interface';
import { LocalStorageService } from '../modules/local/local-storage.service';

@Injectable()
export class LocalStorageProvider implements IStorageProvider {
  constructor(
    private readonly configService: ConfigService<{
      storage: StorageConfigType;
    }>,
    private readonly localStorageService: LocalStorageService,
  ) {}

  /**
   * Upload a file to local storage using LocalStorageService
   * @param file - File buffer
   * @param filePath - Path to store the file
   * @param options - Upload options
   * @returns Upload result
   */
  async uploadFile(
    file: Buffer,
    filePath: string,
    options?: Record<string, any>,
  ): Promise<{
    url: string;
    path: string;
    cdnUrl?: string;
    metadata?: Record<string, any>;
  }> {
    return await this.localStorageService.uploadFile(file, filePath, options);
  }

  /**
   * Delete a file from local storage using LocalStorageService
   * @param filePath - Path of the file to delete
   * @returns Deletion result
   */
  async deleteFile(filePath: string): Promise<boolean> {
    return await this.localStorageService.deleteFile(filePath);
  }

  /**
   * Get a signed URL for a file using LocalStorageService
   * For local storage, this returns the same as public URL
   * @param filePath - Path of the file
   * @param _expiresIn - Expiration time in seconds (not used for local storage)
   * @returns Public URL
   */
  getSignedUrl(
    filePath: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    expiresIn?: number,
  ): Promise<string> {
    const publicUrl = this.localStorageService.getPublicUrl(filePath);
    return Promise.resolve(publicUrl);
  }

  /**
   * Get the public URL for a file using LocalStorageService
   * @param filePath - Path of the file
   * @returns Public URL
   */
  getPublicUrl(filePath: string): string {
    return this.localStorageService.getPublicUrl(filePath);
  }
}
