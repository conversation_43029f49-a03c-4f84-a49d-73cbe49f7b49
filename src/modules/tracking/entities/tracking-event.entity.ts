import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { Column, Entity, Index } from 'typeorm';

/**
 * Simplified tracking event entity
 * Represents a user interaction event in the system
 */
@Entity('tracking_events')
@Index(['eventType'])
@Index(['userId'])
@Index(['anonymousId'])
@Index(['sessionId'])
@Index(['timestamp'])
@Index(['ipAddress'])
@Index(['pagePath'])
export class TrackingEventEntity extends CustomBaseEntity {
  @Column({
    name: 'event_type',
    type: 'varchar',
    length: 50,
  })
  eventType: string; // PAGE_VIEW, CLICK, CUSTOM

  @Column({
    name: 'user_id',
    type: 'uuid',
    nullable: true,
  })
  userId?: string;

  @Column({
    name: 'anonymous_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  anonymousId?: string;

  @Column({
    name: 'session_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  sessionId?: string;

  @Column({
    name: 'page_url',
    type: 'text',
    nullable: true,
  })
  pageUrl?: string;

  @Column({
    name: 'page_path',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  pagePath?: string; // Path portion of the URL (e.g., /products/123)

  @Column({
    name: 'referrer',
    type: 'text',
    nullable: true,
  })
  referrer?: string;

  @Column({
    name: 'user_agent',
    type: 'text',
    nullable: true,
  })
  userAgent?: string;

  @Column({
    name: 'device_type',
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  deviceType?: string; // mobile, desktop, tablet

  @Column({
    name: 'ip_address',
    type: 'inet',
    nullable: true,
  })
  ipAddress?: string;

  @Column({
    name: 'properties',
    type: 'jsonb',
    nullable: true,
  })
  properties?: Record<string, any>;

  @Column({
    name: 'timestamp',
    type: 'timestamp',
    nullable: false,
  })
  timestamp: Date;
}
