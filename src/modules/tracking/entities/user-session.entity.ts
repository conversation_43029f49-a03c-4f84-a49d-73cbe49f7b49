import { Column, Entity, Index } from 'typeorm';
import { CustomBaseEntity } from '../../../common/entities/base.entity';

/**
 * Simplified entity for user sessions
 */
@Entity('user_sessions')
@Index(['sessionId'], { unique: true })
@Index(['userId'])
@Index(['anonymousId'])
@Index(['startTime'])
@Index(['ipAddress'])
export class UserSessionEntity extends CustomBaseEntity {
  @Column({
    name: 'session_id',
    type: 'varchar',
    length: 100,
    unique: true,
  })
  sessionId: string;

  @Column({
    name: 'user_id',
    type: 'uuid',
    nullable: true,
  })
  userId?: string;

  @Column({
    name: 'anonymous_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  anonymousId?: string;

  @Column({
    name: 'user_agent',
    type: 'text',
    nullable: true,
  })
  userAgent?: string;

  @Column({
    name: 'device_type',
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  deviceType?: string;

  @Column({
    name: 'ip_address',
    type: 'inet',
    nullable: true,
  })
  ipAddress?: string;

  @Column({
    name: 'start_time',
    type: 'timestamp',
  })
  startTime: Date;

  @Column({
    name: 'end_time',
    type: 'timestamp',
    nullable: true,
  })
  endTime?: Date;

  @Column({
    name: 'last_activity',
    type: 'timestamp',
  })
  lastActivity: Date;

  @Column({
    name: 'page_views',
    type: 'integer',
    default: 0,
  })
  pageViews: number;

  @Column({
    name: 'properties',
    type: 'jsonb',
    nullable: true,
  })
  properties?: Record<string, any>;
}
