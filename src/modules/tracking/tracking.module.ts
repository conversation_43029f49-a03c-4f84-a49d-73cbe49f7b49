import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrackingController } from './controllers';
import { TrackingEventEntity, UserSessionEntity } from './entities';
import { SessionService, TrackingService } from './services';
import { trackingConfig } from './tracking.config';

/**
 * Simplified Tracking module
 * Provides basic functionality for tracking user interactions with configuration support
 */
@Module({
  imports: [
    // Register tracking configuration
    ConfigModule.forFeature(trackingConfig),
    // Register TypeORM entities
    TypeOrmModule.forFeature([TrackingEventEntity, UserSessionEntity]),
  ],
  controllers: [TrackingController],
  providers: [TrackingService, SessionService],
  exports: [TrackingService, SessionService],
})
export class TrackingModule {}
