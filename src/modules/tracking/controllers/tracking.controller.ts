import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Post,
  Req,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import {
  BatchTrackingDto,
  PageViewDto,
  SessionDto,
  TrackingEventDto,
} from '../dto';
import { SessionResponseDto } from '../dto/session-response.dto';
import { TrackingService } from '../services';
import { SessionService } from '../services/session.service';

/**
 * Simplified controller for core tracking endpoints
 */
@ApiTags('tracking')
@Controller('api/tracking')
export class TrackingController {
  private readonly logger = new Logger(TrackingController.name);

  constructor(
    private readonly trackingService: TrackingService,
    private readonly sessionService: SessionService,
  ) {}

  /**
   * Track a single event
   * @param eventData Event data
   * @param req Request object
   * @returns Success status
   */
  @Post('events')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Track a single event' })
  @ApiBody({ type: TrackingEventDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Event tracked successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid event data',
  })
  async trackEvent(
    @Body() eventData: TrackingEventDto,
    @Req() req: Request,
  ): Promise<{ success: boolean; eventId: string }> {
    try {
      // Enrich event data with request information
      const enrichedEventData = this.enrichWithRequestData(eventData, req);

      const event = await this.trackingService.trackEvent(enrichedEventData);

      return {
        success: true,
        eventId: event.id,
      };
    } catch (error) {
      this.logger.error(`Error tracking event: ${error.message}`, error.stack);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to track event. Please try again later.',
      );
    }
  }

  /**
   * Track multiple events in batch
   * @param batchData Batch of events data
   * @param req Request object
   * @returns Success status
   */
  @Post('batch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Track multiple events in batch' })
  @ApiBody({ type: BatchTrackingDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Events tracked successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid batch data',
  })
  async trackBatchEvents(
    @Body() batchData: BatchTrackingDto,
    @Req() req: Request,
  ): Promise<{ success: boolean; count: number }> {
    try {
      // Validate batch data
      if (
        !batchData.events ||
        !Array.isArray(batchData.events) ||
        batchData.events.length === 0
      ) {
        throw new BadRequestException(
          'Invalid batch data. Events array is required.',
        );
      }

      // Validate batch size limit
      if (batchData.events.length > 50) {
        throw new BadRequestException(
          'Batch size exceeds maximum limit of 50 events.',
        );
      }

      // Enrich each event with request information
      const enrichedEvents = batchData.events.map((event) =>
        this.enrichWithRequestData(event, req),
      );

      const events = await this.trackingService.trackBatchEvents({
        events: enrichedEvents,
      });

      return {
        success: true,
        count: events.length,
      };
    } catch (error) {
      this.logger.error(
        `Error tracking batch events: ${error.message}`,
        error.stack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to track batch events. Please try again later.',
      );
    }
  }

  /**
   * Track a page view
   * @param pageData Page view data
   * @param req Request object
   * @returns Success status
   */
  @Post('pageview')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Track a page view' })
  @ApiBody({ type: PageViewDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Page view tracked successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid page view data',
  })
  async trackPageView(
    @Body() pageData: PageViewDto,
    @Req() req: Request,
  ): Promise<{ success: boolean; eventId: string }> {
    try {
      // Validate required fields for page view
      if (!pageData.pageUrl) {
        throw new BadRequestException(
          'Page URL is required for page view tracking',
        );
      }

      // Ensure the event type is PAGE_VIEW
      pageData.eventType = 'PAGE_VIEW';

      // Enrich event data with request information
      const enrichedPageData = this.enrichWithRequestData(pageData, req);

      const event = await this.trackingService.trackEvent(enrichedPageData);

      return {
        success: true,
        eventId: event.id,
      };
    } catch (error) {
      this.logger.error(
        `Error tracking page view: ${error.message}`,
        error.stack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to track page view. Please try again later.',
      );
    }
  }

  /**
   * Enrich event data with request information
   * @param eventData Event data
   * @param req Request object
   * @returns Enriched event data
   */
  private enrichWithRequestData(
    eventData: TrackingEventDto,
    req: Request,
  ): TrackingEventDto {
    // Validate event type
    if (!eventData.eventType || typeof eventData.eventType !== 'string') {
      throw new BadRequestException(
        'Event type is required and must be a string',
      );
    }

    // Sanitize and validate event type
    eventData.eventType = eventData.eventType.trim().toUpperCase();
    if (eventData.eventType.length > 50) {
      throw new BadRequestException('Event type must be 50 characters or less');
    }

    // Add user agent if not provided (sanitize it)
    if (!eventData.userAgent) {
      const userAgent = req.headers['user-agent'] || 'Unknown';
      eventData.userAgent = userAgent.substring(0, 500); // Limit length
    }

    // Add referrer if not provided (validate URL format)
    if (!eventData.referrer && req.headers.referer) {
      const referrer = req.headers.referer;
      if (this.isValidUrl(referrer)) {
        eventData.referrer = referrer.substring(0, 2000); // Limit length
      }
    }

    // Validate page URL if provided
    if (eventData.pageUrl) {
      if (!this.isValidUrl(eventData.pageUrl)) {
        throw new BadRequestException('Invalid page URL format');
      }
      eventData.pageUrl = eventData.pageUrl.substring(0, 2000); // Limit length
    }

    // Add timestamp if not provided
    if (!eventData.timestamp) {
      eventData.timestamp = new Date();
    } else {
      // Validate timestamp is not too far in the future
      const now = new Date();
      const maxFutureTime = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes in future
      if (eventData.timestamp > maxFutureTime) {
        eventData.timestamp = now;
      }
    }

    // Add user ID if authenticated (validate UUID format if provided)
    if (!eventData.userId && req.user && 'id' in req.user) {
      eventData.userId = req.user.id as string;
    }
    if (eventData.userId && !this.isValidUuid(eventData.userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    // Validate anonymous ID format if provided
    if (eventData.anonymousId && eventData.anonymousId.length > 100) {
      throw new BadRequestException(
        'Anonymous ID must be 100 characters or less',
      );
    }

    // Validate session ID format if provided
    if (eventData.sessionId && eventData.sessionId.length > 100) {
      throw new BadRequestException(
        'Session ID must be 100 characters or less',
      );
    }

    // Add IP address if not provided
    if (!eventData.ipAddress) {
      const ip = req.ip || req.socket.remoteAddress || '0.0.0.0';
      eventData.ipAddress = ip; // Store as plain text as requested
    }

    return eventData;
  }

  /**
   * Validate URL format
   * @param url URL to validate
   * @returns True if valid URL
   */
  private isValidUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return ['http:', 'https:'].includes(parsedUrl.protocol);
    } catch {
      return false;
    }
  }

  /**
   * Validate UUID format
   * @param uuid UUID to validate
   * @returns True if valid UUID
   */
  private isValidUuid(uuid: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Create a new session
   * @param req Request object
   * @param sessionData Session data
   * @returns Created session
   */
  @Post('session')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new tracking session' })
  @ApiBody({ type: SessionDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Session created successfully',
    type: SessionResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid session data',
  })
  async createSession(
    @Req() req: Request,
    @Body() sessionData: SessionDto,
  ): Promise<SessionResponseDto> {
    try {
      // Enrich session data with request information
      if (!sessionData.userAgent && req.headers['user-agent']) {
        sessionData.userAgent = req.headers['user-agent'];
      }

      // Add IP address if not provided
      if (!sessionData.ipAddress) {
        sessionData.ipAddress = req.ip || req.socket.remoteAddress || '0.0.0.0';
      }

      // Add user ID if authenticated
      if (!sessionData.userId && req.user && 'id' in req.user) {
        sessionData.userId = req.user.id as string;
      }

      const session = await this.sessionService.createSession(sessionData);

      return {
        sessionId: session.sessionId,
        userId: session.userId,
        anonymousId: session.anonymousId,
        startTime: session.startTime,
        success: true,
      };
    } catch (error) {
      this.logger.error(
        `Error creating session: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to create session');
    }
  }

  /**
   * End a session
   * @param sessionId Session ID
   * @returns Success status
   */
  @Post('session/:sessionId/end')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'End a tracking session' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Session ended successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  async endSession(
    @Param('sessionId') sessionId: string,
  ): Promise<{ success: boolean }> {
    try {
      await this.sessionService.endSession(sessionId);
      return { success: true };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error ending session: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to end session');
    }
  }
}
