import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TrackingEventEntity } from '../entities';
import { EventType } from '../enums';
import { TrackingService } from '../services';
import { TrackingController } from './tracking.controller';

describe('TrackingController', () => {
  let controller: TrackingController;
  let trackingService: TrackingService;

  const mockTrackingService = {
    trackEvent: jest.fn(),
    trackBatchEvents: jest.fn(),
    trackPageView: jest.fn(),
    identifyUser: jest.fn(),
    getConsentConfiguration: jest.fn(),
    updateConsentPreferences: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TrackingController],
      providers: [
        {
          provide: TrackingService,
          useValue: mockTrackingService,
        },
      ],
    }).compile();

    controller = module.get<TrackingController>(TrackingController);
    trackingService = module.get<TrackingService>(TrackingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('trackEvent', () => {
    it('should track an event successfully', async () => {
      const eventData = {
        eventType: EventType.PAGE_VIEW,
        sessionId: 'test-session',
        anonymousId: 'test-anonymous',
        pageUrl: 'https://example.com',
        timestamp: new Date(),
      };

      const mockEvent = {
        id: 'test-id',
      } as TrackingEventEntity;

      mockTrackingService.trackEvent.mockResolvedValue(mockEvent);

      const req = {
        ip: '127.0.0.1',
        headers: {
          'user-agent': 'test-agent',
          referer: 'https://referrer.com',
        },
        user: { id: 'test-user' },
      } as any;

      const result = await controller.trackEvent(eventData, req);

      expect(result).toEqual({
        success: true,
        eventId: 'test-id',
      });

      expect(mockTrackingService.trackEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: EventType.PAGE_VIEW,
          sessionId: 'test-session',
          anonymousId: 'test-anonymous',
          pageUrl: 'https://example.com',
          ipAddress: '127.0.0.1',
          userAgent: 'test-agent',
          referrer: 'https://referrer.com',
          userId: 'test-user',
        }),
      );
    });

    it('should handle errors', async () => {
      const eventData = {
        eventType: EventType.PAGE_VIEW,
        sessionId: 'test-session',
        anonymousId: 'test-anonymous',
        pageUrl: 'https://example.com',
      };

      mockTrackingService.trackEvent.mockRejectedValue(new Error('Test error'));

      const req = {} as any;

      await expect(controller.trackEvent(eventData, req)).rejects.toThrow();
    });
  });

  describe('trackBatchEvents', () => {
    it('should track batch events successfully', async () => {
      const batchData = {
        events: [
          {
            eventType: EventType.PAGE_VIEW,
            sessionId: 'test-session',
            anonymousId: 'test-anonymous',
            pageUrl: 'https://example.com',
          },
        ],
      };

      const mockEvents = [
        {
          id: 'test-id',
        },
      ] as TrackingEventEntity[];

      mockTrackingService.trackBatchEvents.mockResolvedValue(mockEvents);

      const req = {
        ip: '127.0.0.1',
        headers: {
          'user-agent': 'test-agent',
        },
      } as any;

      const result = await controller.trackBatchEvents(batchData, req);

      expect(result).toEqual({
        success: true,
        count: 1,
      });

      expect(mockTrackingService.trackBatchEvents).toHaveBeenCalledWith(
        expect.objectContaining({
          events: expect.arrayContaining([
            expect.objectContaining({
              eventType: EventType.PAGE_VIEW,
              sessionId: 'test-session',
              anonymousId: 'test-anonymous',
              pageUrl: 'https://example.com',
              ipAddress: '127.0.0.1',
              userAgent: 'test-agent',
            }),
          ]),
        }),
      );
    });

    it('should validate batch data', async () => {
      const batchData = {
        events: [],
      };

      const req = {} as any;

      await expect(controller.trackBatchEvents(batchData, req)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getConsentConfiguration', () => {
    it('should return consent configuration', async () => {
      const mockConfig = {
        categories: {
          necessary: {
            name: 'Necessary',
            required: true,
          },
        },
      };

      mockTrackingService.getConsentConfiguration.mockResolvedValue(mockConfig);

      const result = await controller.getConsentConfiguration();

      expect(result).toEqual(mockConfig);
      expect(mockTrackingService.getConsentConfiguration).toHaveBeenCalled();
    });
  });
});
