# Tracking Module Configuration
# Copy this file to your main .env file and adjust values as needed

# =============================================================================
# SESSION MANAGEMENT SETTINGS (Requirement: 4.5)
# =============================================================================

# Session timeout in minutes - how long a session stays active without activity
# Default: 30
TRACKING_SESSION_TIMEOUT_MINUTES=30

# How often to run session cleanup job in minutes
# Default: 60
TRACKING_SESSION_CLEANUP_INTERVAL_MINUTES=60

# Session inactivity timeout in minutes - mark session as inactive after this time
# Default: 15
TRACKING_SESSION_INACTIVITY_TIMEOUT_MINUTES=15

# =============================================================================
# DATA RETENTION SETTINGS (Requirement: 4.5)
# =============================================================================

# How many days to keep tracking events before deletion
# Default: 90
TRACKING_EVENT_RETENTION_DAYS=90

# How many days to keep session data before deletion
# Default: 30
TRACKING_SESSION_RETENTION_DAYS=30

# Number of records to process in each cleanup batch
# Default: 1000
TRACKING_CLEANUP_BATCH_SIZE=1000



# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# Development Environment (Relaxed settings)
# TRACKING_EVENT_RETENTION_DAYS=7
# TRACKING_SESSION_RETENTION_DAYS=7

# Production Environment (Strict settings)
# TRACKING_EVENT_RETENTION_DAYS=365
# TRACKING_SESSION_RETENTION_DAYS=90

# High Traffic Environment (Performance optimized)
# TRACKING_CLEANUP_BATCH_SIZE=5000