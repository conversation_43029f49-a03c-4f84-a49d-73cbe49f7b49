import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>Null, LessThan, Repository } from 'typeorm';
import { v7 as uuidv7 } from 'uuid';
import { SessionDto } from '../dto/session-request.dto';
import { UserSessionEntity } from '../entities';

/**
 * Simplified service for basic session management
 */
@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly SESSION_TIMEOUT_MINUTES = 30;

  constructor(
    @InjectRepository(UserSessionEntity)
    private readonly sessionRepository: Repository<UserSessionEntity>,
  ) {}

  /**
   * Create a new user session
   * @param sessionData Session data from request
   * @returns Created session
   */
  async createSession(sessionData: SessionDto): Promise<UserSessionEntity> {
    try {
      this.logger.debug(
        `Creating new session for user: ${sessionData.userId || 'anonymous'}`,
      );

      // Generate a unique session ID
      const sessionId = uuidv7();

      // Generate anonymous ID if not provided
      const anonymousId =
        sessionData.anonymousId || `anon_${uuidv7().replace(/-/g, '')}`;

      const now = new Date();

      // Create session entity
      const session = this.sessionRepository.create({
        sessionId,
        userId: sessionData.userId,
        anonymousId,
        userAgent: sessionData.userAgent,
        deviceType: sessionData.deviceType,
        ipAddress: sessionData.ipAddress,
        startTime: now,
        lastActivity: now,
        pageViews: 0,
        properties: sessionData.properties,
      });

      // Save and return the session
      return await this.sessionRepository.save(session);
    } catch (error) {
      this.logger.error(
        `Error creating session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get a session by ID
   * @param sessionId Session ID
   * @returns Session data
   */
  async getSession(sessionId: string): Promise<UserSessionEntity> {
    try {
      const session = await this.sessionRepository.findOne({
        where: { sessionId },
      });

      if (!session) {
        throw new NotFoundException(`Session with ID ${sessionId} not found`);
      }

      return session;
    } catch (error) {
      this.logger.error(
        `Error getting session ${sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update session activity
   * @param sessionId Session ID
   */
  async updateSessionActivity(sessionId: string): Promise<void> {
    try {
      const session = await this.sessionRepository.findOne({
        where: { sessionId },
      });

      if (!session) {
        this.logger.warn(`Session ${sessionId} not found for activity update`);
        return;
      }

      // Update last activity timestamp
      session.lastActivity = new Date();
      await this.sessionRepository.save(session);
    } catch (error) {
      this.logger.error(
        `Error updating session activity ${sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * End a session
   * @param sessionId Session ID
   * @returns Updated session
   */
  async endSession(sessionId: string): Promise<UserSessionEntity> {
    try {
      const session = await this.sessionRepository.findOne({
        where: { sessionId },
      });

      if (!session) {
        throw new NotFoundException(`Session with ID ${sessionId} not found`);
      }

      // Set end time
      session.endTime = new Date();
      session.lastActivity = session.endTime;

      // Save and return the updated session
      return await this.sessionRepository.save(session);
    } catch (error) {
      this.logger.error(
        `Error ending session ${sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle session timeout - automatically end expired sessions
   * @param sessionId Session ID
   * @returns Whether session was expired and ended
   */
  async handleSessionTimeout(sessionId: string): Promise<boolean> {
    try {
      const session = await this.sessionRepository.findOne({
        where: { sessionId },
      });

      if (!session || session.endTime) {
        return false; // Session not found or already ended
      }

      const now = new Date();
      const timeoutThreshold = new Date(
        now.getTime() - this.SESSION_TIMEOUT_MINUTES * 60 * 1000,
      );

      // Check if session has timed out
      if (session.lastActivity < timeoutThreshold) {
        session.endTime = session.lastActivity;
        await this.sessionRepository.save(session);

        this.logger.debug(
          `Session ${sessionId} automatically ended due to timeout`,
        );
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(
        `Error handling session timeout ${sessionId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Cleanup expired sessions - scheduled task
   * Runs every hour to clean up sessions that have exceeded timeout
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupExpiredSessions(): Promise<void> {
    try {
      this.logger.debug('Starting cleanup of expired sessions');

      const now = new Date();
      const timeoutThreshold = new Date(
        now.getTime() - this.SESSION_TIMEOUT_MINUTES * 60 * 1000,
      );

      // Find sessions that are still active but have timed out
      const expiredSessions = await this.sessionRepository.find({
        where: {
          endTime: IsNull(), // Still active
          lastActivity: LessThan(timeoutThreshold),
        },
      });

      if (expiredSessions.length === 0) {
        this.logger.debug('No expired sessions found');
        return;
      }

      // End all expired sessions
      for (const session of expiredSessions) {
        session.endTime = session.lastActivity;
      }

      await this.sessionRepository.save(expiredSessions);

      this.logger.log(`Cleaned up ${expiredSessions.length} expired sessions`);
    } catch (error) {
      this.logger.error(
        `Error during session cleanup: ${error.message}`,
        error.stack,
      );
    }
  }
}
