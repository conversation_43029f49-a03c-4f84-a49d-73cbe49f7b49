import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SessionDto } from '../dto/session-request.dto';
import { UserSessionEntity } from '../entities';
import { SessionService } from './session.service';

// Mock the entities to avoid import issues
jest.mock('../entities', () => ({
  UserSessionEntity: class MockUserSessionEntity {},
}));

describe('SessionService', () => {
  let service: SessionService;
  let repository: Repository<UserSessionEntity>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionService,
        {
          provide: getRepositoryToken(UserSessionEntity),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<SessionService>(SessionService);
    repository = module.get<Repository<UserSessionEntity>>(
      getRepositoryToken(UserSessionEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSession', () => {
    it('should create a new session successfully', async () => {
      const sessionData: SessionDto = {
        userId: 'user-123',
        userAgent: 'Mozilla/5.0',
        deviceType: 'desktop',
      };

      const mockSession = {
        id: '1',
        sessionId: 'session-123',
        userId: 'user-123',
        anonymousId: 'anon_123',
        userAgent: 'Mozilla/5.0',
        deviceType: 'desktop',
        startTime: new Date(),
        lastActivity: new Date(),
        pageViews: 0,
        properties: undefined,
      };

      mockRepository.create.mockReturnValue(mockSession);
      mockRepository.save.mockResolvedValue(mockSession);

      const result = await service.createSession(sessionData);

      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          userAgent: 'Mozilla/5.0',
          deviceType: 'desktop',
          pageViews: 0,
        }),
      );
      expect(mockRepository.save).toHaveBeenCalledWith(mockSession);
      expect(result).toEqual(mockSession);
    });

    it('should generate anonymous ID if not provided', async () => {
      const sessionData: SessionDto = {
        userAgent: 'Mozilla/5.0',
        deviceType: 'mobile',
      };

      const mockSession = {
        id: '1',
        sessionId: 'session-123',
        anonymousId: 'anon_generated',
        userAgent: 'Mozilla/5.0',
        deviceType: 'mobile',
        startTime: new Date(),
        lastActivity: new Date(),
        pageViews: 0,
      };

      mockRepository.create.mockReturnValue(mockSession);
      mockRepository.save.mockResolvedValue(mockSession);

      const result = await service.createSession(sessionData);

      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userAgent: 'Mozilla/5.0',
          deviceType: 'mobile',
        }),
      );
      expect(result.anonymousId).toMatch(/^anon_/);
    });
  });

  describe('getSession', () => {
    it('should return session when found', async () => {
      const sessionId = 'session-123';
      const mockSession = {
        id: '1',
        sessionId,
        userId: 'user-123',
        startTime: new Date(),
        lastActivity: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(mockSession);

      const result = await service.getSession(sessionId);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { sessionId },
      });
      expect(result).toEqual(mockSession);
    });

    it('should throw NotFoundException when session not found', async () => {
      const sessionId = 'non-existent';
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.getSession(sessionId)).rejects.toThrow(
        'Session with ID non-existent not found',
      );
    });
  });

  describe('endSession', () => {
    it('should end session successfully', async () => {
      const sessionId = 'session-123';
      const mockSession = {
        id: '1',
        sessionId,
        startTime: new Date(),
        lastActivity: new Date(),
        endTime: null,
      };

      const endedSession = {
        ...mockSession,
        endTime: new Date(),
        lastActivity: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(mockSession);
      mockRepository.save.mockResolvedValue(endedSession);

      const result = await service.endSession(sessionId);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { sessionId },
      });
      expect(mockRepository.save).toHaveBeenCalled();
      expect(result.endTime).toBeDefined();
    });

    it('should throw NotFoundException when session not found', async () => {
      const sessionId = 'non-existent';
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.endSession(sessionId)).rejects.toThrow(
        'Session with ID non-existent not found',
      );
    });
  });

  describe('updateSessionActivity', () => {
    it('should update session activity', async () => {
      const sessionId = 'session-123';
      const mockSession = {
        id: '1',
        sessionId,
        lastActivity: new Date('2023-01-01'),
      };

      mockRepository.findOne.mockResolvedValue(mockSession);
      mockRepository.save.mockResolvedValue({
        ...mockSession,
        lastActivity: new Date(),
      });

      await service.updateSessionActivity(sessionId);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { sessionId },
      });
      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should handle session not found gracefully', async () => {
      const sessionId = 'non-existent';
      mockRepository.findOne.mockResolvedValue(null);

      await expect(
        service.updateSessionActivity(sessionId),
      ).resolves.toBeUndefined();
    });
  });

  describe('handleSessionTimeout', () => {
    it('should end expired session', async () => {
      const sessionId = 'session-123';
      const oldDate = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
      const mockSession = {
        id: '1',
        sessionId,
        lastActivity: oldDate,
        endTime: null,
      };

      mockRepository.findOne.mockResolvedValue(mockSession);
      mockRepository.save.mockResolvedValue({
        ...mockSession,
        endTime: oldDate,
      });

      const result = await service.handleSessionTimeout(sessionId);

      expect(result).toBe(true);
      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should not end active session', async () => {
      const sessionId = 'session-123';
      const recentDate = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      const mockSession = {
        id: '1',
        sessionId,
        lastActivity: recentDate,
        endTime: null,
      };

      mockRepository.findOne.mockResolvedValue(mockSession);

      const result = await service.handleSessionTimeout(sessionId);

      expect(result).toBe(false);
      expect(mockRepository.save).not.toHaveBeenCalled();
    });
  });
});
