import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Repository } from 'typeorm';
import { BatchTrackingDto, TrackingEventDto } from '../dto';
import { TrackingEventEntity } from '../entities';
import { DeviceType } from '../enums';

/**
 * Simplified service for tracking user interactions
 */
@Injectable()
export class TrackingService {
  private readonly logger = new Logger(TrackingService.name);

  constructor(
    @InjectRepository(TrackingEventEntity)
    private readonly trackingEventRepository: Repository<TrackingEventEntity>,
  ) {}

  /**
   * Track a single event
   * @param eventData Event data
   * @returns The created tracking event
   */
  async trackEvent(eventData: TrackingEventDto): Promise<TrackingEventEntity> {
    try {
      // Validate and enrich the event
      const validatedEvent = this.validateAndEnrichEvent(eventData);

      // Create the tracking event entity
      const trackingEvent = this.trackingEventRepository.create({
        eventType: validatedEvent.eventType,
        userId: validatedEvent.userId,
        anonymousId: validatedEvent.anonymousId,
        sessionId: validatedEvent.sessionId,
        pageUrl: validatedEvent.pageUrl,
        referrer: validatedEvent.referrer,
        userAgent: validatedEvent.userAgent,
        deviceType: validatedEvent.deviceType,
        properties: validatedEvent.properties,
        timestamp: validatedEvent.timestamp || new Date(),
      });

      // Save and return the event
      return await this.trackingEventRepository.save(trackingEvent);
    } catch (error) {
      this.logger.error(`Error tracking event: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track multiple events in batch
   * @param batchData Batch of events data
   * @returns Array of created tracking events
   */
  async trackBatchEvents(
    batchData: BatchTrackingDto,
  ): Promise<TrackingEventEntity[]> {
    try {
      // Validate and enrich all events
      const validatedEvents = batchData.events.map((event) =>
        this.validateAndEnrichEvent(event),
      );

      // Create tracking event entities
      const trackingEvents = validatedEvents.map((event) =>
        this.trackingEventRepository.create({
          eventType: event.eventType,
          userId: event.userId,
          anonymousId: event.anonymousId,
          sessionId: event.sessionId,
          pageUrl: event.pageUrl,
          referrer: event.referrer,
          userAgent: event.userAgent,
          deviceType: event.deviceType,
          properties: event.properties,
          timestamp: event.timestamp || new Date(),
        }),
      );

      // Save all events
      return await this.trackingEventRepository.save(trackingEvents);
    } catch (error) {
      this.logger.error(
        `Error tracking batch events: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate and enrich event data
   * @param eventData Raw event data
   * @returns Validated and enriched event data
   */
  private validateAndEnrichEvent(
    eventData: TrackingEventDto,
  ): TrackingEventDto {
    // Create a copy to avoid mutating the original
    const enrichedEvent = { ...eventData };

    // Basic validation
    if (!enrichedEvent.eventType) {
      throw new Error('Event type is required');
    }

    // Validate event type format
    if (
      typeof enrichedEvent.eventType !== 'string' ||
      enrichedEvent.eventType.trim().length === 0
    ) {
      throw new Error('Event type must be a non-empty string');
    }

    // Generate anonymous ID if not provided and no user ID
    if (!enrichedEvent.anonymousId && !enrichedEvent.userId) {
      enrichedEvent.anonymousId = this.generateAnonymousId();
    }

    // Detect device type from user agent if not provided
    if (!enrichedEvent.deviceType && enrichedEvent.userAgent) {
      enrichedEvent.deviceType = this.detectDeviceType(enrichedEvent.userAgent);
    }

    // Parse browser information and add to properties if user agent is available
    if (enrichedEvent.userAgent) {
      const browserInfo = this.parseBrowserInfo(enrichedEvent.userAgent);
      if (Object.keys(browserInfo).length > 0) {
        enrichedEvent.properties = {
          ...enrichedEvent.properties,
          browserInfo,
        };
      }
    }

    // Set timestamp if not provided
    if (!enrichedEvent.timestamp) {
      enrichedEvent.timestamp = new Date();
    }

    // Validate URL format if provided
    if (enrichedEvent.pageUrl && !this.isValidUrl(enrichedEvent.pageUrl)) {
      this.logger.warn(`Invalid page URL provided: ${enrichedEvent.pageUrl}`);
      // Don't throw error, just log warning and continue
    }

    // Validate referrer URL format if provided
    if (enrichedEvent.referrer && !this.isValidUrl(enrichedEvent.referrer)) {
      this.logger.warn(
        `Invalid referrer URL provided: ${enrichedEvent.referrer}`,
      );
      // Don't throw error, just log warning and continue
    }

    // Sanitize properties to prevent large payloads
    if (enrichedEvent.properties) {
      enrichedEvent.properties = this.sanitizeProperties(
        enrichedEvent.properties,
      );
    }

    return enrichedEvent;
  }

  /**
   * Validate URL format
   * @param url URL to validate
   * @returns True if valid URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Sanitize properties object to prevent large payloads
   * @param properties Properties object
   * @returns Sanitized properties
   */
  private sanitizeProperties(
    properties: Record<string, any>,
  ): Record<string, any> {
    const sanitized: Record<string, any> = {};
    const maxKeys = 50;
    const maxStringLength = 1000;

    let keyCount = 0;
    for (const [key, value] of Object.entries(properties)) {
      if (keyCount >= maxKeys) {
        this.logger.warn(
          `Properties object has too many keys, truncating at ${maxKeys}`,
        );
        break;
      }

      // Skip sensitive keys
      const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth'];
      if (
        sensitiveKeys.some((sensitive) => key.toLowerCase().includes(sensitive))
      ) {
        continue;
      }

      // Truncate long strings
      if (typeof value === 'string' && value.length > maxStringLength) {
        sanitized[key] = value.substring(0, maxStringLength) + '...';
      } else if (typeof value === 'object' && value !== null) {
        // For nested objects, convert to string to avoid deep nesting issues
        sanitized[key] = JSON.stringify(value).substring(0, maxStringLength);
      } else {
        sanitized[key] = value;
      }

      keyCount++;
    }

    return sanitized;
  }

  /**
   * Generate anonymous ID for tracking
   * @returns Generated anonymous ID
   */
  private generateAnonymousId(): string {
    return `anon_${crypto.randomBytes(16).toString('hex')}`;
  }

  /**
   * Detect device type from user agent with comprehensive parsing
   * @param userAgent User agent string
   * @returns Detected device type
   */
  private detectDeviceType(userAgent: string): string {
    if (!userAgent) {
      return DeviceType.OTHER;
    }

    const ua = userAgent.toLowerCase();

    // Mobile device patterns
    const mobilePatterns = [
      'mobile',
      'android',
      'iphone',
      'ipod',
      'blackberry',
      'windows phone',
      'opera mini',
      'opera mobi',
      'palm',
      'webos',
      'symbian',
    ];

    // Tablet device patterns
    const tabletPatterns = [
      'ipad',
      'tablet',
      'kindle',
      'silk',
      'playbook',
      'nexus 7',
      'nexus 9',
      'nexus 10',
      'xoom',
      'sch-i800',
      'gt-p1000',
    ];

    // Check for tablet first (more specific)
    if (tabletPatterns.some((pattern) => ua.includes(pattern))) {
      return DeviceType.TABLET;
    }

    // Then check for mobile
    if (mobilePatterns.some((pattern) => ua.includes(pattern))) {
      return DeviceType.MOBILE;
    }

    // Check for specific mobile OS patterns
    if (ua.includes('android') && !ua.includes('mobile')) {
      // Android without mobile keyword is usually tablet
      return DeviceType.TABLET;
    }

    // Default to desktop for everything else
    return DeviceType.DESKTOP;
  }

  /**
   * Parse basic browser information from user agent
   * @param userAgent User agent string
   * @returns Browser information object
   */
  private parseBrowserInfo(userAgent: string): {
    browser?: string;
    version?: string;
    os?: string;
  } {
    if (!userAgent) {
      return {};
    }

    const ua = userAgent.toLowerCase();
    const browserInfo: { browser?: string; version?: string; os?: string } = {};

    // Detect browser
    if (ua.includes('chrome') && !ua.includes('edge')) {
      browserInfo.browser = 'Chrome';
      const match = ua.match(/chrome\/([0-9.]+)/);
      if (match) browserInfo.version = match[1];
    } else if (ua.includes('firefox')) {
      browserInfo.browser = 'Firefox';
      const match = ua.match(/firefox\/([0-9.]+)/);
      if (match) browserInfo.version = match[1];
    } else if (ua.includes('safari') && !ua.includes('chrome')) {
      browserInfo.browser = 'Safari';
      const match = ua.match(/version\/([0-9.]+)/);
      if (match) browserInfo.version = match[1];
    } else if (ua.includes('edge')) {
      browserInfo.browser = 'Edge';
      const match = ua.match(/edge\/([0-9.]+)/);
      if (match) browserInfo.version = match[1];
    } else if (ua.includes('opera')) {
      browserInfo.browser = 'Opera';
      const match = ua.match(/opera\/([0-9.]+)/);
      if (match) browserInfo.version = match[1];
    }

    // Detect OS
    if (ua.includes('windows')) {
      browserInfo.os = 'Windows';
    } else if (ua.includes('mac os')) {
      browserInfo.os = 'macOS';
    } else if (ua.includes('linux')) {
      browserInfo.os = 'Linux';
    } else if (ua.includes('android')) {
      browserInfo.os = 'Android';
    } else if (
      ua.includes('ios') ||
      ua.includes('iphone') ||
      ua.includes('ipad')
    ) {
      browserInfo.os = 'iOS';
    }

    return browserInfo;
  }
}
