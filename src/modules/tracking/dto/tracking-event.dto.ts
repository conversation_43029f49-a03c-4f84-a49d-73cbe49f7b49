import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';

/**
 * Simplified DTO for tracking event data
 */
export class TrackingEventDto {
  @ApiProperty({
    description: 'Type of event (PAGE_VIEW, CLICK, CUSTOM)',
    example: 'PAGE_VIEW',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  eventType: string;

  @ApiPropertyOptional({
    description: 'User ID if authenticated',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Anonymous user identifier',
    example: 'anon_123456789',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  anonymousId?: string;

  @ApiPropertyOptional({
    description: 'Session ID',
    example: 'sess_123456789',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'URL where the event occurred',
    example: 'https://example.com/products/123',
  })
  @IsString()
  @IsOptional()
  pageUrl?: string;

  @ApiPropertyOptional({
    description: 'Path portion of the URL',
    example: '/products/123',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  pagePath?: string;

  @ApiPropertyOptional({
    description: 'Referrer URL',
    example: 'https://www.google.com',
  })
  @IsString()
  @IsOptional()
  referrer?: string;

  @ApiPropertyOptional({
    description: 'User agent information',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  @IsString()
  @IsOptional()
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'Device type (mobile, desktop, tablet)',
    example: 'desktop',
  })
  @IsString()
  @IsOptional()
  @MaxLength(20)
  deviceType?: string;

  @ApiPropertyOptional({
    description: 'IP address of the user',
    example: '***********',
  })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'Additional event-specific properties',
    example: { buttonId: 'submit-btn', section: 'header' },
  })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Timestamp when the event occurred',
    example: '2023-01-01T12:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  timestamp?: Date;
}

/**
 * DTO for batch tracking events
 */
export class BatchTrackingDto {
  @ApiProperty({
    description: 'Array of tracking events',
    type: [TrackingEventDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TrackingEventDto)
  events: TrackingEventDto[];
}

/**
 * DTO for page view events (extends TrackingEventDto)
 */
export class PageViewDto extends TrackingEventDto {
  @ApiProperty({
    description: 'Type of event - always PAGE_VIEW',
    example: 'PAGE_VIEW',
  })
  declare eventType: 'PAGE_VIEW';

  @ApiProperty({
    description: 'URL of the page being viewed',
    example: 'https://example.com/products/123',
  })
  @IsString()
  @IsNotEmpty()
  declare pageUrl: string;
}
