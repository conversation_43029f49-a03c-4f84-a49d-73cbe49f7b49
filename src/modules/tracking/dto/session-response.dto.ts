import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Simplified DTO for session response
 */
export class SessionResponseDto {
  @ApiProperty({
    description: 'Session ID',
    example: 'sess_123456789',
  })
  sessionId: string;

  @ApiPropertyOptional({
    description: 'User ID if authenticated',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  userId?: string;

  @ApiPropertyOptional({
    description: 'Anonymous ID for the user',
    example: 'anon_123456789',
  })
  anonymousId?: string;

  @ApiProperty({
    description: 'Session start time',
    example: '2023-01-01T12:00:00Z',
  })
  startTime: Date;

  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;
}

/**
 * DTO for session update response
 */
export class SessionUpdateResponseDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  sessionId: string;

  @ApiProperty({
    description: 'Whether the session was updated successfully',
    example: true,
  })
  updated: boolean;

  @ApiPropertyOptional({
    description: 'Last activity timestamp',
    example: '2023-01-01T12:30:00Z',
  })
  lastActivity?: Date;
}
