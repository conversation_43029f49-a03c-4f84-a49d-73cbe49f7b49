import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
} from 'class-validator';

/**
 * Simplified DTO for session management
 */
export class SessionDto {
  @ApiPropertyOptional({
    description: 'User ID if authenticated',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Anonymous user identifier',
    example: 'anon_123456789',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  anonymousId?: string;

  @ApiPropertyOptional({
    description: 'User agent information',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  @IsString()
  @IsOptional()
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'Device type (mobile, desktop, tablet)',
    example: 'desktop',
  })
  @IsString()
  @IsOptional()
  @MaxLength(20)
  deviceType?: string;

  @ApiPropertyOptional({
    description: 'IP address of the user',
    example: '***********',
  })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'Additional session properties',
    example: { language: 'en-US', timezone: 'America/New_York' },
  })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;
}
