import { registerAs } from '@nestjs/config';
import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, <PERSON>, <PERSON> } from 'class-validator';

/**
 * Configuration interface for tracking module settings
 */
export interface TrackingConfig {
  // Session management settings
  sessionTimeoutMinutes: number;
  sessionCleanupIntervalMinutes: number;
  sessionInactivityTimeoutMinutes: number;

  // Data retention settings
  eventRetentionDays: number;
  sessionRetentionDays: number;
  cleanupBatchSize: number;
}

/**
 * Configuration validation class
 */
export class TrackingConfigValidation {
  @IsNumber()
  @Min(1)
  @Max(1440) // 24 hours
  @Transform(({ value }) => parseInt(value, 10))
  @IsOptional()
  TRACKING_SESSION_TIMEOUT_MINUTES?: number;

  @IsNumber()
  @Min(1)
  @Max(1440) // 24 hours
  @Transform(({ value }) => parseInt(value, 10))
  @IsOptional()
  TRACKING_SESSION_CLEANUP_INTERVAL_MINUTES?: number;

  @IsNumber()
  @Min(1)
  @Max(60) // 1 hour
  @Transform(({ value }) => parseInt(value, 10))
  @IsOptional()
  TRACKING_SESSION_INACTIVITY_TIMEOUT_MINUTES?: number;

  @IsNumber()
  @Min(1)
  @Max(3650) // 10 years
  @Transform(({ value }) => parseInt(value, 10))
  @IsOptional()
  TRACKING_EVENT_RETENTION_DAYS?: number;

  @IsNumber()
  @Min(1)
  @Max(365) // 1 year
  @Transform(({ value }) => parseInt(value, 10))
  @IsOptional()
  TRACKING_SESSION_RETENTION_DAYS?: number;

  @IsNumber()
  @Min(100)
  @Max(10000)
  @Transform(({ value }) => parseInt(value, 10))
  @IsOptional()
  TRACKING_CLEANUP_BATCH_SIZE?: number;
}

/**
 * Default configuration values
 */
export const defaultTrackingConfig: TrackingConfig = {
  // Session management settings (Requirements: 4.5)
  sessionTimeoutMinutes: 30,
  sessionCleanupIntervalMinutes: 60,
  sessionInactivityTimeoutMinutes: 15,

  // Data retention settings (Requirements: 4.5)
  eventRetentionDays: 90,
  sessionRetentionDays: 30,
  cleanupBatchSize: 1000,
};

/**
 * Configuration factory function
 */
export const trackingConfig = registerAs(
  'tracking',
  (): TrackingConfig => ({
    // Session management settings
    sessionTimeoutMinutes: parseInt(
      process.env.TRACKING_SESSION_TIMEOUT_MINUTES ||
        defaultTrackingConfig.sessionTimeoutMinutes.toString(),
      10,
    ),
    sessionCleanupIntervalMinutes: parseInt(
      process.env.TRACKING_SESSION_CLEANUP_INTERVAL_MINUTES ||
        defaultTrackingConfig.sessionCleanupIntervalMinutes.toString(),
      10,
    ),
    sessionInactivityTimeoutMinutes: parseInt(
      process.env.TRACKING_SESSION_INACTIVITY_TIMEOUT_MINUTES ||
        defaultTrackingConfig.sessionInactivityTimeoutMinutes.toString(),
      10,
    ),

    // Data retention settings
    eventRetentionDays: parseInt(
      process.env.TRACKING_EVENT_RETENTION_DAYS ||
        defaultTrackingConfig.eventRetentionDays.toString(),
      10,
    ),
    sessionRetentionDays: parseInt(
      process.env.TRACKING_SESSION_RETENTION_DAYS ||
        defaultTrackingConfig.sessionRetentionDays.toString(),
      10,
    ),
    cleanupBatchSize: parseInt(
      process.env.TRACKING_CLEANUP_BATCH_SIZE ||
        defaultTrackingConfig.cleanupBatchSize.toString(),
      10,
    ),
  }),
);

/**
 * Helper function to get tracking configuration
 */
export const getTrackingConfig = (): TrackingConfig => {
  return {
    sessionTimeoutMinutes: parseInt(
      process.env.TRACKING_SESSION_TIMEOUT_MINUTES ||
        defaultTrackingConfig.sessionTimeoutMinutes.toString(),
      10,
    ),
    sessionCleanupIntervalMinutes: parseInt(
      process.env.TRACKING_SESSION_CLEANUP_INTERVAL_MINUTES ||
        defaultTrackingConfig.sessionCleanupIntervalMinutes.toString(),
      10,
    ),
    sessionInactivityTimeoutMinutes: parseInt(
      process.env.TRACKING_SESSION_INACTIVITY_TIMEOUT_MINUTES ||
        defaultTrackingConfig.sessionInactivityTimeoutMinutes.toString(),
      10,
    ),
    eventRetentionDays: parseInt(
      process.env.TRACKING_EVENT_RETENTION_DAYS ||
        defaultTrackingConfig.eventRetentionDays.toString(),
      10,
    ),
    sessionRetentionDays: parseInt(
      process.env.TRACKING_SESSION_RETENTION_DAYS ||
        defaultTrackingConfig.sessionRetentionDays.toString(),
      10,
    ),
    cleanupBatchSize: parseInt(
      process.env.TRACKING_CLEANUP_BATCH_SIZE ||
        defaultTrackingConfig.cleanupBatchSize.toString(),
      10,
    ),
  };
};
