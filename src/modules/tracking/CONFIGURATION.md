# Tracking Module Configuration

This document describes how to configure the Tracking Module for different environments and use cases.

## Configuration Files

- `tracking.config.ts` - Main configuration file with TypeScript interfaces and validation
- `.env.example` - Example environment variables with documentation
- `CONFIGURATION.md` - This documentation file

## Environment Variables

All configuration is done through environment variables. Copy the values from `.env.example` to your main `.env` file and adjust as needed.

### Rate Limiting Settings (Requirement: 5.3)

These settings control how many requests the tracking API will accept:

```bash
# Maximum tracking requests per minute per IP
TRACKING_RATE_LIMIT_PER_MINUTE=100

# Maximum events in a single batch request
TRACKING_BATCH_SIZE_LIMIT=50

# Maximum request size in bytes
TRACKING_REQUEST_SIZE_LIMIT=1048576
```

**Recommendations:**
- **Development**: Set higher limits (1000 requests/minute)
- **Production**: Use conservative limits (50-100 requests/minute)
- **High Traffic**: Increase limits but monitor server resources

### Session Management Settings (Requirement: 4.5)

These settings control how user sessions are managed:

```bash
# How long sessions stay active without activity (minutes)
TRACKING_SESSION_TIMEOUT_MINUTES=30

# How often to clean up expired sessions (minutes)
TRACKING_SESSION_CLEANUP_INTERVAL_MINUTES=60

# Mark sessions inactive after this time (minutes)
TRACKING_SESSION_INACTIVITY_TIMEOUT_MINUTES=15
```

**Recommendations:**
- **Mobile Apps**: Shorter timeouts (15-30 minutes)
- **Web Apps**: Standard timeouts (30-60 minutes)
- **Analytics**: Longer timeouts for better user journey tracking

### Data Retention Settings (Requirement: 4.5)

These settings control how long data is kept:

```bash
# Days to keep tracking events
TRACKING_EVENT_RETENTION_DAYS=90

# Days to keep session data
TRACKING_SESSION_RETENTION_DAYS=30

# Records to process in each cleanup batch
TRACKING_CLEANUP_BATCH_SIZE=1000
```

**Recommendations:**
- **GDPR Compliance**: Set retention based on your privacy policy
- **Analytics Needs**: Keep events longer for trend analysis
- **Storage Costs**: Balance retention with storage requirements

### Security Settings

These settings control data privacy and security:

```bash
# Hash IP addresses for privacy
TRACKING_ENABLE_IP_HASHING=false

# Limit custom properties to prevent abuse
TRACKING_MAX_PROPERTY_KEYS=50
TRACKING_MAX_PROPERTY_VALUE_LENGTH=1000
TRACKING_MAX_EVENT_TYPE_LENGTH=50
```

**Recommendations:**
- **GDPR/Privacy**: Enable IP hashing in production
- **Security**: Keep property limits to prevent large payloads
- **Performance**: Lower limits for high-traffic applications

### Performance Settings

These settings control performance and resource usage:

```bash
# Enable batch processing for better performance
TRACKING_ENABLE_BATCH_PROCESSING=true

# Database query timeout (milliseconds)
TRACKING_DATABASE_QUERY_TIMEOUT=5000

# Maximum concurrent requests
TRACKING_MAX_CONCURRENT_REQUESTS=100
```

## Configuration Usage in Code

### Injecting Configuration

```typescript
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TrackingConfig } from './tracking.config';

@Injectable()
export class YourService {
  constructor(private configService: ConfigService) {}

  someMethod() {
    const trackingConfig = this.configService.get<TrackingConfig>('tracking');
    const sessionTimeout = trackingConfig.sessionTimeoutMinutes;
    // Use configuration values...
  }
}
```

### Using Helper Function

```typescript
import { getTrackingConfig } from './tracking.config';

const config = getTrackingConfig();
console.log(`Session timeout: ${config.sessionTimeoutMinutes} minutes`);
```

## Environment-Specific Configurations

### Development Environment

```bash
# Relaxed settings for development
TRACKING_RATE_LIMIT_PER_MINUTE=1000
TRACKING_BATCH_SIZE_LIMIT=100
TRACKING_EVENT_RETENTION_DAYS=7
TRACKING_SESSION_RETENTION_DAYS=7
TRACKING_ENABLE_IP_HASHING=false
TRACKING_DATABASE_QUERY_TIMEOUT=10000
```

### Production Environment

```bash
# Secure settings for production
TRACKING_RATE_LIMIT_PER_MINUTE=50
TRACKING_BATCH_SIZE_LIMIT=25
TRACKING_EVENT_RETENTION_DAYS=365
TRACKING_SESSION_RETENTION_DAYS=90
TRACKING_ENABLE_IP_HASHING=true
TRACKING_MAX_CONCURRENT_REQUESTS=50
```

### High Traffic Environment

```bash
# Performance-optimized settings
TRACKING_RATE_LIMIT_PER_MINUTE=500
TRACKING_BATCH_SIZE_LIMIT=100
TRACKING_MAX_CONCURRENT_REQUESTS=500
TRACKING_DATABASE_QUERY_TIMEOUT=10000
TRACKING_CLEANUP_BATCH_SIZE=5000
```

## Configuration Validation

The configuration includes built-in validation:

- **Rate limits**: 1-1000 requests per minute
- **Batch size**: 1-100 events per batch
- **Timeouts**: 1-1440 minutes (24 hours max)
- **Retention**: 1-3650 days (10 years max)
- **Request size**: 1KB-10MB

Invalid values will cause the application to fail startup with clear error messages.

## Monitoring Configuration

### Key Metrics to Monitor

1. **Rate Limiting**: Track how often limits are hit
2. **Session Cleanup**: Monitor cleanup job performance
3. **Data Retention**: Track storage growth and cleanup effectiveness
4. **Performance**: Monitor query timeouts and concurrent request limits

### Logging Configuration Impact

```typescript
// Example: Log configuration on startup
const config = getTrackingConfig();
console.log('Tracking Configuration:', {
  sessionTimeout: config.sessionTimeoutMinutes,
  eventRetention: config.eventRetentionDays,
  sessionRetention: config.sessionRetentionDays,
  cleanupBatchSize: config.cleanupBatchSize
});
```

## Troubleshooting

### Common Issues

1. **Rate Limiting Too Strict**: Increase `TRACKING_RATE_LIMIT_PER_MINUTE`
2. **Sessions Expiring Too Fast**: Increase `TRACKING_SESSION_TIMEOUT_MINUTES`
3. **Database Timeouts**: Increase `TRACKING_DATABASE_QUERY_TIMEOUT`
4. **Storage Growing Too Fast**: Decrease retention days or increase cleanup frequency

### Configuration Testing

```bash
# Test configuration loading
npm run start:dev

# Check logs for configuration values
# Look for "Tracking Configuration:" in startup logs
```

## Security Considerations

1. **IP Hashing**: Enable in production for privacy compliance
2. **Rate Limiting**: Prevent abuse and DoS attacks
3. **Property Limits**: Prevent large payload attacks
4. **Data Retention**: Comply with privacy regulations
5. **Request Size Limits**: Prevent memory exhaustion attacks

## Performance Tuning

1. **Batch Processing**: Enable for better throughput
2. **Concurrent Requests**: Tune based on server capacity
3. **Database Timeouts**: Balance responsiveness vs. reliability
4. **Cleanup Batch Size**: Larger batches = better performance, more memory usage

## Migration Guide

When updating configuration:

1. **Backup Current Settings**: Save current environment variables
2. **Test New Settings**: Use development environment first
3. **Monitor Impact**: Watch performance metrics after changes
4. **Rollback Plan**: Keep previous settings ready for quick rollback