# Tracking Module

## Overview

The Tracking Module provides a simplified system for collecting basic user interaction data across mobile and web applications. It focuses on core tracking functionality without complex analytics or queue processing, making it lightweight and easy to maintain.

## Key Features

- **Event Tracking**: Capture user interactions such as page views, clicks, and custom events
- **Session Management**: Track basic user sessions with start/end functionality
- **Anonymous Tracking**: Support for tracking both authenticated and anonymous users
- **Device Detection**: Automatic device type detection (mobile, desktop, tablet)
- **Batch Processing**: Handle multiple events in a single request
- **Privacy Compliant**: Built with privacy considerations and data validation

## Module Structure

### Entities

- **TrackingEventEntity**: Stores individual user interaction events
- **UserSessionEntity**: Manages basic user browsing sessions

### Enums

- **DeviceType**: Types of devices (DESKTOP, MOBILE, TABLET, OTHER)
- **EventType**: Types of tracking events (PAGE_VIEW, CLICK, CUSTOM)


### Services

- **TrackingService**: Core service for processing and storing events
- **SessionService**: Manages user session lifecycle

### Controllers

- **TrackingController**: REST API endpoints for receiving tracking events and managing sessions

## API Endpoints

### Track Single Event
```http
POST /api/tracking/events
Content-Type: application/json

{
  "eventType": "PAGE_VIEW",
  "pageUrl": "https://example.com/products/123",
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "sessionId": "sess_123456789",
  "properties": {
    "section": "products",
    "category": "electronics"
  }
}
```

### Track Multiple Events (Batch)
```http
POST /api/tracking/batch
Content-Type: application/json

{
  "events": [
    {
      "eventType": "PAGE_VIEW",
      "pageUrl": "https://example.com/home"
    },
    {
      "eventType": "CLICK",
      "pageUrl": "https://example.com/home",
      "properties": {
        "elementId": "header-logo",
        "elementType": "link"
      }
    }
  ]
}
```

### Track Page View
```http
POST /api/tracking/pageview
Content-Type: application/json

{
  "pageUrl": "https://example.com/products/123",
  "referrer": "https://www.google.com",
  "userId": "123e4567-e89b-12d3-a456-426614174000"
}
```

### Create Session
```http
POST /api/tracking/session
Content-Type: application/json

{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "anonymousId": "anon_123456789",
  "properties": {
    "language": "en-US",
    "timezone": "America/New_York"
  }
}
```

### End Session
```http
POST /api/tracking/session/sess_123456789/end
```

## Usage Examples

### Frontend JavaScript Integration

```javascript
// Initialize tracking
class SimpleTracker {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.sessionId = this.getOrCreateSessionId();
    this.anonymousId = this.getOrCreateAnonymousId();
  }

  // Track page view
  async trackPageView(pageUrl, properties = {}) {
    return await this.sendEvent({
      eventType: 'PAGE_VIEW',
      pageUrl: pageUrl || window.location.href,
      referrer: document.referrer,
      properties
    });
  }

  // Track custom event
  async trackEvent(eventType, properties = {}) {
    return await this.sendEvent({
      eventType,
      pageUrl: window.location.href,
      properties
    });
  }

  // Track click event
  async trackClick(element, properties = {}) {
    return await this.sendEvent({
      eventType: 'CLICK',
      pageUrl: window.location.href,
      properties: {
        elementId: element.id,
        elementClass: element.className,
        elementText: element.textContent?.substring(0, 100),
        ...properties
      }
    });
  }

  // Send event to API
  async sendEvent(eventData) {
    try {
      const response = await fetch(`${this.baseUrl}/api/tracking/events`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...eventData,
          sessionId: this.sessionId,
          anonymousId: this.anonymousId,
          timestamp: new Date().toISOString()
        })
      });
      
      return await response.json();
    } catch (error) {
      console.error('Tracking error:', error);
    }
  }

  // Batch track multiple events
  async trackBatch(events) {
    try {
      const enrichedEvents = events.map(event => ({
        ...event,
        sessionId: this.sessionId,
        anonymousId: this.anonymousId,
        timestamp: new Date().toISOString()
      }));

      const response = await fetch(`${this.baseUrl}/api/tracking/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ events: enrichedEvents })
      });
      
      return await response.json();
    } catch (error) {
      console.error('Batch tracking error:', error);
    }
  }

  // Helper methods
  getOrCreateSessionId() {
    let sessionId = sessionStorage.getItem('tracking_session_id');
    if (!sessionId) {
      sessionId = 'sess_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('tracking_session_id', sessionId);
    }
    return sessionId;
  }

  getOrCreateAnonymousId() {
    let anonymousId = localStorage.getItem('tracking_anonymous_id');
    if (!anonymousId) {
      anonymousId = 'anon_' + Math.random().toString(36).substr(2, 16);
      localStorage.setItem('tracking_anonymous_id', anonymousId);
    }
    return anonymousId;
  }
}

// Usage
const tracker = new SimpleTracker('https://your-api-domain.com');

// Track page view on load
tracker.trackPageView();

// Track button clicks
document.addEventListener('click', (event) => {
  if (event.target.tagName === 'BUTTON') {
    tracker.trackClick(event.target);
  }
});

// Track custom events
tracker.trackEvent('FORM_SUBMIT', {
  formId: 'contact-form',
  formType: 'contact'
});
```

### Backend NestJS Integration

```typescript
// In your service or controller
import { TrackingService } from './modules/tracking/services/tracking.service';

@Injectable()
export class YourService {
  constructor(private readonly trackingService: TrackingService) {}

  async handleUserAction(userId: string, action: string) {
    // Track the user action
    await this.trackingService.trackEvent({
      eventType: 'CUSTOM',
      userId,
      properties: {
        action,
        timestamp: new Date(),
        source: 'backend'
      }
    });
  }
}

// In a controller
@Controller('products')
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    private readonly trackingService: TrackingService
  ) {}

  @Get(':id')
  async getProduct(@Param('id') id: string, @Req() req: Request) {
    const product = await this.productService.findById(id);
    
    // Track product view
    await this.trackingService.trackEvent({
      eventType: 'PAGE_VIEW',
      userId: req.user?.id,
      pageUrl: `/products/${id}`,
      properties: {
        productId: id,
        productName: product.name,
        productCategory: product.category
      }
    });

    return product;
  }
}
```

### React Hook Integration

```typescript
// useTracking.ts
import { useEffect, useCallback } from 'react';

interface TrackingEvent {
  eventType: string;
  properties?: Record<string, any>;
}

export const useTracking = (baseUrl: string) => {
  const sessionId = sessionStorage.getItem('tracking_session_id') || 
    (() => {
      const id = 'sess_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('tracking_session_id', id);
      return id;
    })();

  const trackEvent = useCallback(async (event: TrackingEvent) => {
    try {
      await fetch(`${baseUrl}/api/tracking/events`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...event,
          sessionId,
          pageUrl: window.location.href,
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Tracking error:', error);
    }
  }, [baseUrl, sessionId]);

  const trackPageView = useCallback(() => {
    trackEvent({
      eventType: 'PAGE_VIEW',
      properties: {
        path: window.location.pathname,
        search: window.location.search
      }
    });
  }, [trackEvent]);

  return { trackEvent, trackPageView };
};

// Usage in component
function MyComponent() {
  const { trackEvent, trackPageView } = useTracking('https://api.example.com');

  useEffect(() => {
    trackPageView();
  }, [trackPageView]);

  const handleButtonClick = () => {
    trackEvent({
      eventType: 'CLICK',
      properties: {
        buttonId: 'cta-button',
        section: 'hero'
      }
    });
  };

  return <button onClick={handleButtonClick}>Click me</button>;
}
```

## Data Models

### TrackingEventDto
```typescript
{
  eventType: string;           // Required: PAGE_VIEW, CLICK, CUSTOM
  userId?: string;             // Optional: UUID of authenticated user
  anonymousId?: string;        // Optional: Anonymous user identifier
  sessionId?: string;          // Optional: Session identifier
  pageUrl?: string;            // Optional: URL where event occurred
  referrer?: string;           // Optional: Referrer URL
  userAgent?: string;          // Optional: User agent string
  deviceType?: string;         // Optional: mobile, desktop, tablet
  properties?: object;         // Optional: Custom event properties
  timestamp?: Date;            // Optional: Event timestamp
}
```

### SessionDto
```typescript
{
  userId?: string;             // Optional: UUID of authenticated user
  anonymousId?: string;        // Optional: Anonymous user identifier
  userAgent?: string;          // Optional: User agent string
  deviceType?: string;         // Optional: mobile, desktop, tablet
  properties?: object;         // Optional: Custom session properties
}
```

## Features

### Automatic Data Enrichment
- Device type detection from user agent
- Browser information parsing
- Anonymous ID generation for unauthenticated users
- Request metadata extraction (IP, user agent, referrer)

### Data Validation
- Event type validation and sanitization
- URL format validation
- Property object sanitization (prevents large payloads)
- Input length limits and security checks

### Rate Limiting
- Per-IP rate limiting to prevent abuse
- Batch size limits (max 50 events per batch)
- Request size validation

### Privacy Considerations
- IP addresses can be stored or hashed based on configuration
- Sensitive property keys are filtered out
- Data retention policies can be configured
- Anonymous tracking support for GDPR compliance

## Configuration

The module can be configured through environment variables:

```typescript
// Rate limiting
TRACKING_RATE_LIMIT=100          // requests per minute per IP
TRACKING_BATCH_LIMIT=50          // max events per batch request

// Session management  
SESSION_TIMEOUT=30               // session timeout in minutes
SESSION_CLEANUP_INTERVAL=60      // cleanup interval in minutes

// Data retention
EVENT_RETENTION_DAYS=90          // how long to keep events
SESSION_RETENTION_DAYS=30        // how long to keep sessions
```

## Error Handling

The API returns structured error responses:

```typescript
// Success response
{
  "success": true,
  "eventId": "123e4567-e89b-12d3-a456-426614174000"
}

// Error response
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid event data",
    "details": ["eventType is required"]
  }
}
```

## Performance Considerations

- Database indexes on frequently queried fields
- Batch processing for multiple events
- Efficient data validation and sanitization
- Minimal dependencies for fast startup
- Direct database writes (no queue overhead for simple use cases)
