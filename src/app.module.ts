import { redisConfig } from '@app/config/redis.config';
import { alertConfig, AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertModule } from '@app/modules/alert/alert.module';
import { appSettingsConfig } from '@app/modules/app-settings/app-settings.config';
import { AppSettingsModule } from '@app/modules/app-settings/app-settings.module';
import { AuthModule } from '@app/modules/auth/auth.module';
import { CacheStoreModule } from '@app/modules/cache-store/cache-store.module';
import { DatabaseModule } from '@app/modules/database/database.module';
import { MailerModule } from '@app/modules/mailer/mailer.module';
import { ManualActionModule } from '@app/modules/manual-action/manual-action.module';
import { PasskeyModule } from '@app/modules/passkey/passkey.module';
import { AuthorizationModule } from '@app/modules/authorization';
import {
  socialAuthConfig,
  SocialAuthConfigType,
  SocialAuthModule,
  SocialProviderEnum,
} from '@app/modules/social-auth';
import { TwoFactorModule } from '@app/modules/two-factor/two-factor.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [redisConfig, appSettingsConfig],
    }),
    ScheduleModule.forRoot(),
    ServeStaticModule.forRoot({
      rootPath: join(process.cwd(), 'public'),
      exclude: ['/api/*splat'],
    }),
    CacheStoreModule,
    DatabaseModule,
    AppSettingsModule,
    AuthModule,
    TwoFactorModule,
    MailerModule,
    AlertModule.forRootAsync({
      imports: [ConfigModule.forFeature(alertConfig)],
      useFactory: (
        configService: ConfigService<{
          alert: AlertConfigType;
        }>,
      ) => {
        const config = configService.getOrThrow('alert', { infer: true });
        return config;
      },
      inject: [ConfigService],
    }),
    PasskeyModule,
    TwoFactorModule,
    SocialAuthModule.forRootAsync({
      imports: [ConfigModule.forFeature(socialAuthConfig)],
      useFactory: (
        configService: ConfigService<{
          socialAuth: SocialAuthConfigType;
        }>,
      ) => {
        const config = configService.getOrThrow('socialAuth', { infer: true });
        return {
          providers: {
            google: config.google
              ? {
                  clientId: config.google.clientId,
                  clientSecret: config.google.clientSecret,
                  callbackURL: config.google.callbackURL,
                }
              : undefined,
            facebook: config.facebook
              ? {
                  clientId: config.facebook.clientId,
                  clientSecret: config.facebook.clientSecret,
                  redirectUrl: config.facebook.redirectUrl,
                }
              : undefined,
            microsoft: config.microsoft
              ? {
                  clientId: config.microsoft.clientId,
                  clientSecret: config.microsoft.clientSecret,
                  callbackURL: config.microsoft.callbackURL,
                  tenant: config.microsoft.tenant,
                }
              : undefined,
          },
          enabledProviders: [
            SocialProviderEnum.GOOGLE,
            SocialProviderEnum.FACEBOOK,
            SocialProviderEnum.MICROSOFT,
          ],
        };
      },
      inject: [ConfigService],
    }),
    ManualActionModule,
    AuthorizationModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
