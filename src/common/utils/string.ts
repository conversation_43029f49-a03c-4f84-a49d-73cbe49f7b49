import { randomBytes } from 'crypto';

/**
 * Generate an OTP string with a specified length
 * @param length - The length of the OTP string (default is 6)
 * @returns The generated OTP string
 */
export const generateOtpString = (length: number = 6) => {
  return randomBytes(length).toString('hex');
};

/**
 * Generates a random numeric OTP (One-Time Password) of a given length.
 * This method ensures that the OTP can start with '0'.
 *
 * @param {number} length - The desired length of the OTP. Defaults to 6.
 * @returns {string} The generated OTP string.
 */
export const generateOtpNumber = (length: number = 6): string => {
  let otp = '';

  for (let i = 0; i < length; i++) {
    // Generate a random digit from 0 to 9
    const digit = Math.floor(Math.random() * 10);
    // Append the digit to the OTP string
    otp += digit.toString();
  }

  return otp;
};

/**
 * Generates a random string of a given length.
 *
 * @param {number} length - The desired length of the string. Defaults to 32.
 * @param {object} options - The options for the string generation.
 * @param {boolean} options.includeUppercase - Whether to include uppercase letters. Defaults to true.
 * @param {boolean} options.includeLowercase - Whether to include lowercase letters. Defaults to true.
 * @param {boolean} options.includeNumbers - Whether to include numbers. Defaults to true.
 * @param {boolean} options.includeSymbols - Whether to include symbols. Defaults to false.
 * @returns {string} The generated string.
 */
export const randomStringGenerator = (
  length: number = 32,
  options: {
    includeUppercase?: boolean;
    includeLowercase?: boolean;
    includeNumbers?: boolean;
    includeSymbols?: boolean;
  } = {
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSymbols: false,
  },
): string => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  let characterPool = '';

  if (options.includeUppercase) characterPool += uppercase;
  if (options.includeLowercase) characterPool += lowercase;
  if (options.includeNumbers) characterPool += numbers;
  if (options.includeSymbols) characterPool += symbols;

  // Default to alphanumeric if no options are selected
  if (characterPool === '') {
    characterPool = uppercase + lowercase + numbers;
  }

  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characterPool.length);
    result += characterPool[randomIndex];
  }

  return result;
};
