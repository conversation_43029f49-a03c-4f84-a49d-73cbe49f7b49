export const parseJsonFromMarkdown = <T = any>(jsonString: string) => {
  try {
    return JSON.parse(
      jsonString.replace('```json', '').replace('```', '').replaceAll('\n', ''),
    ) as T;
  } catch (error) {
    console.error('Error parsing JSO<PERSON> from Markdown: ', jsonString);
    console.error(error);
    throw new Error('Error parsing JSON from Markdown');
  }
};

export const parseJsonFromTextAIResponse = <T = any>(text: string): T => {
  try {
    let jsonString = text;
    if (text.includes('```json')) {
      jsonString = text.split('```json')[1].split('```')[0];
      return JSON.parse(jsonString) as T;
    }
    return extractJsonFromText(jsonString) as T;
  } catch (error) {
    console.error('Error parsing <PERSON><PERSON><PERSON> from TextAI Response: ', text);
    console.error(error);
    throw new Error('Error parsing <PERSON><PERSON><PERSON> from Text');
  }
};

const extractJsonFromText = (text: string) => {
  // Find the start and end indices of the JSON object
  const startIndex = text.indexOf('{');
  const endIndex = text.lastIndexOf('}') + 1;

  // Check if both '{' and '}' are found
  if (startIndex === -1 || endIndex === -1) {
    throw new Error('JSON not found in the text');
  }

  // Extract the JSON string
  const jsonString = text.substring(startIndex, endIndex);

  const jsonObject = JSON.parse(jsonString);
  return jsonObject;
};

export function getCircularReplacer() {
  const seen = new WeakSet();
  return (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return; // Circular reference found, discard key
      }
      seen.add(value);
    }
    return value;
  };
}
