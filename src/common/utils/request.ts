import { Request } from 'express';

export const getUserAgent = (req: Request) => {
  return (req.headers['user-agent'] as string) || 'unknown';
};

export const getIpAddress = (req: Request) => {
  const regex = /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/;
  const ipFound = req.headers['forwarded']?.match(regex);
  if (ipFound && ipFound[0]) {
    return ipFound[0];
  }
  const ipAddress = req.headers?.['x-forwarded-for'];
  if (ipAddress && typeof ipAddress === 'string') {
    return ipAddress.split(',')[0];
  }
  return req.ip || 'unknown';
};

export const getTimeZone = (req: Request) => {
  return (req.headers['x-timezone'] as string) || 'unknown';
};
