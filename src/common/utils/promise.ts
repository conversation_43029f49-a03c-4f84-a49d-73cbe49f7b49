import { Logger } from '@nestjs/common';

export const sleep = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const MAX_RETRIES = 3;
export const BASE_DELAY = 1000 * 5; // 5 seconds

export const retryWithExponentialBackoff = async <T>(
  operation: () => Promise<T>,
  context: string,
  {
    maxRetries = MAX_RETRIES,
    baseDelay = BASE_DELAY,
    logger = new Logger('Retry'),
  }: {
    maxRetries?: number;
    baseDelay?: number;
    logger?: Logger;
  } = {},
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        logger.error(
          `${context} failed after ${maxRetries + 1} attempts. Final error:`,
          lastError.message,
        );
        throw lastError;
      }

      const delay = baseDelay * Math.pow(2, attempt);
      logger.warn(
        `${context} attempt ${attempt + 1} failed: ${lastError.message}. Retrying in ${delay}ms...`,
      );

      await sleep(delay);
    }
  }

  throw lastError!;
};
