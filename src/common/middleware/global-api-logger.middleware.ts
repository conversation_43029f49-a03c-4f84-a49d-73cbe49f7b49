import { getCircularReplacer } from '@app/common/utils/json';
import { getIpAddress, getUserAgent } from '@app/common/utils/request';
import { Logger } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import * as httpContext from 'express-http-context';
import { v7 as uuidv7 } from 'uuid';

const logger = new Logger('LoggerRequest');
declare module 'express' {
  interface Request {
    id: string;
    user?: {
      sub: string;
    };
  }
}
export const GlobalApiLoggerMiddleware = (
  request: Request,
  response: Response,
  next: NextFunction,
) => {
  const { method, originalUrl: url } = request;
  if (!process.env.NODE_ENV || url === '/api/v1/health') {
    return next();
  }
  request.id = uuidv7();
  const ipAddress = getIpAddress(request);
  const userAgent = getUserAgent(request);
  const userId = request.user?.sub;
  httpContext.set('requestId', request.id);
  httpContext.set('originalUrl', request['originalUrl']);
  httpContext.set('userAgent', userAgent);
  httpContext.set('userId', userId);
  httpContext.set('httpMethod', method);
  const startHrTime = process.hrtime();
  let responseData = '';
  const logData = () => {
    const { statusCode } = response as any;
    const elapsedHrTime = process.hrtime(startHrTime);
    const elapsedTimeInMs = elapsedHrTime[0] * 1000 + elapsedHrTime[1] / 1e6;
    const data = {
      ip: ipAddress,
      method,
      url,
      requestId: request.id,
      status: statusCode,
      userAgent,
      responseTime: `${elapsedTimeInMs}ms`,
      userId: userId,
      responseData: statusCode >= 400 ? responseData : undefined,
      type: 'applicationLog',
      body: request?.body,
      params: request?.params,
      requestHeaders: request?.headers,
    };
    logger.log(JSON.stringify(data, getCircularReplacer()));
  };
  const originalSendFunc = response.send.bind(response);
  response.send = function (body) {
    responseData = body;
    return originalSendFunc(body);
  };
  response.on('finish', logData);
  next();
};
