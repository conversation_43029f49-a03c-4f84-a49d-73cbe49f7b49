import { MasterSeeder } from '@app/database/seeds/master.seeder';
import { databaseConfig } from '@app/modules/database/database.config';
import { DataSource } from 'typeorm';

async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  const options = args.slice(1);

  // Create database connection
  const dataSource = new DataSource(databaseConfig() as any);

  try {
    await dataSource.initialize();
    console.log('📡 Database connection established');

    const masterSeeder = new MasterSeeder(dataSource);

    switch (command) {
      case 'seed':
        await handleSeedCommand(masterSeeder, options);
        break;

      case 'list':
        await handleListCommand(masterSeeder, options);
        break;

      case 'status':
        masterSeeder.showSeedersStatus();
        break;

      case 'help':
      case '--help':
      case '-h':
        showHelp();
        break;

      default:
        console.error(`❌ Unknown command: ${command}`);
        showHelp();
        process.exit(1);
    }
  } catch (error) {
    console.error(
      '❌ Error:',
      error instanceof Error ? error.message : String(error),
    );
    process.exit(1);
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

async function handleSeedCommand(
  masterSeeder: MasterSeeder,
  options: string[],
): Promise<void> {
  const isForce = options.includes('--force') || options.includes('-f');
  const seederName = options.find(
    (opt) => !opt.startsWith('--') && !opt.startsWith('-'),
  );

  if (seederName && seederName.toLowerCase() != 'all') {
    // Seed specific seeder
    console.log(`🎯 Seeding specific table: ${seederName}`);
    if (isForce) {
      console.log('🔄 Force mode enabled - existing data will be cleared');
    }
    await masterSeeder.seedSpecific(seederName, isForce);
  } else {
    // Seed all
    console.log('🚀 Seeding all tables');
    if (isForce) {
      console.log('🔄 Force mode enabled - existing data will be cleared');
    }
    await masterSeeder.seedAll(isForce);
  }
}

async function handleListCommand(
  masterSeeder: MasterSeeder,
  options: string[],
): Promise<void> {
  const seederName = options.find(
    (opt) => !opt.startsWith('--') && !opt.startsWith('-'),
  );

  if (seederName) {
    // List specific seeder data
    await masterSeeder.listSpecific(seederName);
  } else {
    // List all seeded data
    await masterSeeder.listAll();
  }
}

function showHelp(): void {
  console.log(`
🌱 Database Seeding CLI

USAGE:
  npm run seed <command> [options] [seeder]

COMMANDS:
  seed [seeder]     Seed database table(s)
  list [seeder]     List seeded data
  status            Show available seeders status
  help              Show this help message

OPTIONS:
  --force, -f       Force reseed (clear existing data)

EXAMPLES:
  npm run seed                    # Seed all tables
  npm run seed --force            # Force seed all tables
  npm run seed ai-models          # Seed only AI models
  npm run seed users --force      # Force seed only users
  npm run seed list               # List all seeded data
  npm run seed list ai-models     # List AI models data
  npm run seed status             # Show available seeders

AVAILABLE SEEDERS:
  • ai-models      - AI model configurations

NOTES:
  • Seeders run in dependency order (users first, then ai-models)
  • Use --force to clear existing data before seeding
  • Normal seed operations skip if data already exists
  • Each seeder can be run independently

For more information, check: src/database/seeds/README.md
`);
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.error('💥 Unhandled Rejection:', error);
  process.exit(1);
});

main().catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
