---
trigger: always_on
alwaysApply: true
---

Please follow the commit message base on rule below:

# Don't Miss Out! The "Power" Prefix Table Changes How You Manage Your Codebase!

## Structure: `<type>(<scope>): <short description>`

| Prefix            | Meaning                                                                                                  | Commit Example                                                                                                                                                                             |
| :---------------- | :------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Add               | Add new features, APIs, tests, files                                                                     | feat(user): add registration endpoint                                                                                                                                                    |
| Cut               | Delete old features, unused files                                                                        | chore(payment): remove deprecated payment method                                                                                                                                        |
| Update            | Upgrade logic, improve existing code that doesn't change functionality                                   | feat(validation): update validation logic for user input                                                                                                                                |
| Enhance           | Improve features, upgrade UX/UI, expand functionality                                                  | feat(search): enhance search feature with keyword highlight                                                                                                                              |
| Improve (synonym) | Optimize and increase accuracy or performance                                                            | perf(checkout): improve checkout flow performance                                                                                                                                        |
| Fix               | Fix bugs, logic errors, UI errors                                                                        | fix(login): correct error message on failure                                                                                                                                            |
| Refactor          | If you only restructure without changing behavior                                                        | refactor(report): Refactor ReportService to use new utility class                                                                                                                        |
|                   | Apply SOLID principles                                                                                   | refactor(user): apply Single Responsibility Principle to UserService                                                                                                                     |
|                   | Apply design patterns                                                                                    | refactor(order): implement Strategy pattern for discount calculation                                                                                                                     |
|                   | Separate functions to KISS (Keep It Simple, Stupid)                                                      | refactor(report): split long method into smaller reusable methods                                                                                                                        |
|                   | Remove duplicate code (DRY - Don't Repeat Yourself)                                                      | refactor(common): extract shared logic into utility class                                                                                                                                |
|                   | Rename variables for clarity                                                                             | refactor(product): rename ambiguous variables for clarity                                                                                                                                |
| Optimize          | If you update to increase performance or reduce complexity                                               | perf(image): optimize image resize logic in upload service                                                                                                                              |
| Bump              | Update package/module version                                                                            | chore(deps): bump spring-boot to 3.1.0                                                                                                                                                   |
| Make              | Change config, Docker, infra                                                                             | build(docker): switch to multi-stage Docker build                                                                                                                                        |
| Start             | Initialize new features                                                                                  | feat(dark-mode): initial implementation                                                                                                                                                  |
| Stop              | Stop using features or APIs                                                                              | refactor(auth): remove legacy email login                                                                                                                                                |
| Style             | Adjust format, whitespace, prettier, remove redundant comments                                           | style(codebase): apply Prettier formatting                                                                                                                                               |
| Document          | Update README, comment code                                                                              | docs(readme): update API usage instructions                                                                                                                                              |
| Test              | Write or update test cases                                                                               | test(report): add unit tests for summary API                                                                                                                                             |
| Build             | Change build system (Docker, Webpack, Maven...)                                                          | build(maven): add build profile for staging                                                                                                                                              |
| CI                | Configure CI/CD, GitHub Actions, GitLab CI                                                               | ci(gitlab): add deploy stage to pipeline                                                                                                                                                 |
| Chore             | Minor tasks that do not affect code logic                                                                | chore(env): rename .env.example                                                                                                                                                         |
| Revert            | Revert previous commit                                                                                   | revert: revert "feat(auth): add login feature"                                                                                                                                           |
| Merge             | Used when merging a branch into the main branch                                                          | merge(feature/login): integrate login module into develop                                                                                                                                |
| chore             | Used when you want to keep the commit log clean, without automatic changelog creation                    | chore: merge hotfix/validate-email into main                                                                                                                                             |
| release           | Used when merging to prepare for release or deployment                                                   | release: merge staging into main for deployment                                                                                                                                          |