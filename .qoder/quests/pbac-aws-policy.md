# Policy-Based Access Control (PBAC) Authorization Module Design

## Overview

This document outlines the design for a Policy-Based Access Control (PBAC) authorization module that extends the existing NestJS authentication system with AWS IAM policy-style authorization. The module provides fine-grained access control using JSON-based policies that define permissions for resources and actions.

## Architecture

### System Integration

The PBAC module integrates seamlessly with the existing authentication infrastructure:

```mermaid
graph TB
    A[Client Request] --> B[JwtAuthGuard]
    B --> C[PbacGuard]
    C --> D[PolicyEvaluationService]
    D --> E[PolicyEngine]
    E --> F[Resource Handler]
    F --> G[Controller Method]
    
    H[PolicyRepository] --> D
    I[ResourceRegistry] --> D
    J[ConditionEvaluator] --> E
    
    subgraph "Existing Auth System"
        B
        K[UserRoleEnum]
        L[AuthService]
    end
    
    subgraph "PBAC Module"
        C
        D
        E
        F
        H
        I
        J
    end
```

### Core Components

#### 1. Role-Based Policy Management System

```mermaid
classDiagram
    class RoleEntity {
        +id: string
        +name: string
        +description: string
        +type: RoleType
        +isBuiltIn: boolean
        +isActive: boolean
        +permissions: string[]
        +createdAt: Date
        +updatedAt: Date
    }
    
    class PolicyEntity {
        +id: string
        +name: string
        +description: string
        +version: string
        +effect: PolicyEffect
        +actions: string[]
        +resources: string[]
        +conditions: PolicyCondition[]
        +principals: string[]
        +isActive: boolean
        +createdAt: Date
        +updatedAt: Date
    }
    
    class UserRoleEntity {
        +userId: string
        +roleId: string
        +assignedAt: Date
        +assignedBy: string
        +expiresAt: Date
    }
    
    class RolePolicyEntity {
        +roleId: string
        +policyId: string
        +attachedAt: Date
        +attachedBy: string
    }
    
    class RoleType {
        <<enumeration>>
        SYSTEM
        CUSTOM
        TEMPORARY
    }
    
    UserRoleEntity ||--|| RoleEntity
    RolePolicyEntity ||--|| RoleEntity
    RolePolicyEntity ||--|| PolicyEntity
```

#### 2. Policy Evaluation Engine

```mermaid
sequenceDiagram
    participant Guard as PbacGuard
    participant Evaluator as PolicyEvaluationService
    participant Engine as PolicyEngine
    participant Repository as PolicyRepository
    participant Condition as ConditionEvaluator
    
    Guard->>Evaluator: evaluateAccess(context, user)
    Evaluator->>Repository: findApplicablePolicies(user, resource)
    Repository-->>Evaluator: policies[]
    
    loop For each policy
        Evaluator->>Engine: evaluatePolicy(policy, context)
        Engine->>Condition: evaluateConditions(conditions, context)
        Condition-->>Engine: conditionResult
        Engine-->>Evaluator: policyResult
    end
    
    Evaluator->>Evaluator: combineResults(results[])
    Evaluator-->>Guard: AccessDecision
```

### Data Models

#### Policy Entity Structure

```typescript
interface PolicyDocument {
  version: '2023-11-01';
  statement: PolicyStatement[];
}

interface PolicyStatement {
  sid?: string;
  effect: 'Allow' | 'Deny';
  principal?: string | string[];
  action: string | string[];
  resource: string | string[];
  condition?: PolicyConditions;
}

interface PolicyConditions {
  [operator: string]: {
    [key: string]: string | string[] | number | number[] | boolean;
  };
}
```

#### Resource Identification

```mermaid
graph LR
    A[Resource ARN] --> B[Service]
    A --> C[Resource Type]
    A --> D[Resource ID]
    A --> E[Sub-resource]
    
    F["arn:app:user:*:user/123"] --> G[app]
    F --> H[user]
    F --> I[*]
    F --> J[user/123]
```

## API Endpoints Reference

### Role Management API

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/roles` | Create new role | Admin |
| GET | `/api/v1/roles` | List roles | Admin |
| GET | `/api/v1/roles/:id` | Get role details | Admin |
| PUT | `/api/v1/roles/:id` | Update role | Admin |
| DELETE | `/api/v1/roles/:id` | Delete role | Admin |
| POST | `/api/v1/roles/:id/users` | Assign role to user | Admin |
| DELETE | `/api/v1/roles/:id/users/:userId` | Remove role from user | Admin |
| POST | `/api/v1/roles/:id/policies` | Attach policy to role | Admin |
| DELETE | `/api/v1/roles/:id/policies/:policyId` | Detach policy from role | Admin |
| GET | `/api/v1/roles/:id/permissions` | Get effective permissions | Admin |

### Policy Management API

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/policies` | Create new policy | Admin |
| GET | `/api/v1/policies` | List policies | Admin |
| GET | `/api/v1/policies/:id` | Get policy details | Admin |
| PUT | `/api/v1/policies/:id` | Update policy | Admin |
| DELETE | `/api/v1/policies/:id` | Delete policy | Admin |
| POST | `/api/v1/policies/:id/attach` | Attach policy to user directly | Admin |
| POST | `/api/v1/policies/:id/detach` | Detach policy from user | Admin |

### User Management API

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/users/:id/roles` | Get user roles | Admin |
| GET | `/api/v1/users/:id/permissions` | Get effective user permissions | Admin |
| GET | `/api/v1/users/:id/policies` | Get user policies (direct + role-based) | Admin |

### Policy Evaluation API

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/policies/evaluate` | Evaluate access request | Authenticated |
| GET | `/api/v1/policies/simulate` | Simulate policy evaluation | Admin |

### Request/Response Schema

#### Create Role Request
```typescript
interface CreateRoleRequest {
  name: string;
  description?: string;
  type: 'SYSTEM' | 'CUSTOM' | 'TEMPORARY';
  permissions?: string[];
  isActive?: boolean;
}
```

#### Assign Role Request
```typescript
interface AssignRoleRequest {
  userId: string;
  roleId: string;
  expiresAt?: Date;
}
```

#### Create Policy Request
```typescript
interface CreatePolicyRequest {
  name: string;
  description?: string;
  document: PolicyDocument;
  isActive?: boolean;
}
```

#### Policy Evaluation Request
```typescript
interface EvaluateAccessRequest {
  action: string;
  resource: string;
  context?: {
    ipAddress?: string;
    userAgent?: string;
    time?: string;
    [key: string]: any;
  };
}
```

## Business Logic Layer

### Role-Based Policy Evaluation Algorithm

```mermaid
flowchart TD
    A[Start Evaluation] --> B[Get User Roles]
    B --> C[Get Role Policies]
    C --> D[Get Direct User Policies]
    D --> E[Merge All Policies]
    E --> F[Filter by Resource & Action]
    F --> G[Sort by Priority: Deny > Allow]
    G --> H[Evaluate First Policy]
    
    H --> I{Policy Effect}
    I -->|Deny| J[Check Conditions]
    I -->|Allow| K[Check Conditions]
    
    J --> L{Conditions Met?}
    K --> M{Conditions Met?}
    
    L -->|Yes| N[DENY ACCESS]
    L -->|No| O[Next Policy]
    
    M -->|Yes| P[Continue to Next]
    M -->|No| O
    
    O --> Q{More Policies?}
    Q -->|Yes| H
    Q -->|No| R[Check Role Permissions]
    
    R --> S{Has Required Permission?}
    S -->|Yes| T[ALLOW ACCESS]
    S -->|No| U[Default Deny]
    
    P --> V{More Policies?}
    V -->|Yes| H
    V -->|No| T
    
    N --> W[End]
    U --> W
    T --> W
```

### Resource Registry

```typescript
interface ResourceDefinition {
  type: string;
  pattern: string;
  actions: string[];
  attributes: ResourceAttribute[];
}

interface ResourceAttribute {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date';
  description: string;
  required: boolean;
}
```

### Condition Evaluation

```mermaid
classDiagram
    class ConditionEvaluator {
        +evaluateStringEquals(value, expected): boolean
        +evaluateStringLike(value, pattern): boolean
        +evaluateNumericComparison(value, expected, operator): boolean
        +evaluateDateComparison(value, expected, operator): boolean
        +evaluateIpAddress(clientIp, cidrBlock): boolean
        +evaluateArrayContains(array, value): boolean
        +evaluateBool(value, expected): boolean
    }
    
    class ContextExtractor {
        +extractUserContext(user): UserContext
        +extractRequestContext(request): RequestContext
        +extractResourceContext(resource): ResourceContext
    }
    
    class PolicyEngine {
        -conditionEvaluator: ConditionEvaluator
        -contextExtractor: ContextExtractor
        +evaluatePolicy(policy, context): PolicyResult
        +combineResults(results): AccessDecision
    }
```

## Middleware & Interceptors

### PBAC Guard Implementation

```typescript
@Injectable()
export class PbacGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private policyEvaluationService: PolicyEvaluationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Skip PBAC for public routes
    const isPublic = this.reflector.get('isPublic', context.getHandler());
    if (isPublic) return true;

    // Get required action and resource from metadata
    const requiredAction = this.reflector.get('pbac:action', context.getHandler());
    const resourcePattern = this.reflector.get('pbac:resource', context.getHandler());
    
    if (!requiredAction || !resourcePattern) {
      // Fallback to role-based access if no PBAC metadata
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    const accessContext = {
      user,
      action: requiredAction,
      resource: this.buildResourceArn(resourcePattern, request),
      context: this.extractRequestContext(request),
    };

    const decision = await this.policyEvaluationService.evaluateAccess(accessContext);
    return decision.allowed;
  }
}
```

### PBAC Decorators

```typescript
// Action decorator
export const PbacAction = (action: string) => SetMetadata('pbac:action', action);

// Resource decorator
export const PbacResource = (pattern: string) => SetMetadata('pbac:resource', pattern);

// Role decorator
export const RequireRole = (role: string) => SetMetadata('pbac:role', role);

// Permission decorator
export const RequirePermission = (permission: string) => SetMetadata('pbac:permission', permission);

// Combined decorators
export const RequirePolicyAccess = (action: string, resource: string) => 
  applyDecorators(
    PbacAction(action),
    PbacResource(resource),
    UseGuards(JwtAuthGuard, PbacGuard)
  );

export const RequireRoleAccess = (role: string) =>
  applyDecorators(
    RequireRole(role),
    UseGuards(JwtAuthGuard, PbacGuard)
  );

export const RequirePermissionAccess = (permission: string) =>
  applyDecorators(
    RequirePermission(permission),
    UseGuards(JwtAuthGuard, PbacGuard)
  );

// Multiple requirements
export const RequireRoleAndPermission = (role: string, permission: string) =>
  applyDecorators(
    RequireRole(role),
    RequirePermission(permission),
    UseGuards(JwtAuthGuard, PbacGuard)
  );
```

## Testing Strategy

### Unit Testing

```mermaid
graph TB
    A[Policy Engine Tests] --> B[Condition Evaluation]
    A --> C[Policy Document Parsing]
    A --> D[Access Decision Logic]
    
    E[Service Tests] --> F[Policy CRUD Operations]
    E --> G[Policy Evaluation]
    E --> H[Resource Registration]
    
    I[Guard Tests] --> J[Route Protection]
    I --> K[Metadata Extraction]
    I --> L[Error Handling]
```

### Integration Testing

```typescript
describe('PBAC Integration', () => {
  describe('Policy Evaluation', () => {
    it('should allow access with valid policy');
    it('should deny access without policy');
    it('should deny access with explicit deny policy');
    it('should evaluate conditions correctly');
  });

  describe('Resource Protection', () => {
    it('should protect controller methods');
    it('should extract resource ARN from request');
    it('should handle dynamic resource IDs');
  });
});
```

### Test Data Examples

```typescript
const testPolicies = {
  userReadOwn: {
    version: '2023-11-01',
    statement: [{
      effect: 'Allow',
      action: ['user:read'],
      resource: 'arn:app:user:*:user/${user.id}',
      condition: {
        StringEquals: {
          'user:id': '${aws:userid}'
        }
      }
    }]
  },
  adminFullAccess: {
    version: '2023-11-01',
    statement: [{
      effect: 'Allow',
      action: ['*'],
      resource: ['*']
    }]
  }
};
```

## Usage Examples

### Basic Controller Protection

```typescript
@Controller('users')
@UseGuards(JwtAuthGuard, PbacGuard)
export class UserController {
  
  @Get(':id')
  @RequirePermission('user:read', 'arn:app:user:*:user/${params.id}')
  async getUser(@Param('id') id: string) {
    return this.userService.findById(id);
  }

  @Put(':id')
  @RequirePermission('user:update', 'arn:app:user:*:user/${params.id}')
  async updateUser(@Param('id') id: string, @Body() data: UpdateUserDto) {
    return this.userService.update(id, data);
  }

  @Delete(':id')
  @RequirePermission('user:delete', 'arn:app:user:*:user/${params.id}')
  async deleteUser(@Param('id') id: string) {
    return this.userService.delete(id);
  }
}
```

### Administrative Operations

```typescript
@Controller('admin/policies')
@UseGuards(JwtAuthGuard, PbacGuard)
export class PolicyController {
  
  @Post()
  @RequirePermission('policy:create', 'arn:app:iam:*:policy/*')
  async createPolicy(@Body() data: CreatePolicyDto) {
    return this.policyService.create(data);
  }

  @Get()
  @RequirePermission('policy:list', 'arn:app:iam:*:policy/*')
  async listPolicies(@Query() query: ListPoliciesDto) {
    return this.policyService.findAll(query);
  }
}
```

### Custom Resource Handlers

```typescript
@Injectable()
export class DocumentResourceHandler implements ResourceHandler {
  getResourceArn(request: any, pattern: string): string {
    const documentId = request.params.documentId;
    const ownerId = request.params.ownerId;
    
    return pattern
      .replace('${params.documentId}', documentId)
      .replace('${params.ownerId}', ownerId);
  }

  extractAttributes(request: any): Record<string, any> {
    return {
      'document:owner': request.params.ownerId,
      'document:type': request.body?.type,
      'request:time': new Date().toISOString(),
    };
  }
}
```

### Role Management Service

```typescript
@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(RoleEntity)
    private roleRepository: Repository<RoleEntity>,
    @InjectRepository(UserRoleEntity)
    private userRoleRepository: Repository<UserRoleEntity>,
  ) {}

  async createRole(data: CreateRoleDto): Promise<RoleEntity> {
    const role = this.roleRepository.create({
      ...data,
      isBuiltIn: false,
      isActive: true,
    });
    return this.roleRepository.save(role);
  }

  async assignRoleToUser(roleId: string, userId: string, expiresAt?: Date): Promise<void> {
    await this.userRoleRepository.save({
      userId,
      roleId,
      assignedAt: new Date(),
      expiresAt,
    });
  }

  async getUserRoles(userId: string): Promise<RoleEntity[]> {
    const userRoles = await this.userRoleRepository.find({
      where: { userId },
      relations: ['role'],
    });
    return userRoles.map(ur => ur.role).filter(role => role.isActive);
  }

  async userHasRole(userId: string, roleName: string): Promise<boolean> {
    const roles = await this.getUserRoles(userId);
    return roles.some(role => role.name === roleName);
  }

  async userHasPermission(userId: string, permission: string): Promise<boolean> {
    const roles = await this.getUserRoles(userId);
    return roles.some(role => 
      role.permissions && role.permissions.includes(permission)
    );
  }
}
```

### Policy Management Service

```typescript
@Injectable()
export class PolicyManagementService {
  async attachPolicyToUser(userId: string, policyId: string): Promise<void> {
    const policy = await this.policyRepository.findOne({
      where: { id: policyId, isActive: true }
    });
    
    if (!policy) {
      throw new NotFoundException('Policy not found');
    }

    await this.userPolicyRepository.save({
      userId,
      policyId,
      attachedAt: new Date(),
    });
  }

  async evaluateUserAccess(
    userId: string, 
    action: string, 
    resource: string,
    context: Record<string, any> = {}
  ): Promise<AccessDecision> {
    const userPolicies = await this.getUserPolicies(userId);
    const applicablePolicies = this.filterApplicablePolicies(
      userPolicies, 
      action, 
      resource
    );

    return this.policyEngine.evaluate(applicablePolicies, {
      action,
      resource,
      user: { id: userId },
      ...context
    });
  }
}
```

## Configuration & Environment

### Module Configuration

```typescript
interface PbacConfig {
  defaultEffect: 'allow' | 'deny';
  enableAuditLogging: boolean;
  cacheExpiration: number;
  maxPolicySize: number;
  evaluationTimeout: number;
}

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [pbacConfig],
    }),
    TypeOrmModule.forFeature([
      PolicyEntity,
      UserPolicyEntity,
      ResourceDefinitionEntity,
    ]),
    CacheModule.register(),
  ],
  controllers: [PolicyController],
  providers: [
    PolicyService,
    PolicyEvaluationService,
    PolicyEngine,
    ConditionEvaluator,
    PbacGuard,
  ],
  exports: [PolicyService, PolicyEvaluationService, PbacGuard],
})
export class PbacModule {}
```

### Environment Variables

```bash
# PBAC Configuration
PBAC_DEFAULT_EFFECT=deny
PBAC_ENABLE_AUDIT_LOGGING=true
PBAC_CACHE_EXPIRATION=300
PBAC_MAX_POLICY_SIZE=10240
PBAC_EVALUATION_TIMEOUT=5000

# Database
PBAC_DB_CACHE_POLICIES=true
PBAC_DB_CACHE_TTL=600
```

This PBAC module design provides enterprise-grade authorization capabilities that integrate seamlessly with the existing NestJS authentication system, offering fine-grained access control similar to AWS IAM policies while maintaining the modular architecture of the boilerplate project.