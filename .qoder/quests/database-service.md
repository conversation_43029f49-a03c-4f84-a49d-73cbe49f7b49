# Database Service Design: Archive and Backup Operations

## Overview

This design document outlines the implementation of a `DatabaseService` within the existing `DatabaseModule` that provides two primary methods for data management: archive data operations and backup operations. The service will integrate with the current TypeORM-based architecture to handle data archiving based on time ranges and full table backups.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Database Module"
        DM[DatabaseModule]
        DS[DatabaseService]
    end
    
    subgraph "Configuration"
        DC[DatabaseConfig]
    end
    
    subgraph "Storage"
        PG[(PostgreSQL)]
    end
    
    DM --> DS
    DS --> DC
    DS --> PG
```

### Component Architecture

```mermaid
classDiagram
    class DatabaseService {
        +archiveData(tableName, options): ArchiveResult
        +backupTable(tableName): BackupResult
        -generateTimestamp(): string
        -executeQuery(query, parameters): Promise<any>
    }
    
    class ArchiveOptions {
        +timeRange: TimeRangeOptions
        +dateColumn: 'createdAt' | 'updatedAt'
    }
    
    class TimeRangeOptions {
        +startDate: Date
        +endDate: Date
    }
    
    DatabaseService --> ArchiveOptions
    ArchiveOptions --> TimeRangeOptions
```

## Data Models & Interfaces

### Archive Options Interface

```typescript
interface ArchiveOptions {
  timeRange: {
    startDate: Date;
    endDate: Date;
  };
  dateColumn: 'createdAt' | 'updatedAt';
}

interface ArchiveResult {
  originalTableName: string;
  archiveTableName: string;
  archivedRecordCount: number;
  timestamp: Date;
}

interface BackupResult {
  originalTableName: string;
  backupTableName: string;
  totalRecordCount: number;
  timestamp: Date;
}
```

## Business Logic Layer

### Database Service Implementation

```typescript
@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService
  ) {}

  /**
   * Archive data from a table based on time range criteria
   * Archive table format: [original_table_name]_archive_[YYYY_MM_DD_HH_mm_ss]
   */
  async archiveData(
    tableName: string,
    options: ArchiveOptions
  ): Promise<ArchiveResult> {
    const timestamp = this.generateTimestamp();
    const archiveTableName = `${tableName}_archive_${timestamp}`;
    
    return this.dataSource.transaction(async (manager) => {
      try {
        // Create archive table with same structure as original
        await manager.query(`
          CREATE TABLE ${archiveTableName} AS 
          SELECT * FROM ${tableName} WHERE 1=0
        `);
        
        // Move data based on time range criteria
        const whereClause = this.buildTimeRangeWhereClause(options);
        const result = await manager.query(`
          WITH archived_data AS (
            DELETE FROM ${tableName}
            WHERE ${whereClause}
            RETURNING *
          )
          INSERT INTO ${archiveTableName}
          SELECT * FROM archived_data
        `, [options.timeRange.startDate, options.timeRange.endDate]);
        
        this.logger.log(
          `Archived ${result.length} records from ${tableName} to ${archiveTableName}`
        );
        
        return {
          originalTableName: tableName,
          archiveTableName,
          archivedRecordCount: result.length,
          timestamp: new Date()
        };
        
      } catch (error) {
        this.logger.error(
          `Failed to archive data from ${tableName}: ${error.message}`
        );
        throw new HttpErrorException(
          DATABASE_ERROR_CODES.ARCHIVE_OPERATION_FAILED,
          { description: `Archive operation failed: ${error.message}` }
        );
      }
    });
  }

  /**
   * Backup all data from a table
   * Backup table format: [original_table_name]_bkp_[YYYY_MM_DD_HH_mm_ss]
   */
  async backupTable(tableName: string): Promise<BackupResult> {
    const timestamp = this.generateTimestamp();
    const backupTableName = `${tableName}_bkp_${timestamp}`;
    
    try {
      // Create backup table with all data
      await this.dataSource.query(`
        CREATE TABLE ${backupTableName} AS 
        SELECT * FROM ${tableName}
      `);
      
      // Get record count
      const countResult = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM ${backupTableName}
      `);
      const recordCount = parseInt(countResult[0].count);
      
      this.logger.log(
        `Created backup of ${tableName} with ${recordCount} records as ${backupTableName}`
      );
      
      return {
        originalTableName: tableName,
        backupTableName,
        totalRecordCount: recordCount,
        timestamp: new Date()
      };
      
    } catch (error) {
      this.logger.error(
        `Failed to backup table ${tableName}: ${error.message}`
      );
      throw new HttpErrorException(
        DATABASE_ERROR_CODES.BACKUP_OPERATION_FAILED,
        { description: `Backup operation failed: ${error.message}` }
      );
    }
  }

  /**
   * Generate timestamp in format YYYY_MM_DD_HH_mm_ss
   */
  private generateTimestamp(): string {
    const now = new Date();
    return [
      now.getFullYear(),
      String(now.getMonth() + 1).padStart(2, '0'),
      String(now.getDate()).padStart(2, '0'),
      String(now.getHours()).padStart(2, '0'),
      String(now.getMinutes()).padStart(2, '0'),
      String(now.getSeconds()).padStart(2, '0')
    ].join('_');
  }

  /**
   * Build WHERE clause for time range filtering
   */
  private buildTimeRangeWhereClause(options: ArchiveOptions): string {
    const { dateColumn, timeRange } = options;
    return `${dateColumn} >= $1 AND ${dateColumn} <= $2`;
  }
}
```

### Updated Database Module

```typescript
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [databaseConfig],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (
        configService: ConfigService<{
          database: TypeOrmModuleOptions;
        }>,
      ) => {
        return configService.getOrThrow('database', {
          infer: true,
        });
      },
    }),
  ],
  providers: [DatabaseService],
  exports: [TypeOrmModule, DatabaseService],
})
export class DatabaseModule {}
```

## Usage Examples

### Using DatabaseService in Other Services

```typescript
@Injectable()
export class UserMaintenanceService {
  constructor(
    private readonly databaseService: DatabaseService
  ) {}

  /**
   * Archive old user records
   */
  async archiveOldUsers(): Promise<ArchiveResult> {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const today = new Date();
    
    return this.databaseService.archiveData('users', {
      timeRange: {
        startDate: new Date('2020-01-01'), // Archive from beginning
        endDate: sixMonthsAgo
      },
      dateColumn: 'updatedAt'
    });
  }

  /**
   * Create backup before major updates
   */
  async createUserBackupBeforeUpdate(): Promise<BackupResult> {
    return this.databaseService.backupTable('users');
  }
}
```

### Using DatabaseService in Scheduled Tasks

```typescript
@Injectable()
export class DatabaseMaintenanceService {
  constructor(
    private readonly databaseService: DatabaseService
  ) {}

  @Cron('0 2 * * 0') // Every Sunday at 2 AM
  async weeklyArchiveCleanup() {
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    
    const tables = ['audit_logs', 'tracking_events', 'user_sessions'];
    
    for (const table of tables) {
      try {
        await this.databaseService.archiveData(table, {
          timeRange: {
            startDate: new Date('2020-01-01'),
            endDate: oneYearAgo
          },
          dateColumn: 'createdAt'
        });
      } catch (error) {
        console.error(`Failed to archive ${table}:`, error);
      }
    }
  }

  @Cron('0 3 * * 1') // Every Monday at 3 AM
  async weeklyBackup() {
    const criticalTables = ['users', 'app_settings', 'two_factor_auth'];
    
    for (const table of criticalTables) {
      try {
        await this.databaseService.backupTable(table);
      } catch (error) {
        console.error(`Failed to backup ${table}:`, error);
      }
    }
  }
}
```

## Testing Strategy

### Unit Testing

```typescript
describe('DatabaseService', () => {
  let service: DatabaseService;
  let dataSource: DataSource;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DatabaseService,
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<DatabaseService>(DatabaseService);
    dataSource = module.get<DataSource>(DataSource);
  });

  describe('archiveData', () => {
    it('should archive data successfully', async () => {
      const tableName = 'test_users';
      const options = {
        timeRange: {
          startDate: new Date('2023-01-01'),
          endDate: new Date('2023-12-31')
        },
        dateColumn: 'createdAt' as const
      };

      const result = await service.archiveData(tableName, options);
      
      expect(result.originalTableName).toBe(tableName);
      expect(result.archiveTableName).toMatch(/test_users_archive_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}/);
      expect(result.archivedRecordCount).toBeGreaterThanOrEqual(0);
    });

    it('should handle archive errors gracefully', async () => {
      const tableName = 'non_existent_table';
      const options = {
        timeRange: {
          startDate: new Date('2023-01-01'),
          endDate: new Date('2023-12-31')
        },
        dateColumn: 'createdAt' as const
      };

      await expect(service.archiveData(tableName, options))
        .rejects.toThrow(HttpErrorException);
    });
  });

  describe('backupTable', () => {
    it('should backup table successfully', async () => {
      const tableName = 'test_users';

      const result = await service.backupTable(tableName);
      
      expect(result.originalTableName).toBe(tableName);
      expect(result.backupTableName).toMatch(/test_users_bkp_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}/);
      expect(result.totalRecordCount).toBeGreaterThanOrEqual(0);
    });

    it('should handle backup errors gracefully', async () => {
      const tableName = 'non_existent_table';

      await expect(service.backupTable(tableName))
        .rejects.toThrow(HttpErrorException);
    });
  });

  describe('generateTimestamp', () => {
    it('should generate valid timestamp format', () => {
      const timestamp = service['generateTimestamp']();
      
      expect(timestamp).toMatch(/^\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}$/);
    });
  });
});
```