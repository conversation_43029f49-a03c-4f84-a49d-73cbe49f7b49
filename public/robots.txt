# robots.txt for NestJS Boilerplate Application
# This file tells search engine crawlers which parts of your site they can access

# Allow all crawlers to access most of the site
User-agent: *

# Disallow access to private/admin areas
Disallow: /admin/
Disallow: /api/
Disallow: /auth/
Disallow: /private/

# Disallow access to configuration and system files
Disallow: /.env
Disallow: /config/
Disallow: /database/
Disallow: /migrations/

# Allow access to public assets
Allow: /public/
Allow: /assets/
Allow: /images/
Allow: /css/
Allow: /js/

# Crawl delay (optional - tells crawlers to wait between requests)
Crawl-delay: 1