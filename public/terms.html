<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - NestJS Boilerplate</title>
    <meta name="description" content="Terms of service for NestJS Boilerplate application.">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-800">NestJS Boilerplate</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">Home</a>
                    <a href="/about.html" class="text-gray-600 hover:text-gray-900">About</a>
                    <a href="/privacy.html" class="text-gray-600 hover:text-gray-900">Privacy</a>
                    <a href="/terms.html" class="text-gray-600 hover:text-gray-900 font-semibold">Terms</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h1 class="text-3xl font-bold text-gray-900">Terms of Service</h1>
                <p class="mt-2 text-sm text-gray-600">Last updated: January 2024</p>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div class="prose max-w-none">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Acceptance of Terms</h2>
                    <p class="text-gray-700 mb-4">
                        By accessing and using this application, you accept and agree to be bound by the terms 
                        and provision of this agreement.
                    </p>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Use License</h2>
                    <p class="text-gray-700 mb-4">
                        Permission is granted to temporarily download one copy of the materials (information or software) 
                        on NestJS Boilerplate's website for personal, non-commercial transitory viewing only.
                    </p>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">This is the grant of a license, not a transfer of title, and under this license you may not:</h3>
                    <ul class="list-disc pl-6 text-gray-700 mb-4">
                        <li>Modify or copy the materials</li>
                        <li>Use the materials for any commercial purpose or for any public display</li>
                        <li>Attempt to reverse engineer any software contained on the website</li>
                        <li>Remove any copyright or other proprietary notations from the materials</li>
                    </ul>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Disclaimer</h2>
                    <p class="text-gray-700 mb-4">
                        The materials on NestJS Boilerplate's website are provided on an 'as is' basis. 
                        NestJS Boilerplate makes no warranties, expressed or implied, and hereby disclaims 
                        and negates all other warranties including without limitation, implied warranties 
                        or conditions of merchantability, fitness for a particular purpose, or non-infringement 
                        of intellectual property or other violation of rights.
                    </p>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Limitations</h2>
                    <p class="text-gray-700 mb-4">
                        In no event shall NestJS Boilerplate or its suppliers be liable for any damages 
                        (including, without limitation, damages for loss of data or profit, or due to business interruption) 
                        arising out of the use or inability to use the materials on the website.
                    </p>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Revisions and Errata</h2>
                    <p class="text-gray-700 mb-4">
                        The materials appearing on NestJS Boilerplate's website could include technical, 
                        typographical, or photographic errors. NestJS Boilerplate does not warrant that 
                        any of the materials on its website are accurate, complete or current.
                    </p>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Links</h2>
                    <p class="text-gray-700 mb-4">
                        NestJS Boilerplate has not reviewed all of the sites linked to its website and 
                        is not responsible for the contents of any such linked site. The inclusion of 
                        any link does not imply endorsement by NestJS Boilerplate of the site.
                    </p>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Contact Information</h2>
                    <p class="text-gray-700 mb-4">
                        If you have any questions about these Terms of Service, please contact us at 
                        <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-500"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-gray-800">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Product</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="/about.html" class="text-base text-gray-300 hover:text-white">About</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Documentation</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Legal</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="/privacy.html" class="text-base text-gray-300 hover:text-white">Privacy</a></li>
                        <li><a href="/terms.html" class="text-base text-gray-300 hover:text-white font-semibold">Terms</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-700 pt-8">
                <p class="text-base text-gray-400 text-center">
                    &copy; 2024 NestJS Boilerplate. All rights reserved.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
