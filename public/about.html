<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - NestJS Boilerplate</title>
    <meta name="description" content="Learn more about NestJS Boilerplate and its features.">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-800">NestJS Boilerplate</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">Home</a>
                    <a href="/about.html" class="text-gray-600 hover:text-gray-900 font-semibold">About</a>
                    <a href="/privacy.html" class="text-gray-600 hover:text-gray-900">Privacy</a>
                    <a href="/terms.html" class="text-gray-600 hover:text-gray-900">Terms</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h1 class="text-4xl font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                About NestJS Boilerplate
            </h1>
            <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                A comprehensive, production-ready boilerplate for building scalable Node.js applications with NestJS.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">What is NestJS?</h2>
            <p class="text-gray-700 text-lg leading-relaxed mb-4">
                NestJS is a progressive Node.js framework for building efficient, reliable, and scalable server-side applications. 
                It uses modern JavaScript, is built with TypeScript, and combines elements of OOP (Object Oriented Programming), 
                FP (Functional Programming), and FRP (Functional Reactive Programming).
            </p>
            <p class="text-gray-700 text-lg leading-relaxed">
                Our boilerplate takes this powerful framework and adds all the essential features you need to get started 
                building production applications immediately.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Authentication System</h3>
                <p class="text-gray-600">Complete JWT authentication with refresh tokens, OAuth social login, passkey support, and two-factor authentication.</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Storage Solutions</h3>
                <p class="text-gray-600">Multiple storage providers including local file system, AWS S3, and Cloudinary with unified interface.</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM4 13h6V7H4v6zM10 7h6V1h-6v6z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Alert System</h3>
                <p class="text-gray-600">Multi-channel notifications via Discord, Slack, Telegram, and email with configurable templates.</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Analytics & Tracking</h3>
                <p class="text-gray-600">User session tracking, event analytics, and performance monitoring built-in.</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">WebSocket Support</h3>
                <p class="text-gray-600">Real-time communication with Socket.IO integration and Redis adapter for scaling.</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Database & Migrations</h3>
                <p class="text-gray-600">TypeORM integration with automatic migrations, seeding, and database configuration.</p>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">Technology Stack</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold text-gray-900">Backend</h4>
                    <p class="text-gray-600">NestJS, Node.js</p>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold text-gray-900">Language</h4>
                    <p class="text-gray-600">TypeScript</p>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold text-gray-900">Database</h4>
                    <p class="text-gray-600">TypeORM, PostgreSQL</p>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold text-gray-900">Cache</h4>
                    <p class="text-gray-600">Redis</p>
                </div>
            </div>
        </div>

        <div class="bg-indigo-50 rounded-lg p-8 text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
            <p class="text-gray-700 text-lg mb-6">
                Clone the repository and start building your next application in minutes.
            </p>
            <a href="https://github.com/your-repo/nestjs-boilerplate" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                View on GitHub
            </a>
        </div>
    </div>

    <footer class="bg-gray-800">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Product</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="/about.html" class="text-base text-gray-300 hover:text-white font-semibold">About</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Documentation</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Legal</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="/privacy.html" class="text-base text-gray-300 hover:text-white">Privacy</a></li>
                        <li><a href="/terms.html" class="text-base text-gray-300 hover:text-white">Terms</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-700 pt-8">
                <p class="text-base text-gray-400 text-center">
                    &copy; 2024 NestJS Boilerplate. All rights reserved.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
