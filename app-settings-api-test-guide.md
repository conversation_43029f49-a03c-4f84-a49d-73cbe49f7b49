# App Settings Module API Testing Guide

This guide provides comprehensive curl commands to test all features of the app-settings module.

## Prerequisites

1. **Start the application** and ensure it's running on `http://localhost:3014`
2. **Run database migrations** to create the tables:
   ```bash
   npm run migration:run
   ```
3. **Get an authentication token** by logging in through the auth endpoints
4. **Set environment variables** for the app-settings module:
   ```bash
   export APP_SETTINGS_ENCRYPTION_KEY="your-64-character-encryption-key-here"
   export APP_SETTINGS_AUDIT_ENABLED=true
   export APP_SETTINGS_VALIDATION_ENABLED=true
   ```

## Authentication Setup

First, get your JWT token by logging in:

```bash
# Login to get JWT token
curl -X POST http://localhost:3014/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

**Save the token from the response and use it in the `Authorization` header for all subsequent requests.**

Replace `YOUR_JWT_TOKEN` in the examples below with your actual token.

---

## 1. Get All Settings

### Basic request
```bash
curl -X GET "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### With pagination
```bash
curl -X GET "http://localhost:3014/v1/settings?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### With category filter
```bash
curl -X GET "http://localhost:3014/v1/settings?category=application" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### With search
```bash
curl -X GET "http://localhost:3014/v1/settings?search=app" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Filter system settings only
```bash
curl -X GET "http://localhost:3014/v1/settings?isSystem=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Filter encrypted settings only
```bash
curl -X GET "http://localhost:3014/v1/settings?isEncrypted=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Combined filters
```bash
curl -X GET "http://localhost:3014/v1/settings?category=security&isSystem=true&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 2. Get Specific Setting by Key

### Get system setting
```bash
curl -X GET "http://localhost:3014/v1/settings/app.name" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Get security setting
```bash
curl -X GET "http://localhost:3014/v1/settings/security.session_timeout" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Test non-existent setting (should return 404)
```bash
curl -X GET "http://localhost:3014/v1/settings/non.existent.key" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 3. Get Settings by Category

### Get application settings
```bash
curl -X GET "http://localhost:3014/v1/settings/category/application" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Get security settings with pagination
```bash
curl -X GET "http://localhost:3014/v1/settings/category/security?page=1&limit=3" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Get features settings
```bash
curl -X GET "http://localhost:3014/v1/settings/category/features" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 4. Create New Custom Settings

### Create basic custom setting
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "user.theme.primary_color",
    "value": "#3498db",
    "description": "Primary color for user theme",
    "category": "theme"
  }'
```

### Create encrypted setting
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "user.api.secret_key",
    "value": "super-secret-api-key-12345",
    "description": "Secret API key for external service",
    "category": "api",
    "isEncrypted": true
  }'
```

### Create setting with validation schema
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "user.profile.max_file_size",
    "value": "5242880",
    "description": "Maximum file upload size in bytes",
    "category": "profile",
    "validationSchema": {
      "type": "number",
      "minimum": 1024,
      "maximum": 10485760
    }
  }'
```

### Create setting with complex validation
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "user.notification.email_frequency",
    "value": "daily",
    "description": "Email notification frequency",
    "category": "notifications",
    "validationSchema": {
      "type": "string",
      "enum": ["immediate", "hourly", "daily", "weekly", "never"]
    }
  }'
```

### Test duplicate key creation (should return 409)
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "user.theme.primary_color",
    "value": "#e74c3c",
    "description": "Duplicate key test"
  }'
```

---

## 5. Update Settings (PUT - Full Update)

### Update custom setting value
```bash
curl -X PUT "http://localhost:3014/v1/settings/user.theme.primary_color" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "#e74c3c",
    "description": "Updated primary color - now red",
    "category": "theme"
  }'
```

### Update with encryption
```bash
curl -X PUT "http://localhost:3014/v1/settings/user.api.secret_key" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "new-super-secret-key-67890",
    "description": "Updated secret API key",
    "isEncrypted": true
  }'
```

### Test updating system setting (should be restricted)
```bash
curl -X PUT "http://localhost:3014/v1/settings/app.name" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "Modified App Name"
  }'
```

---

## 6. Update Settings (PATCH - Partial Update)

### Update only value
```bash
curl -X PATCH "http://localhost:3014/v1/settings/user.theme.primary_color" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "#9b59b6"
  }'
```

### Update only description
```bash
curl -X PATCH "http://localhost:3014/v1/settings/user.profile.max_file_size" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Maximum file upload size - updated description"
  }'
```

### Update category
```bash
curl -X PATCH "http://localhost:3014/v1/settings/user.notification.email_frequency" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "category": "user_preferences"
  }'
```

---

## 7. Delete Settings

### Delete custom setting
```bash
curl -X DELETE "http://localhost:3014/v1/settings/user.theme.primary_color" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Test deleting system setting (should return 403)
```bash
curl -X DELETE "http://localhost:3014/v1/settings/app.name" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Test deleting non-existent setting (should return 404)
```bash
curl -X DELETE "http://localhost:3014/v1/settings/non.existent.setting" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 8. Search Settings

### Search by pattern
```bash
curl -X GET "http://localhost:3014/v1/settings/search/theme" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Search with category filter
```bash
curl -X GET "http://localhost:3014/v1/settings/search/app?category=application" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Search with pagination
```bash
curl -X GET "http://localhost:3014/v1/settings/search/security?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 9. Get System Settings Only

### All system settings
```bash
curl -X GET "http://localhost:3014/v1/settings/system/all" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### System settings by category
```bash
curl -X GET "http://localhost:3014/v1/settings/system/all?category=security" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### System settings with pagination
```bash
curl -X GET "http://localhost:3014/v1/settings/system/all?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 10. Get Custom Settings Only

### All custom settings
```bash
curl -X GET "http://localhost:3014/v1/settings/custom/all" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Custom settings by category
```bash
curl -X GET "http://localhost:3014/v1/settings/custom/all?category=theme" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 11. Get Encrypted Settings Only

### All encrypted settings
```bash
curl -X GET "http://localhost:3014/v1/settings/encrypted/all" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Encrypted settings by category
```bash
curl -X GET "http://localhost:3014/v1/settings/encrypted/all?category=api" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 12. Error Testing

### Test invalid authentication
```bash
curl -X GET "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer invalid-token" \
  -H "Content-Type: application/json"
```

### Test missing authentication
```bash
curl -X GET "http://localhost:3014/v1/settings" \
  -H "Content-Type: application/json"
```

### Test invalid setting key format
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "invalid key with spaces!",
    "value": "test"
  }'
```

### Test empty value
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "test.empty.value",
    "value": ""
  }'
```

---

## 13. Validation Testing

Create a setting with validation and test valid/invalid values:

### Create setting with number validation
```bash
curl -X POST "http://localhost:3014/v1/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "test.number.setting",
    "value": "50",
    "description": "Test number validation",
    "validationSchema": {
      "type": "number",
      "minimum": 1,
      "maximum": 100
    }
  }'
```

### Test valid update
```bash
curl -X PATCH "http://localhost:3014/v1/settings/test.number.setting" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "75"
  }'
```

### Test invalid update (should fail validation)
```bash
curl -X PATCH "http://localhost:3014/v1/settings/test.number.setting" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "150"
  }'
```

---

## Expected Response Formats

### Successful Setting Response
```json
{
  "id": "uuid-here",
  "key": "user.theme.primary_color",
  "value": "#3498db",
  "description": "Primary color for user theme",
  "category": "theme",
  "isSystem": false,
  "isEncrypted": false,
  "validationSchema": null,
  "createdBy": "<EMAIL>",
  "updatedBy": null,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### List Response with Pagination
```json
{
  "data": [
    {
      "id": "uuid-here",
      "key": "setting.key",
      "value": "setting value",
      // ... other fields
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### Error Response
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": {
    "field": "key",
    "message": "Key already exists"
  }
}
```

---

## Testing Checklist

- [ ] Authentication works correctly
- [ ] Can retrieve all settings with pagination
- [ ] Can filter by category, system/custom, encrypted status
- [ ] Can search settings by key pattern
- [ ] Can create new custom settings
- [ ] Can update existing custom settings (PUT and PATCH)
- [ ] Cannot modify system settings
- [ ] Can delete custom settings only
- [ ] Cannot delete system settings
- [ ] Validation schemas work correctly
- [ ] Encryption/decryption works for sensitive settings
- [ ] Error handling works for invalid requests
- [ ] Audit logging captures all changes (check database)

---

## Notes

1. **System Settings**: Created by migrations, cannot be deleted or modified via API
2. **Custom Settings**: Created by users, can be modified and deleted
3. **Encryption**: Sensitive values are automatically encrypted/decrypted
4. **Validation**: Settings can have JSON schemas for value validation
5. **Audit Trail**: All changes are logged in the `setting_audits` table
6. **Pagination**: All list endpoints support pagination with `page` and `limit` parameters