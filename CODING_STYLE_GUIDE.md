# NestJS Boilerplate - Coding Style Guide

## Table of Contents
- [General Principles](#general-principles)
- [File Organization](#file-organization)
- [Naming Conventions](#naming-conventions)
- [TypeScript & Code Standards](#typescript--code-standards)
- [Import/Export Patterns](#importexport-patterns)
- [Entity & Database Conventions](#entity--database-conventions)
- [Service Layer Patterns](#service-layer-patterns)
- [Controller Patterns](#controller-patterns)
- [DTO Patterns](#dto-patterns)
- [Error Handling](#error-handling)
- [Documentation Standards](#documentation-standards)
- [Module Organization](#module-organization)
- [Configuration Management](#configuration-management)
- [Testing Standards](#testing-standards)

## General Principles

### 1. Type Safety First
- Use strict TypeScript settings with null checks enabled
- Prefer explicit types over `any` (configured to warn, not error)
- Use interfaces for contracts and type definitions
- Leverage TypeScript's inference where appropriate

### 2. Consistency Over Preferences
- Follow established patterns throughout the codebase
- Maintain consistent naming conventions across all modules
- Use shared utilities and base classes when applicable

### 3. Modularity & Separation of Concerns
- Each module should have a single responsibility
- Use dependency injection for loose coupling
- Separate business logic from presentation layer

## File Organization

### Directory Structure Pattern
```
src/modules/{module-name}/
├── controllers/           # HTTP request handlers
├── services/             # Business logic
├── dto/                  # Data Transfer Objects
├── entities/             # Database entities
├── interfaces/           # Type definitions
├── enums/               # Enumeration types
├── guards/              # Authentication/Authorization guards
├── decorators/          # Custom decorators
├── utils/               # Module-specific utilities
├── test/                # Module-specific tests
├── {module}.module.ts   # Module definition
├── {module}.config.ts   # Module configuration
├── {module}.error-codes.ts  # Error code definitions
└── index.ts            # Public API exports
```

### File Naming Conventions
- **Modules**: `kebab-case` (e.g., `app-settings.module.ts`)
- **Services**: `kebab-case.service.ts` (e.g., `user-login.service.ts`)
- **Controllers**: `kebab-case.controller.ts` (e.g., `auth.controller.ts`)
- **DTOs**: `kebab-case.dto.ts` or `kebab-case.input.ts`
- **Entities**: `kebab-case.entity.ts` (e.g., `user-login.entity.ts`)
- **Interfaces**: `kebab-case.interface.ts`
- **Enums**: `kebab-case.enum.ts`
- **Configs**: `kebab-case.config.ts`
- **Error Codes**: `kebab-case.error-codes.ts`

## Naming Conventions

### Classes
- **PascalCase** for all class names
- **Descriptive suffixes** indicating purpose:
  ```typescript
  // Services
  export class AppSettingsService {}
  export class UserLoginService {}
  
  // Controllers
  export class AuthController {}
  
  // DTOs
  export class LoginDto {}
  export class RegisterInput {}
  
  // Entities
  export class AppSettingEntity {}
  export class UserEntity {}
  
  // Interfaces
  export interface IEncryptionService {}
  export interface ISetting {}
  
  // Enums
  export enum UserRoleEnum {}
  export enum SettingTypeEnum {}
  ```

### Variables & Methods
- **camelCase** for variables, methods, and properties
- **Descriptive and meaningful names**:
  ```typescript
  // Good
  const userAgent = getUserAgent(req);
  const ipAddress = getIpAddress(req);
  async findByKey(key: string): Promise<ISetting | null>
  
  // Avoid generic names
  const data = getData(); // Too generic
  ```

### Constants
- **SCREAMING_SNAKE_CASE** for constants:
  ```typescript
  const AUTH_NAMESPACE = 'AUTH';
  
  export const AUTH_ERROR_CODES: Record<AuthErrorCodes, ErrorCode> = {
    INVALID_CREDENTIALS: {
      code: `${AUTH_NAMESPACE}:10000`,
      statusCode: HttpStatus.UNAUTHORIZED,
      message: 'Invalid credentials'
    }
  };
  ```

### Interface Naming
- Prefix with `I` for service interfaces: `IEncryptionService`
- No prefix for data structures: `CreateSetting`, `SettingFilter`
- Use descriptive names: `ISettingMetadata`, `IEncryptedValue`

## TypeScript & Code Standards

### Import Organization
Follow this order with blank lines between groups:
```typescript
// 1. Node modules
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

// 2. Internal app imports (using path aliases)
import { HttpErrorException } from '@common/exception/http-error.exception';
import { APP_SETTINGS_ERROR_CODES } from '@modules/app-settings/app-settings.error-codes';

// 3. Local relative imports
import { AppSettingEntity } from '../entities/app-setting.entity';
import { ICreateSetting, ISetting } from '../interfaces/setting.interface';
```

### Path Aliases Usage
Use configured path aliases consistently:
```typescript
// Preferred
import { HttpErrorException } from '@common/exception/http-error.exception';
import { UserEntity } from '@modules/user/entities/user.entity';

// Avoid deep relative paths
import { HttpErrorException } from '../../../common/exception/http-error.exception';
```

### Decorator Usage
- Use appropriate NestJS decorators with clear configuration
- Place decorators immediately above the target
- **STRICT ORDER REQUIREMENT** - Follow the exact decorator order specified below

#### Controller Decorator Order (Top to Bottom):
1. `@ApiOperation()` - Endpoint summary and description
2. `@ApiParam()` - Path parameters
3. `@ApiQuery()` - Query parameters
4. `@ApiBody()` - Request body specification
5. `@ApiResponse()` - All response scenarios
6. Custom Decorators (e.g., `@Public()`, `@User()`)
7. `@UseGuards()` - Authentication/authorization guards
8. `@HttpCode()` - HTTP status code override
9. HTTP Method (`@Post()`, `@Get()`, `@Put()`, `@Delete()`, etc.)

#### DTO Decorator Order (Top to Bottom):
1. `@ApiProperty()` - Swagger documentation
2. Validation decorators (Input DTOs only: `@IsString()`, `@IsNotEmpty()`, etc.)
3. Transform decorators (if applicable: `@Transform()`, `@Type()`)
4. `@Expose()` - Class-transformer exposure

#### Controller Example with Correct Order:
```typescript
@ApiOperation({ 
  summary: 'Create a new application setting',
  description: 'Creates a new setting with validation and encryption support'
})
@ApiParam({ 
  name: 'id', 
  description: 'Setting unique identifier',
  type: 'string'
})
@ApiQuery({ 
  name: 'includeSystem', 
  required: false, 
  type: 'boolean',
  description: 'Include system settings in response'
})
@ApiBody({ 
  type: CreateSettingDto,
  description: 'Setting creation data'
})
@ApiResponse({
  status: HttpStatus.CREATED,
  description: 'Setting created successfully',
  type: CreateSettingResponseDto,
})
@ApiResponse({
  status: HttpStatus.CONFLICT,
  description: 'Setting key already exists',
})
@ApiResponse({
  status: HttpStatus.BAD_REQUEST,
  description: 'Validation failed or invalid input',
})
@Public()
@UseGuards(JwtAuthGuard)
@HttpCode(HttpStatus.CREATED)
@Post(':id/settings')
async create(
  @Param('id') id: string,
  @Query('includeSystem') includeSystem: boolean,
  @Body() createData: CreateSettingDto
): Promise<CreateSettingResponseDto> {
  // implementation
}
```

## Import/Export Patterns

### Index Files
Create `index.ts` files to provide clean public APIs:
```typescript
// src/modules/app-settings/index.ts
export * from './app-settings.module';
export * from './services/app-settings.service';
export * from './interfaces/setting.interface';
export * from './app-settings.error-codes';
export * from './app-settings.config';
```

### Barrel Exports
Group related exports by category:
```typescript
// interfaces/index.ts
export * from './audit.interface';
export * from './config.interface';
export * from './encryption.interface';
export * from './setting.interface';
export * from './validation.interface';
```

## Entity & Database Conventions

### Base Entity Usage
Extend from `CustomBaseEntity` for common fields:
```typescript
import { CustomBaseEntity } from '@common/entities/base.entity';
import { Column, Entity, Index, ManyToOne, OneToMany, JoinColumn } from 'typeorm';

@Entity('app_settings')
export class AppSettingEntity extends CustomBaseEntity {
  @Column({ unique: true })
  key: string;

  @Column({ type: 'text' })
  value: string;

  @Column({ nullable: true })
  description?: string;
}
```

### Foreign Key Relationships Pattern
**IMPORTANT**: Always use `createForeignKeyConstraints: false` for relations and create manual indexes instead.

#### Many-to-One Relationships
```typescript
@Entity('user_sessions')
@Index(['user_id']) // Manual index for foreign key column
export class UserSessionEntity extends CustomBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => UserEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'session_token' })
  sessionToken: string;

  @Column({ name: 'expires_at' })
  expiresAt: Date;
}
```

#### One-to-Many Relationships
```typescript
@Entity('users')
export class UserEntity extends CustomBaseEntity {
  @Column({ unique: true })
  email: string;

  @OneToMany(() => UserSessionEntity, session => session.user, {
    createForeignKeyConstraints: false
  })
  sessions: UserSessionEntity[];

  @OneToMany(() => UserLoginEntity, login => login.user, {
    createForeignKeyConstraints: false
  })
  logins: UserLoginEntity[];
}
```

#### Complex Relationships with Multiple Foreign Keys
```typescript
@Entity('user_role_assignments')
@Index(['user_id']) // Index for user foreign key
@Index(['role_id']) // Index for role foreign key
@Index(['user_id', 'role_id'], { unique: true }) // Composite unique index
export class UserRoleAssignmentEntity extends CustomBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'role_id' })
  roleId: string;

  @ManyToOne(() => UserEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => RoleEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'role_id' })
  role: RoleEntity;

  @Column({ name: 'assigned_at', default: () => 'CURRENT_TIMESTAMP' })
  assignedAt: Date;

  @Column({ name: 'assigned_by' })
  assignedBy: string;
}
```

### Index Strategy Guidelines

#### Single Column Indexes
```typescript
// For foreign key columns
@Index(['user_id'])
@Index(['category_id'])
@Index(['created_by'])

// For frequently queried columns
@Index(['status'])
@Index(['type'])
@Index(['is_active'])
```

#### Composite Indexes
```typescript
// For unique combinations
@Index(['user_id', 'setting_key'], { unique: true })
@Index(['tenant_id', 'email'], { unique: true })

// For query optimization
@Index(['status', 'created_at'])
@Index(['type', 'is_active', 'updated_at'])
```

#### Conditional Indexes
```typescript
// For soft-deleted entities
@Index(['deleted_at'], { where: 'deleted_at IS NULL' })

// For status-based queries
@Index(['is_active'], { where: 'is_active = true' })
```

### Benefits of This Pattern

1. **Performance**: Faster queries without constraint checking overhead
2. **Flexibility**: Easier data migrations and bulk operations
3. **Scalability**: Better performance in high-transaction environments
4. **Control**: Manual index optimization for specific query patterns
5. **Maintenance**: Easier to handle data inconsistencies during migrations

### Column Naming
- Use `snake_case` for database column names
- Specify column names explicitly in decorators:
```typescript
@CreateDateColumn({ name: 'created_at' })
createdAt: Date;

@UpdateDateColumn({ name: 'updated_at' })
updatedAt: Date;

@Column({ name: 'user_id' })
userId: string;

@Column({ name: 'foreign_entity_id' })
foreignEntityId: string;
```

### UUID Usage
Use UUID v7 for primary keys (configured in base entity):
```typescript
@PrimaryGeneratedColumn('uuid', { name: 'id' })
id = uuidv7();
```

## Service Layer Patterns

### Service Structure
```typescript
@Injectable()
export class AppSettingsService {
  private readonly logger = new Logger(AppSettingsService.name);

  constructor(
    @InjectRepository(AppSettingEntity)
    private readonly settingRepository: Repository<AppSettingEntity>,
    private readonly encryptionService: EncryptionService,
    private readonly validationService: SettingValidationService,
  ) {}

  /**
   * Method documentation with JSDoc
   * @param createData - Setting creation data
   * @returns Promise<ISetting> - The created setting
   */
  async create(createData: ICreateSetting): Promise<ISetting> {
    try {
      // Implementation with proper error handling
    } catch (error) {
      if (error instanceof HttpErrorException) {
        throw error;
      }
      
      this.logger.error(`Failed to create setting: ${createData.key}`, error);
      throw new HttpErrorException(APP_SETTINGS_ERROR_CODES.DATABASE_ERROR, {
        description: `Failed to create setting '${createData.key}'`,
      });
    }
  }
}
```

### Method Naming Patterns
- `findByKey()`, `findById()`, `findMany()` for queries
- `create()`, `update()`, `delete()` for mutations
- `validate...()` for validation methods
- `encrypt()`, `decrypt()` for transformation methods

## Controller Patterns

### Controller Structure
```typescript
@ApiTags('Authentication')
@Controller({ path: 'auth', version: '1' })
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({ summary: 'Register new user' })
  @ApiBody({ type: RegisterInput })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User registered successfully',
    type: RegisterInput,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email already exists',
  })
  @Public()
  @HttpCode(HttpStatus.CREATED)
  @Post('register')
  async register(@Body() registerDto: RegisterInput, @Req() req: Request) {
    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);
    return await this.authService.register(registerDto, userAgent, ipAddress);
  }
}
```

### HTTP Status Codes
Use appropriate HTTP status codes:
- `201` for resource creation
- `200` for successful operations
- `204` for successful operations with no content
- `400` for validation errors
- `401` for authentication errors
- `403` for authorization errors
- `404` for resource not found
- `409` for conflicts

## DTO Patterns

### DTO Structure
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

// Input DTO Example with Correct Decorator Order
export class CreateSettingDto {
  @ApiProperty({ 
    description: 'Unique setting key identifier',
    example: 'app.theme.primary_color',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @Expose()
  key: string;

  @ApiProperty({ 
    description: 'Setting value (can be string, number, boolean, or object)',
    example: '#007bff'
  })
  @IsNotEmpty()
  @Transform(({ value }) => value?.toString()?.trim())
  @Expose()
  value: any;

  @ApiProperty({ 
    description: 'Optional description of the setting purpose',
    example: 'Primary theme color for the application UI',
    required: false
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  @Expose()
  description?: string;
}

// Response DTO Example with Correct Decorator Order
export class CreateSettingResponseDto {
  @ApiProperty({ 
    description: 'Unique setting identifier',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @Expose()
  id: string;

  @ApiProperty({ 
    description: 'Setting key',
    example: 'app.theme.primary_color'
  })
  @Type(() => String)
  @Expose()
  key: string;

  @ApiProperty({ 
    description: 'Setting creation timestamp',
    example: '2023-12-01T10:30:00Z'
  })
  @Type(() => Date)
  @Expose()
  createdAt: Date;
}
```

### DTO Inheritance
Use inheritance for related DTOs:
```typescript
export class TokenDto {
  @ApiProperty({ description: 'Access token' })
  @Expose()
  accessToken: string;
}

export class LoginDto extends TokenDto {
  @ApiProperty({ description: 'User profile', type: ProfileDto })
  @Type(() => ProfileDto)
  @Expose()
  user: ProfileDto;
}
```

## Error Handling

### Error Code Structure
```typescript
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/types';

const APP_SETTINGS_NAMESPACE = 'APP_SETTINGS';

type AppSettingsErrorCodes =
  | 'SETTING_KEY_ALREADY_EXISTS'
  | 'VALIDATION_FAILED'
  | 'DATABASE_ERROR';

export const APP_SETTINGS_ERROR_CODES: Record<AppSettingsErrorCodes, ErrorCode> = {
  SETTING_KEY_ALREADY_EXISTS: {
    code: `${APP_SETTINGS_NAMESPACE}:10000`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Setting key already exists'
  },
  VALIDATION_FAILED: {
    code: `${APP_SETTINGS_NAMESPACE}:10001`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Setting value validation failed'
  },
  DATABASE_ERROR: {
    code: `${APP_SETTINGS_NAMESPACE}:10002`,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Database operation failed'
  }
};
```

### Exception Usage
```typescript
throw new HttpErrorException(
  APP_SETTINGS_ERROR_CODES.SETTING_KEY_ALREADY_EXISTS,
  {
    description: `Setting with key '${createData.key}' already exists`,
  },
);
```

## Documentation Standards

### JSDoc Comments
Document all public methods with JSDoc:
```typescript
/**
 * Create a new setting
 * @param createData - Setting creation data
 * @returns Promise<ISetting> - The created setting
 * @throws HttpErrorException - When validation fails or key already exists
 */
async create(createData: ICreateSetting): Promise<ISetting> {
  // implementation
}
```

### Swagger Documentation for Controllers
Use comprehensive Swagger decorators for all API endpoints:
```typescript
@ApiTags('Settings')
@Controller({ path: 'settings', version: '1' })
export class SettingsController {
  @ApiOperation({ 
    summary: 'Create a new application setting',
    description: 'Creates a new setting with validation and encryption support'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Setting created successfully',
    type: CreateSettingResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Setting key already exists',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Validation failed or invalid input',
  })
  @ApiBody({ 
    type: CreateSettingDto,
    description: 'Setting creation data'
  })
  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Body() createData: CreateSettingDto): Promise<CreateSettingResponseDto> {
    // implementation
  }
}
```

### Swagger Documentation for DTOs
All DTOs (Input, Response, Update) must include comprehensive Swagger decorators:

#### Input DTOs
```typescript
export class CreateSettingDto {
  @ApiProperty({ 
    description: 'Unique setting key identifier',
    example: 'app.theme.primary_color',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  @Expose()
  key: string;

  @ApiProperty({ 
    description: 'Setting value (can be string, number, boolean, or object)',
    example: '#007bff',
    oneOf: [
      { type: 'string' },
      { type: 'number' },
      { type: 'boolean' },
      { type: 'object' }
    ]
  })
  @IsNotEmpty()
  @Expose()
  value: any;

  @ApiProperty({ 
    description: 'Optional description of the setting purpose',
    example: 'Primary theme color for the application UI',
    required: false,
    maxLength: 500
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  @Expose()
  description?: string;

  @ApiProperty({ 
    description: 'Setting category for organization',
    example: 'theme',
    required: false
  })
  @IsString()
  @IsOptional()
  @Expose()
  category?: string;

  @ApiProperty({ 
    description: 'Whether the setting value should be encrypted',
    example: false,
    default: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  @Expose()
  isEncrypted?: boolean;
}
```

#### Response DTOs
```typescript
export class CreateSettingResponseDto {
  @ApiProperty({ 
    description: 'Unique setting identifier',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @Expose()
  id: string;

  @ApiProperty({ 
    description: 'Setting key',
    example: 'app.theme.primary_color'
  })
  @Expose()
  key: string;

  @ApiProperty({ 
    description: 'Setting value',
    example: '#007bff'
  })
  @Expose()
  value: any;

  @ApiProperty({ 
    description: 'Setting description',
    example: 'Primary theme color for the application UI',
    required: false
  })
  @Expose()
  description?: string;

  @ApiProperty({ 
    description: 'Setting category',
    example: 'theme',
    required: false
  })
  @Expose()
  category?: string;

  @ApiProperty({ 
    description: 'Whether the setting is a system setting',
    example: false
  })
  @Expose()
  isSystem: boolean;

  @ApiProperty({ 
    description: 'Whether the setting value is encrypted',
    example: false
  })
  @Expose()
  isEncrypted: boolean;

  @ApiProperty({ 
    description: 'Setting creation timestamp',
    example: '2023-12-01T10:30:00Z'
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({ 
    description: 'Setting last update timestamp',
    example: '2023-12-01T10:30:00Z'
  })
  @Expose()
  updatedAt: Date;
}
```

#### Update DTOs
```typescript
export class UpdateSettingDto {
  @ApiProperty({ 
    description: 'New setting value',
    example: '#28a745',
    required: false
  })
  @IsOptional()
  @Expose()
  value?: any;

  @ApiProperty({ 
    description: 'Updated setting description',
    example: 'Updated primary theme color',
    required: false,
    maxLength: 500
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  @Expose()
  description?: string;
}
```

### Required Swagger Documentation Elements

#### For Controllers:
- `@ApiTags()` - Group related endpoints
- `@ApiOperation()` - Endpoint summary and description
- `@ApiResponse()` - All possible HTTP responses (success and error)
- `@ApiBody()` - Request body description
- `@ApiParam()` - Path parameters
- `@ApiQuery()` - Query parameters
- `@ApiBearerAuth()` - For protected endpoints

#### For DTOs:
- `@ApiProperty()` - Every property with:
  - Descriptive text
  - Example values
  - Type information
  - Validation constraints
  - Whether required or optional
  - Enum values (if applicable)
  - Format specifications

### Swagger Best Practices
1. **Consistent Descriptions**: Use clear, professional language
2. **Realistic Examples**: Provide meaningful example values
3. **Complete Coverage**: Document all properties and endpoints
4. **Error Documentation**: Include all possible error responses
5. **Schema References**: Use DTO types in responses
6. **Validation Alignment**: Ensure Swagger docs match validation rules

## Module Organization

### Module Structure
```typescript
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [authConfig],
    }),
    TypeOrmModule.forFeature([SessionEntity, UserEntity]),
    PassportModule,
    UserModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, UserLoginService, JwtStrategy],
  exports: [AuthService, UserLoginService, JwtModule, PassportModule],
})
export class AuthModule {}
```

### Dynamic Module Pattern
For configurable modules:
```typescript
static register(options: StorageModuleOptions = {}): DynamicModule {
  return {
    global: options.isGlobal,
    module: StorageModule,
    imports: [...this.getImports(options)],
    providers: [...this.getProviders(options)],
    exports: this.getExports(options),
  };
}
```

## Configuration Management

### Config File Structure
```typescript
import { registerAs } from '@nestjs/config';
import { IsOptional, IsString, IsUrl } from 'class-validator';
import { validateConfig } from '@common/config/validate-config';

export interface AuthConfigType {
  accessToken: {
    secret: string;
    expiresIn: string;
    algorithm?: string;
  };
  refreshToken: {
    secret: string;
    expiresIn: string;
  };
}

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  JWT_ACCESS_TOKEN_SECRET: string;

  @IsString()
  @IsOptional()
  JWT_ACCESS_TOKEN_EXPIRES_IN: string;
}

export const authConfig = registerAs<AuthConfigType>('auth', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    accessToken: {
      secret: process.env.JWT_ACCESS_TOKEN_SECRET || 'secret',
      expiresIn: process.env.JWT_ACCESS_TOKEN_EXPIRES_IN || '15m',
    },
    refreshToken: {
      secret: process.env.JWT_REFRESH_TOKEN_SECRET || 'secret',
      expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES_IN || '7d',
    },
  };
});
```

## Testing Standards

### Test File Naming
- Unit tests: `{name}.spec.ts`
- Integration tests: `{name}.e2e-spec.ts`
- Place test files adjacent to source files or in dedicated test directories

### Test Structure
```typescript
describe('AppSettingsService', () => {
  let service: AppSettingsService;
  let repository: Repository<AppSettingEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppSettingsService,
        {
          provide: getRepositoryToken(AppSettingEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<AppSettingsService>(AppSettingsService);
    repository = module.get<Repository<AppSettingEntity>>(
      getRepositoryToken(AppSettingEntity),
    );
  });

  describe('create', () => {
    it('should create a new setting successfully', async () => {
      // Test implementation
    });

    it('should throw error when key already exists', async () => {
      // Test implementation
    });
  });
});
```

## Code Formatting & Linting

### Prettier Configuration
```json
{
  "singleQuote": true,
  "trailingComma": "all"
}
```

### ESLint Rules
Key rules from configuration:
- TypeScript strict type checking enabled
- Prettier integration for formatting
- No explicit `any` warnings (not errors)
- Floating promises as warnings
- Unsafe operations disabled for flexibility

## Best Practices Summary

1. **Always use TypeScript types and interfaces**
2. **Follow the established file and directory structure**
3. **Use descriptive naming conventions consistently**
4. **Document public APIs with JSDoc and Swagger**
5. **Handle errors gracefully with proper error codes**
6. **Use dependency injection for all services**
7. **Validate input data with class-validator decorators**
8. **Use path aliases to avoid deep relative imports**
9. **Create reusable base classes and utilities**
10. **Write comprehensive tests for all functionality**

This style guide should be followed for all code contributions to maintain consistency and quality throughout the boilerplate project.